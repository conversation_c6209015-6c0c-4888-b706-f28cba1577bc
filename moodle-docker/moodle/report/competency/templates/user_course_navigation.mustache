{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template report_competency/user_course_navigation

    Moodle navigation control allowing to jump to a user or filter to an activity.

    Example context (json):
    { "hasusers": false, "hasmodules": false}
}}
<div class="float-end border p-2 mb-2">
<p>{{{groupselector}}}</p>
<form class="user-competency-course-navigation">
<input type="hidden" name="user" value="{{userid}}"/>
<input type="hidden" name="id" value="{{courseid}}"/>
<input type="hidden" name="mod" value="{{moduleid}}"/>
{{#hasusers}}
<span>
<label for="user-nav-{{uniqid}}" class="accesshide">{{#str}}jumptouser, tool_lp{{/str}}</label>
<select id="user-nav-{{uniqid}}">
{{#users}}
<option value="{{id}}" {{#selected}}selected="selected"{{/selected}}>{{fullname}}</option>
{{/users}}
</select>
</span>
{{/hasusers}}

{{#hasmodules}}
<span>
<label for="module-nav-{{uniqid}}" class="accesshide">{{#str}}filterbyactivity, tool_lp{{/str}}</label>
<select id="module-nav-{{uniqid}}">
{{#modules}}
<option value="{{id}}" {{#selected}}selected="selected"{{/selected}}>{{name}}</option>
{{/modules}}
</select>
</span>
{{/hasmodules}}
</form>
</div>
{{#js}}
require(['core/form-autocomplete', 'report_competency/user_course_navigation'], function(autocomplete, nav) {
    (new nav('#user-nav-{{uniqid}}', '#module-nav-{{uniqid}}', '{{baseurl}}', {{userid}}, {{courseid}}, {{moduleid}}));
{{#hasusers}}
    autocomplete.enhance('#user-nav-{{uniqid}}', false, false, {{# quote }}{{# str }}jumptouser, tool_lp{{/ str }}{{/ quote }});
{{/hasusers}}
{{#hasmodules}}
    autocomplete.enhance('#module-nav-{{uniqid}}', false, false, {{# quote }}{{# str }}filterbyactivity, tool_lp{{/ str }}{{/ quote }});
{{/hasmodules}}
});
{{/js}}
