define("report_insights/actions",["exports","core/str","core/ajax","core/notification","core/url","core/modal_events","core/modal_save_cancel"],(function(_exports,_str,_ajax,_notification,_url,_modal_events,_modal_save_cancel){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Module to manage report insights actions that are executed using AJAX.
   *
   * @copyright  2017 <PERSON> {@link http://www.davidmonllao.com}
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.initBulk=void 0,_ajax=_interopRequireDefault(_ajax),_notification=_interopRequireDefault(_notification),_url=_interopRequireDefault(_url),_modal_events=_interopRequireDefault(_modal_events),_modal_save_cancel=_interopRequireDefault(_modal_save_cancel);const executeAction=(predictionIds,predictionContainers,actionName)=>{var predictionids,actionname;(predictionids=predictionIds,actionname=actionName,_ajax.default.call([{methodname:"report_insights_action_executed",args:{actionname:actionname,predictionids:predictionids}}])[0]).then((()=>{const tableNode=(predictionContainers=>{for(const el of predictionContainers)if(el.closest("table"))return el.closest("table");return null})(predictionContainers);if(predictionContainers.forEach((el=>el.remove())),!tableNode.querySelector("tbody > tr")){const params={contextid:tableNode.closest("div.insight-container").dataset.contextId,modelid:tableNode.closest("div.insight-container").dataset.modelId};window.location.assign(_url.default.relativeUrl("report/insights/insights.php",params,!1))}})).catch(_notification.default.exception)};_exports.initBulk=rootNode=>{document.addEventListener("click",(e=>{const action=e.target.closest("".concat(rootNode," [data-bulk-actionname]"));if(!action)return;e.preventDefault();const actionName=action.dataset.bulkActionname,actionVisibleName=action.textContent.trim(),predictionContainers=Array.from(document.querySelectorAll('.insights-list input[data-togglegroup^="insight-bulk-action-"][data-toggle="slave"]:checked')).map((checkbox=>checkbox.closest("tr[data-prediction-id]"))),predictionIds=predictionContainers.map((el=>el.dataset.predictionId));if(0===predictionIds.length)return;const stringParams={action:actionVisibleName,nitems:predictionIds.length};_modal_save_cancel.default.create({title:actionVisibleName,body:(0,_str.get_string)("confirmbulkaction","report_insights",stringParams),buttons:{save:(0,_str.get_string)("confirm")},show:!0}).then((modal=>(modal.getRoot().on(_modal_events.default.save,(function(){return executeAction(predictionIds,predictionContainers,actionName)})),modal))).catch(_notification.default.exception)}))}}));

//# sourceMappingURL=actions.min.js.map