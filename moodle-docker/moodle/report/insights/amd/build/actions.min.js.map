{"version": 3, "file": "actions.min.js", "sources": ["../src/actions.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to manage report insights actions that are executed using AJAX.\n *\n * @copyright  2017 <PERSON> {@link http://www.davidmonllao.com}\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\n/**\n * This module manages prediction actions that require AJAX requests.\n *\n * @module report_insights/actions\n */\n\nimport {get_string as getString} from 'core/str';\nimport Ajax from 'core/ajax';\nimport Notification from 'core/notification';\nimport Url from 'core/url';\nimport ModalEvents from 'core/modal_events';\nimport ModalSaveCancel from 'core/modal_save_cancel';\n\n\n/**\n * Executes the provided action.\n *\n * @param  {Array}  predictionids\n * @param  {String} actionname\n * @return {Promise}\n */\nconst markActionExecuted = (predictionids, actionname) => Ajax.call([\n    {\n        methodname: 'report_insights_action_executed',\n        args: {\n            actionname,\n            predictionids,\n        },\n    }\n])[0];\n\nconst getPredictionTable = (predictionContainers) => {\n    for (const el of predictionContainers) {\n        if (el.closest('table')) {\n            return el.closest('table');\n        }\n    }\n\n    return null;\n};\n\nconst executeAction = (predictionIds, predictionContainers, actionName) => {\n    markActionExecuted(predictionIds, actionName).then(() => {\n        // Remove the selected elements from the list.\n        const tableNode = getPredictionTable(predictionContainers);\n        predictionContainers.forEach((el) => el.remove());\n\n        if (!tableNode.querySelector('tbody > tr')) {\n            const params = {\n                contextid: tableNode.closest('div.insight-container').dataset.contextId,\n                modelid: tableNode.closest('div.insight-container').dataset.modelId,\n            };\n            window.location.assign(Url.relativeUrl(\"report/insights/insights.php\", params, false));\n        }\n        return;\n    }).catch(Notification.exception);\n};\n\n/**\n * Attach on click handlers for bulk actions.\n *\n * @param {String} rootNode\n * @access public\n */\nexport const initBulk = (rootNode) => {\n    document.addEventListener('click', (e) => {\n        const action = e.target.closest(`${rootNode} [data-bulk-actionname]`);\n        if (!action) {\n            return;\n        }\n\n        e.preventDefault();\n        const actionName = action.dataset.bulkActionname;\n        const actionVisibleName = action.textContent.trim();\n\n        const predictionContainers = Array.from(document.querySelectorAll(\n            '.insights-list input[data-togglegroup^=\"insight-bulk-action-\"][data-toggle=\"slave\"]:checked',\n        )).map((checkbox) => checkbox.closest('tr[data-prediction-id]'));\n        const predictionIds = predictionContainers.map((el) => el.dataset.predictionId);\n\n        if (predictionIds.length === 0) {\n            // No items selected message.\n            return;\n        }\n\n        const stringParams = {\n            action: actionVisibleName,\n            nitems: predictionIds.length,\n        };\n\n        ModalSaveCancel.create({\n            title: actionVisibleName,\n            body: getString('confirmbulkaction', 'report_insights', stringParams),\n            buttons: {\n                save: getString('confirm'),\n            },\n            show: true,\n        }).then((modal) => {\n            modal.getRoot().on(ModalEvents.save, function() {\n                // The action is now confirmed, sending an action for it.\n                return executeAction(predictionIds, predictionContainers, actionName);\n            });\n\n            return modal;\n        }).catch(Notification.exception);\n    });\n};\n"], "names": ["executeAction", "predictionIds", "predictionContainers", "actionName", "predictionids", "actionname", "Ajax", "call", "methodname", "args", "then", "tableNode", "el", "closest", "getPredictionTable", "for<PERSON>ach", "remove", "querySelector", "params", "contextid", "dataset", "contextId", "modelid", "modelId", "window", "location", "assign", "Url", "relativeUrl", "catch", "Notification", "exception", "rootNode", "document", "addEventListener", "e", "action", "target", "preventDefault", "bulkActionname", "actionVisibleName", "textContent", "trim", "Array", "from", "querySelectorAll", "map", "checkbox", "predictionId", "length", "stringParams", "nitems", "create", "title", "body", "buttons", "save", "show", "modal", "getRoot", "on", "ModalEvents"], "mappings": ";;;;;;wUA+DMA,cAAgB,CAACC,cAAeC,qBAAsBC,cApBjC,IAACC,cAAeC,YAAfD,cAqBLH,cArBoBI,WAqBLF,WArBoBG,cAAKC,KAAK,CAChE,CACIC,WAAY,kCACZC,KAAM,CACFJ,WAAAA,WACAD,cAAAA,kBAGT,IAa+CM,MAAK,WAEzCC,UAbcT,CAAAA,2BACnB,MAAMU,MAAMV,wBACTU,GAAGC,QAAQ,gBACJD,GAAGC,QAAQ,gBAInB,MAMeC,CAAmBZ,yBACrCA,qBAAqBa,SAASH,IAAOA,GAAGI,YAEnCL,UAAUM,cAAc,cAAe,OAClCC,OAAS,CACXC,UAAWR,UAAUE,QAAQ,yBAAyBO,QAAQC,UAC9DC,QAASX,UAAUE,QAAQ,yBAAyBO,QAAQG,SAEhEC,OAAOC,SAASC,OAAOC,aAAIC,YAAY,+BAAgCV,QAAQ,QAGpFW,MAAMC,sBAAaC,8BASDC,WACrBC,SAASC,iBAAiB,SAAUC,UAC1BC,OAASD,EAAEE,OAAOxB,kBAAWmB,yCAC9BI,cAILD,EAAEG,uBACInC,WAAaiC,OAAOhB,QAAQmB,eAC5BC,kBAAoBJ,OAAOK,YAAYC,OAEvCxC,qBAAuByC,MAAMC,KAAKX,SAASY,iBAC7C,gGACDC,KAAKC,UAAaA,SAASlC,QAAQ,4BAChCZ,cAAgBC,qBAAqB4C,KAAKlC,IAAOA,GAAGQ,QAAQ4B,kBAErC,IAAzB/C,cAAcgD,oBAKZC,aAAe,CACjBd,OAAQI,kBACRW,OAAQlD,cAAcgD,mCAGVG,OAAO,CACnBC,MAAOb,kBACPc,MAAM,mBAAU,oBAAqB,kBAAmBJ,cACxDK,QAAS,CACLC,MAAM,mBAAU,YAEpBC,MAAM,IACP/C,MAAMgD,QACLA,MAAMC,UAAUC,GAAGC,sBAAYL,MAAM,kBAE1BxD,cAAcC,cAAeC,qBAAsBC,eAGvDuD,SACR7B,MAAMC,sBAAaC"}