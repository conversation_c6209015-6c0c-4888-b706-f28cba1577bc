/**
 * Message users.
 *
 * @module     report_insights/message_users
 * @copyright  2019 <PERSON>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define("report_insights/message_users",["jquery","core/str","core/log","core/modal_save_cancel","core/modal_events","core/templates","core/notification","core/ajax"],(function($,Str,Log,ModalSaveCancel,ModalEvents,Templates,Notification,Ajax){var SELECTORS_BULKACTIONSELECT="#formactionid",MessageUsers=function(rootNode,actionName){this.actionName=actionName,this.attachEventListeners(rootNode)};return MessageUsers.prototype.actionName=null,MessageUsers.prototype.modal=null,MessageUsers.prototype.attachEventListeners=function(rootNode){$(rootNode+" button[data-bulk-sendmessage]").on("click",function(e){e.preventDefault();var cTarget=$(e.currentTarget),users={},predictionToUserMapping=cTarget.data("prediction-to-user-id");return $('.insights-list input[data-togglegroup^="insight-bulk-action"][data-toggle="slave"]:checked').each((function(index,value){var predictionId=$(value).closest("tr[data-prediction-id]").data("prediction-id");if(void 0!==predictionToUserMapping[predictionId]){var userId=predictionToUserMapping[predictionId];users[predictionId]=userId}else Log.error("Unknown user for prediction "+predictionId)})),0===Object.keys(users).length||this.showSendMessage(users),this}.bind(this))},MessageUsers.prototype.showSendMessage=function(users){var userIds=new Set(Object.values(users));if(0==userIds.length)return $.Deferred().resolve().promise();var titlePromise=null;titlePromise=1==userIds.size?Str.get_string("sendbulkmessagesingle","core_message"):Str.get_string("sendbulkmessage","core_message",userIds.size),ModalSaveCancel.create({body:Templates.render("core_user/send_bulk_message",{}),title:titlePromise,buttons:{save:titlePromise},show:!0}).then(function(modal){return this.modal=modal,this.modal.getRoot().on(ModalEvents.hidden,function(){$(SELECTORS_BULKACTIONSELECT).focus(),this.modal.getRoot().remove()}.bind(this)),this.modal.getRoot().on(ModalEvents.save,this.submitSendMessage.bind(this,users)),this.modal}.bind(this))},MessageUsers.prototype.submitSendMessage=function(users){var messageText=this.modal.getRoot().find("form textarea").val(),messages=[];new Set(Object.values(users)).forEach((function(userId){messages.push({touserid:userId,text:messageText})}));var actionName=this.actionName,message=null;return Ajax.call([{methodname:"core_message_send_instant_messages",args:{messages:messages}}])[0].then((function(messageIds){return 1==messageIds.length?Str.get_string("sendbulkmessagesentsingle","core_message"):Str.get_string("sendbulkmessagesent","core_message",messageIds.length)})).then((function(msg){return message=msg,Ajax.call([{methodname:"report_insights_action_executed",args:{actionname:actionName,predictionids:Object.keys(users)}}])[0]})).then((function(){return Notification.addNotification({message:message,type:"success"}),!0})).catch(Notification.exception)},{init:function(rootNode,actionName){return new MessageUsers(rootNode,actionName)}}}));

//# sourceMappingURL=message_users.min.js.map