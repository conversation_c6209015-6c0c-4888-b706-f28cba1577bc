{"version": 3, "file": "message_users.min.js", "sources": ["../src/message_users.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Message users.\n *\n * @module     report_insights/message_users\n * @copyright  2019 <PERSON>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\ndefine(['jquery', 'core/str', 'core/log', 'core/modal_save_cancel', 'core/modal_events', 'core/templates',\n    'core/notification', 'core/ajax'],\n        function($, Str, Log, ModalSaveCancel, ModalEvents, Templates, Notification, Ajax) {\n\n    var SELECTORS = {\n        BULKACTIONSELECT: \"#formactionid\"\n    };\n\n    /**\n     * Constructor.\n     *\n     * @param {String} rootNode\n     * @param {String} actionName\n     */\n    var MessageUsers = function(rootNode, actionName) {\n        this.actionName = actionName;\n        this.attachEventListeners(rootNode);\n    };\n\n    /**\n     * @var {String} actionName\n     * @private\n     */\n    MessageUsers.prototype.actionName = null;\n\n    /**\n     * @var {Modal} modal\n     * @private\n     */\n    MessageUsers.prototype.modal = null;\n\n    /**\n     * Attach the event listener to the send message bulk action.\n     * @param {String} rootNode\n     */\n    MessageUsers.prototype.attachEventListeners = function(rootNode) {\n        $(rootNode + ' button[data-bulk-sendmessage]').on('click', function(e) {\n            e.preventDefault();\n            var cTarget = $(e.currentTarget);\n\n            // Using an associative array in case there is more than 1 prediction for the same user.\n            var users = {};\n            var predictionToUserMapping = cTarget.data('prediction-to-user-id');\n\n            var checkedSelector = '.insights-list input[data-togglegroup^=\"insight-bulk-action\"][data-toggle=\"slave\"]:checked';\n            $(checkedSelector).each(function(index, value) {\n                var predictionId = $(value).closest('tr[data-prediction-id]').data('prediction-id');\n\n                if (typeof predictionToUserMapping[predictionId] === 'undefined') {\n                    Log.error('Unknown user for prediction ' + predictionId);\n                    return;\n                }\n\n                var userId = predictionToUserMapping[predictionId];\n                users[predictionId] = userId;\n\n            });\n\n            if (Object.keys(users).length === 0) {\n                return this;\n            }\n\n            this.showSendMessage(users);\n\n            return this;\n        }.bind(this));\n    };\n\n    /**\n     * Show the send message popup.\n     *\n     * @method showSendMessage\n     * @private\n     * @param {Object} users Prediction id to user id mapping.\n     * @returns {Promise}\n     */\n    MessageUsers.prototype.showSendMessage = function(users) {\n\n        var userIds = new Set(Object.values(users));\n\n        if (userIds.length == 0) {\n            // Nothing to do.\n            return $.Deferred().resolve().promise();\n        }\n        var titlePromise = null;\n        if (userIds.size == 1) {\n            titlePromise = Str.get_string('sendbulkmessagesingle', 'core_message');\n        } else {\n            titlePromise = Str.get_string('sendbulkmessage', 'core_message', userIds.size);\n        }\n\n        // eslint-disable-next-line promise/catch-or-return\n        ModalSaveCancel.create({\n            body: Templates.render('core_user/send_bulk_message', {}),\n            title: titlePromise,\n            buttons: {\n                save: titlePromise,\n            },\n            show: true,\n        })\n        .then(function(modal) {\n            // Keep a reference to the modal.\n            this.modal = modal;\n\n            // We want to focus on the action select when the dialog is closed.\n            this.modal.getRoot().on(ModalEvents.hidden, function() {\n                $(SELECTORS.BULKACTIONSELECT).focus();\n                this.modal.getRoot().remove();\n            }.bind(this));\n\n            this.modal.getRoot().on(ModalEvents.save, this.submitSendMessage.bind(this, users));\n\n            return this.modal;\n        }.bind(this));\n    };\n\n    /**\n     * Send a message to these users.\n     *\n     * @method submitSendMessage\n     * @private\n     * @param {Object} users Prediction id to user id mapping.\n     * @returns {Promise}\n     */\n    MessageUsers.prototype.submitSendMessage = function(users) {\n\n        var messageText = this.modal.getRoot().find('form textarea').val();\n\n        var messages = [];\n\n        var userIds = new Set(Object.values(users));\n        userIds.forEach(function(userId) {\n            messages.push({touserid: userId, text: messageText});\n        });\n\n        var actionName = this.actionName;\n        var message = null;\n        return Ajax.call([{\n            methodname: 'core_message_send_instant_messages',\n            args: {messages: messages}\n        }])[0].then(function(messageIds) {\n            if (messageIds.length == 1) {\n                return Str.get_string('sendbulkmessagesentsingle', 'core_message');\n            } else {\n                return Str.get_string('sendbulkmessagesent', 'core_message', messageIds.length);\n            }\n        }).then(function(msg) {\n\n            // Save this for the following callback. Now that we got everything\n            // done we can flag this action as executed.\n            message = msg;\n\n            return Ajax.call([{\n                methodname: 'report_insights_action_executed',\n                args: {\n                    actionname: actionName,\n                    predictionids: Object.keys(users)\n                }\n            }])[0];\n        }).then(function() {\n            Notification.addNotification({\n                message: message,\n                type: \"success\"\n            });\n            return true;\n        }).catch(Notification.exception);\n    };\n\n    return /** @alias module:report_insights/message_users */ {\n        // Public variables and functions.\n\n        /**\n         * @method init\n         * @param {String} rootNode\n         * @param {String} actionName\n         * @returns {MessageUsers}\n         */\n        'init': function(rootNode, actionName) {\n            return new MessageUsers(rootNode, actionName);\n        }\n    };\n});\n"], "names": ["define", "$", "Str", "Log", "ModalSaveCancel", "ModalEvents", "Templates", "Notification", "Ajax", "SELECTORS", "MessageUsers", "rootNode", "actionName", "attachEventListeners", "prototype", "modal", "on", "e", "preventDefault", "c<PERSON><PERSON><PERSON>", "currentTarget", "users", "predictionToUserMapping", "data", "each", "index", "value", "predictionId", "closest", "userId", "error", "Object", "keys", "length", "showSendMessage", "this", "bind", "userIds", "Set", "values", "Deferred", "resolve", "promise", "titlePromise", "size", "get_string", "create", "body", "render", "title", "buttons", "save", "show", "then", "getRoot", "hidden", "focus", "remove", "submitSendMessage", "messageText", "find", "val", "messages", "for<PERSON>ach", "push", "to<PERSON><PERSON>", "text", "message", "call", "methodname", "args", "messageIds", "msg", "actionname", "predictionids", "addNotification", "type", "catch", "exception"], "mappings": ";;;;;;;AAsBAA,uCAAO,CAAC,SAAU,WAAY,WAAY,yBAA0B,oBAAqB,iBACrF,oBAAqB,cACjB,SAASC,EAAGC,IAAKC,IAAKC,gBAAiBC,YAAaC,UAAWC,aAAcC,UAE7EC,2BACkB,gBASlBC,aAAe,SAASC,SAAUC,iBAC7BA,WAAaA,gBACbC,qBAAqBF,kBAO9BD,aAAaI,UAAUF,WAAa,KAMpCF,aAAaI,UAAUC,MAAQ,KAM/BL,aAAaI,UAAUD,qBAAuB,SAASF,UACnDV,EAAEU,SAAW,kCAAkCK,GAAG,QAAS,SAASC,GAChEA,EAAEC,qBACEC,QAAUlB,EAAEgB,EAAEG,eAGdC,MAAQ,GACRC,wBAA0BH,QAAQI,KAAK,gCAG3CtB,EADsB,8FACHuB,MAAK,SAASC,MAAOC,WAChCC,aAAe1B,EAAEyB,OAAOE,QAAQ,0BAA0BL,KAAK,yBAEd,IAA1CD,wBAAwBK,mBAK/BE,OAASP,wBAAwBK,cACrCN,MAAMM,cAAgBE,YALlB1B,IAAI2B,MAAM,+BAAiCH,iBASjB,IAA9BI,OAAOC,KAAKX,OAAOY,aAIlBC,gBAAgBb,OAHVc,MAMbC,KAAKD,QAWXzB,aAAaI,UAAUoB,gBAAkB,SAASb,WAE1CgB,QAAU,IAAIC,IAAIP,OAAOQ,OAAOlB,WAEd,GAAlBgB,QAAQJ,cAEDhC,EAAEuC,WAAWC,UAAUC,cAE9BC,aAAe,KAEfA,aADgB,GAAhBN,QAAQO,KACO1C,IAAI2C,WAAW,wBAAyB,gBAExC3C,IAAI2C,WAAW,kBAAmB,eAAgBR,QAAQO,MAI7ExC,gBAAgB0C,OAAO,CACnBC,KAAMzC,UAAU0C,OAAO,8BAA+B,IACtDC,MAAON,aACPO,QAAS,CACLC,KAAMR,cAEVS,MAAM,IAETC,KAAK,SAAStC,mBAENA,MAAQA,WAGRA,MAAMuC,UAAUtC,GAAGX,YAAYkD,OAAQ,WACxCtD,EAAEQ,4BAA4B+C,aACzBzC,MAAMuC,UAAUG,UACvBrB,KAAKD,YAEFpB,MAAMuC,UAAUtC,GAAGX,YAAY8C,KAAMhB,KAAKuB,kBAAkBtB,KAAKD,KAAMd,QAErEc,KAAKpB,OACdqB,KAAKD,QAWXzB,aAAaI,UAAU4C,kBAAoB,SAASrC,WAE5CsC,YAAcxB,KAAKpB,MAAMuC,UAAUM,KAAK,iBAAiBC,MAEzDC,SAAW,GAED,IAAIxB,IAAIP,OAAOQ,OAAOlB,QAC5B0C,SAAQ,SAASlC,QACrBiC,SAASE,KAAK,CAACC,SAAUpC,OAAQqC,KAAMP,qBAGvC/C,WAAauB,KAAKvB,WAClBuD,QAAU,YACP3D,KAAK4D,KAAK,CAAC,CACdC,WAAY,qCACZC,KAAM,CAACR,SAAUA,aACjB,GAAGT,MAAK,SAASkB,mBACQ,GAArBA,WAAWtC,OACJ/B,IAAI2C,WAAW,4BAA6B,gBAE5C3C,IAAI2C,WAAW,sBAAuB,eAAgB0B,WAAWtC,WAE7EoB,MAAK,SAASmB,YAIbL,QAAUK,IAEHhE,KAAK4D,KAAK,CAAC,CACdC,WAAY,kCACZC,KAAM,CACFG,WAAY7D,WACZ8D,cAAe3C,OAAOC,KAAKX,WAE/B,MACLgC,MAAK,kBACJ9C,aAAaoE,gBAAgB,CACzBR,QAASA,QACTS,KAAM,aAEH,KACRC,MAAMtE,aAAauE,YAGgC,MAS9C,SAASnE,SAAUC,mBAChB,IAAIF,aAAaC,SAAUC"}