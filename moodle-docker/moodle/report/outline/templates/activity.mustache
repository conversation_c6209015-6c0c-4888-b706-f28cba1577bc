{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template report_outline/activity

    Template for Activity report activities.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * none

    Example context (json):
    {
        "indelegated": true,
        "visible": true,
        "class": "activity",
        "text": "Announcements",
        "cells": [{
            "activityclass": "lastaccess",
            "text": "Friday, 14 June 2024, 5:17 PM"
        }]
    }
}}
<tr>
    {{#activitycolumn}}
        <td class="cell activityname {{!
            }}{{#indelegated}}delegated{{/indelegated}}{{^indelegated}}ps-5{{/indelegated}}"
        >
            <div class="d-flex align-items-center">
                {{{ activityicon }}}
                {{#link}}<a href="{{ link }}">{{/link}}{{{ text }}}{{#link}}</a>{{/link}}
            </div>
            {{^visible}}
            <div class="activity-badges my-1" data-region="visibility">
                <span class="badge rounded-pill bg-secondary text-dark">{{#pix}}i/show, core{{/pix}}{{#str}}hiddenfromstudents{{/str}}</span>
            </div>
            {{/visible}}
        </td>
    {{/activitycolumn}}
    {{#cells}}
        <td class="cell {{ activityclass }}">
            {{#text}}{{{ text }}}{{/text}}
            {{#link}}
                {{> core/action_link}}
            {{/link}}
        </td>
    {{/cells}}
</tr>
