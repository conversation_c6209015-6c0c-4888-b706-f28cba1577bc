{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template report_outline/report

    Template for Activity report.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * none

    Example context (json):
    {
        "minlog": "Monday, 29 April 2024, 4:18 PM",
        "table": {
            "class": "generaltable boxaligncenter",
            "id": "outlinereport",
            "headers": ["Activity", "Views", "Last access"],
            "headerscount": 3,
            "actitivities": [{
                "isactivity": false,
                "indelegated": true,
                "visible": true,
                "class": "activity",
                "text": "Announcements",
                "cells": [{
                    "activityclass": "lastaccess",
                    "text": "Friday, 14 June 2024, 5:17 PM"
                }]
            },
            {
                "issection": true,
                "isdelegated": true,
                "visible": true,
                "class": "section",
                "text": "Section 1"
            }]
        }
    }
}}
<div class="loginfo font-lg my-5 pb-2">{{#str}} computedfromlogs, admin, {{{ minlog }}} {{/str}}</div>
<div class="table-responsive">
    {{#table}}
    <table class="{{ class }}"{{#id}} id = "{{ id }}"{{/id}}>
        <thead>
        <tr>
            {{#headers}}
                <th class="header font-lg" style="" scope="col">{{{ . }}}</th>
            {{/headers}}
        </tr>
        </thead>
        <tbody>
            {{#activities}}
                {{#issection}}
                    {{>report_outline/section }}
                {{/issection}}
                {{#isdelegated}}
                    {{>report_outline/delegatedsection }}
                {{/isdelegated}}
                {{#isactivity}}
                    {{>report_outline/activity }}
                {{/isactivity}}
            {{/activities}}
        </tbody>
    </table>
    {{/table}}
</div>
