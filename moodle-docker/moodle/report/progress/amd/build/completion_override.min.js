/**
 * AMD module to handle overriding activity completion status.
 *
 * @module     report_progress/completion_override
 * @copyright  2016 onwards <PERSON><PERSON> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * @since      3.1
 */
define("report_progress/completion_override",["jquery","core/ajax","core/str","core/modal_save_cancel","core/modal_events","core/notification","core/custom_interaction_events","core/templates","core/pending"],(function($,Ajax,Str,ModalSaveCancel,ModalEvents,Notification,CustomEvents,Templates,Pending){var userFullName,triggerElement,userConfirm=function(e,data){data.originalEvent.preventDefault(),data.originalEvent.stopPropagation(),e.preventDefault(),e.stopPropagation();var elemData=(triggerElement=$(e.currentTarget)).data("changecompl").split("-"),override={userid:elemData[0],cmid:elemData[1],newstate:elemData[2]},newStateStr=1==override.newstate?"completion-y":"completion-n";Str.get_strings([{key:newStateStr,component:"completion"}]).then((function(strings){return Str.get_strings([{key:"confirm",component:"moodle"},{key:"areyousureoverridecompletion",component:"completion",param:strings[0]}])})).then((function(strings){return ModalSaveCancel.create({title:strings[0],body:strings[1],show:!0,removeOnClose:!0})})).then((function(modal){return modal.getRoot().on(ModalEvents.save,(function(){!function(override){const pendingPromise=new Pending("report_progress/compeletion_override/setOverride");Templates.render("core/loading",{}).then((function(html){return triggerElement.append(html),Ajax.call([{methodname:"core_completion_override_activity_completion_status",args:override}])[0]})).then((function(results){var completionState=results.state>0?1:0,tooltipKey=completionState?"completion-y-override":"completion-n-override";Str.get_string(tooltipKey,"completion",userFullName).then((function(stateString){var params={state:stateString,date:"",user:triggerElement.attr("data-userfullname"),activity:triggerElement.attr("data-activityname")};return Str.get_string("progress-title","completion",params)})).then((function(titleString){var tracking,completionTracking=triggerElement.attr("data-completiontracking");return Templates.renderPix((tracking=completionTracking,completionState>0?"i/completion-"+tracking+"-y-override":"i/completion-"+tracking+"-n-override"),"core",titleString)})).then((function(html){var oppositeState=completionState>0?0:1;triggerElement.find(".loading-icon").remove(),triggerElement.data("changecompl",override.userid+"-"+override.cmid+"-"+oppositeState),triggerElement.attr("data-changecompl",override.userid+"-"+override.cmid+"-"+oppositeState),triggerElement.children("img").replaceWith(html)})).catch(Notification.exception)})).then((()=>{pendingPromise.resolve()})).catch(Notification.exception)}(override)})),modal})).catch(Notification.exception)};return{init:function(fullName){userFullName=fullName,$("#completion-progress a.changecompl").each((function(index,element){CustomEvents.define(element,[CustomEvents.events.activate])})),$("#completion-progress").on(CustomEvents.events.activate,"a.changecompl",(function(e,data){userConfirm(e,data)}))}}}));

//# sourceMappingURL=completion_override.min.js.map