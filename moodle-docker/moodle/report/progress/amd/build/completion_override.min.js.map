{"version": 3, "file": "completion_override.min.js", "sources": ["../src/completion_override.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * AMD module to handle overriding activity completion status.\n *\n * @module     report_progress/completion_override\n * @copyright  2016 onwards Eiz Eddin Al Katrib <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @since      3.1\n */\ndefine(['jquery', 'core/ajax', 'core/str', 'core/modal_save_cancel', 'core/modal_events', 'core/notification',\n        'core/custom_interaction_events', 'core/templates', 'core/pending'],\n    function($, Ajax, Str, ModalSaveCancel, ModalEvents, Notification, CustomEvents, Templates, Pending) {\n\n        /**\n         * @var {String} the full name of the current user.\n         * @private\n         */\n        var userFullName;\n\n        /**\n         * @var {JQuery} JQuery object containing the element (completion link) that was most recently activated.\n         * @private\n         */\n        var triggerElement;\n\n        /**\n         * Helper function to get the pix icon key based on the completion state.\n         * @method getIconDescriptorFromState\n         * @param {number} state The current completion state.\n         * @param {string} tracking The completion tracking type, either 'manual' or 'auto'.\n         * @return {string} the key for the respective icon.\n         * @private\n         */\n        var getIconKeyFromState = function(state, tracking) {\n            return state > 0 ? 'i/completion-' + tracking + '-y-override' : 'i/completion-' + tracking + '-n-override';\n        };\n\n        /**\n         * Handles the confirmation of an override change, calling the web service to update it.\n         * @method setOverride\n         * @param {Object} override the override data\n         * @private\n         */\n        var setOverride = function(override) {\n            const pendingPromise = new Pending('report_progress/compeletion_override/setOverride');\n            // Generate a loading spinner while we're working.\n            Templates.render('core/loading', {}).then(function(html) {\n                // Append the loading spinner to the trigger element.\n                triggerElement.append(html);\n\n                // Update the completion status override.\n                return Ajax.call([{\n                    methodname: 'core_completion_override_activity_completion_status',\n                    args: override\n                }])[0];\n            }).then(function(results) {\n                var completionState = (results.state > 0) ? 1 : 0;\n\n                // Now, build the new title string, get the new icon, and update the DOM.\n                var tooltipKey = completionState ? 'completion-y-override' : 'completion-n-override';\n                Str.get_string(tooltipKey, 'completion', userFullName).then(function(stateString) {\n                    var params = {\n                        state: stateString,\n                        date: '',\n                        user: triggerElement.attr('data-userfullname'),\n                        activity: triggerElement.attr('data-activityname')\n                    };\n                    return Str.get_string('progress-title', 'completion', params);\n                }).then(function(titleString) {\n                    var completionTracking = triggerElement.attr('data-completiontracking');\n                    return Templates.renderPix(getIconKeyFromState(completionState, completionTracking), 'core', titleString);\n                }).then(function(html) {\n                    var oppositeState = completionState > 0 ? 0 : 1;\n                    triggerElement.find('.loading-icon').remove();\n                    triggerElement.data('changecompl', override.userid + '-' + override.cmid + '-' + oppositeState);\n                    triggerElement.attr('data-changecompl', override.userid + '-' + override.cmid + '-' + oppositeState);\n                    triggerElement.children(\"img\").replaceWith(html);\n                    return;\n                }).catch(Notification.exception);\n\n                return;\n            })\n            .then(() => {\n                pendingPromise.resolve();\n                return;\n            }).catch(Notification.exception);\n        };\n\n        /**\n         * Handler for activation of a completion status button element.\n         * @method userConfirm\n         * @param {Event} e the CustomEvents event (CustomEvents.events.activate in this case)\n         * @param {Object} data an object containing the original event (click, keydown, etc.).\n         * @private\n         */\n        var userConfirm = function(e, data) {\n            data.originalEvent.preventDefault();\n            data.originalEvent.stopPropagation();\n            e.preventDefault();\n            e.stopPropagation();\n\n            triggerElement = $(e.currentTarget);\n            var elemData = triggerElement.data('changecompl').split('-');\n            var override = {\n                userid: elemData[0],\n                cmid: elemData[1],\n                newstate: elemData[2]\n            };\n            var newStateStr = (override.newstate == 1) ? 'completion-y' : 'completion-n';\n\n            Str.get_strings([\n                {key: newStateStr, component: 'completion'}\n            ]).then(function(strings) {\n                return Str.get_strings([\n                    {key: 'confirm', component: 'moodle'},\n                    {key: 'areyousureoverridecompletion', component: 'completion', param: strings[0]}\n                ]);\n            }).then(function(strings) {\n                // Create a save/cancel modal.\n                return ModalSaveCancel.create({\n                    title: strings[0],\n                    body: strings[1],\n                    show: true,\n                    removeOnClose: true,\n                });\n            }).then(function(modal) {\n                // Now set up the handlers for the confirmation or cancellation of the modal, and show it.\n\n                // Confirmation only.\n                modal.getRoot().on(ModalEvents.save, function() {\n                    setOverride(override);\n                });\n\n                return modal;\n            }).catch(Notification.exception);\n        };\n\n        /**\n         * Init this module which allows activity completion state to be changed via ajax.\n         * @method init\n         * @param {string} fullName The current user's full name.\n         * @private\n         */\n        var init = function(fullName) {\n            userFullName = fullName;\n\n            // Register the click, space and enter events as activators for the trigger element.\n            $('#completion-progress a.changecompl').each(function(index, element) {\n                CustomEvents.define(element, [CustomEvents.events.activate]);\n            });\n\n            // Set the handler on the parent element (the table), but filter so the callback is only called for <a> type children\n            // having the '.changecompl' class. The <a> element can then be accessed in the callback via e.currentTarget.\n            $('#completion-progress').on(CustomEvents.events.activate, \"a.changecompl\", function(e, data) {\n                userConfirm(e, data);\n            });\n        };\n\n        return /** @alias module:report_progress/completion_override */ {\n            init: init\n        };\n    });\n"], "names": ["define", "$", "Ajax", "Str", "ModalSaveCancel", "ModalEvents", "Notification", "CustomEvents", "Templates", "Pending", "userFullName", "triggerElement", "userConfirm", "e", "data", "originalEvent", "preventDefault", "stopPropagation", "elemData", "currentTarget", "split", "override", "userid", "cmid", "newstate", "newStateStr", "get_strings", "key", "component", "then", "strings", "param", "create", "title", "body", "show", "removeOnClose", "modal", "getRoot", "on", "save", "pendingPromise", "render", "html", "append", "call", "methodname", "args", "results", "completionState", "state", "tooltipKey", "get_string", "stateString", "params", "date", "user", "attr", "activity", "titleString", "tracking", "completionTracking", "renderPix", "oppositeState", "find", "remove", "children", "replaceWith", "catch", "exception", "resolve", "setOverride", "init", "fullName", "each", "index", "element", "events", "activate"], "mappings": ";;;;;;;;AAuBAA,6CAAO,CAAC,SAAU,YAAa,WAAY,yBAA0B,oBAAqB,oBAClF,iCAAkC,iBAAkB,iBACxD,SAASC,EAAGC,KAAMC,IAAKC,gBAAiBC,YAAaC,aAAcC,aAAcC,UAAWC,aAMpFC,aAMAC,eAwEAC,YAAc,SAASC,EAAGC,MAC1BA,KAAKC,cAAcC,iBACnBF,KAAKC,cAAcE,kBACnBJ,EAAEG,iBACFH,EAAEI,sBAGEC,UADJP,eAAiBV,EAAEY,EAAEM,gBACSL,KAAK,eAAeM,MAAM,KACpDC,SAAW,CACXC,OAAQJ,SAAS,GACjBK,KAAML,SAAS,GACfM,SAAUN,SAAS,IAEnBO,YAAoC,GAArBJ,SAASG,SAAiB,eAAiB,eAE9DrB,IAAIuB,YAAY,CACZ,CAACC,IAAKF,YAAaG,UAAW,gBAC/BC,MAAK,SAASC,gBACN3B,IAAIuB,YAAY,CACnB,CAACC,IAAK,UAAWC,UAAW,UAC5B,CAACD,IAAK,+BAAgCC,UAAW,aAAcG,MAAOD,QAAQ,SAEnFD,MAAK,SAASC,gBAEN1B,gBAAgB4B,OAAO,CAC1BC,MAAOH,QAAQ,GACfI,KAAMJ,QAAQ,GACdK,MAAM,EACNC,eAAe,OAEpBP,MAAK,SAASQ,cAIbA,MAAMC,UAAUC,GAAGlC,YAAYmC,MAAM,YAtF3B,SAASnB,gBACjBoB,eAAiB,IAAIhC,QAAQ,oDAEnCD,UAAUkC,OAAO,eAAgB,IAAIb,MAAK,SAASc,aAE/ChC,eAAeiC,OAAOD,MAGfzC,KAAK2C,KAAK,CAAC,CACdC,WAAY,sDACZC,KAAM1B,YACN,MACLQ,MAAK,SAASmB,aACTC,gBAAmBD,QAAQE,MAAQ,EAAK,EAAI,EAG5CC,WAAaF,gBAAkB,wBAA0B,wBAC7D9C,IAAIiD,WAAWD,WAAY,aAAczC,cAAcmB,MAAK,SAASwB,iBAC7DC,OAAS,CACTJ,MAAOG,YACPE,KAAM,GACNC,KAAM7C,eAAe8C,KAAK,qBAC1BC,SAAU/C,eAAe8C,KAAK,6BAE3BtD,IAAIiD,WAAW,iBAAkB,aAAcE,WACvDzB,MAAK,SAAS8B,iBAnCiBC,SAoC1BC,mBAAqBlD,eAAe8C,KAAK,kCACtCjD,UAAUsD,WArCaF,SAqCkCC,mBAAjBZ,gBApCxC,EAAI,gBAAkBW,SAAW,cAAgB,gBAAkBA,SAAW,eAoCA,OAAQD,gBAC9F9B,MAAK,SAASc,UACToB,cAAgBd,gBAAkB,EAAI,EAAI,EAC9CtC,eAAeqD,KAAK,iBAAiBC,SACrCtD,eAAeG,KAAK,cAAeO,SAASC,OAAS,IAAMD,SAASE,KAAO,IAAMwC,eACjFpD,eAAe8C,KAAK,mBAAoBpC,SAASC,OAAS,IAAMD,SAASE,KAAO,IAAMwC,eACtFpD,eAAeuD,SAAS,OAAOC,YAAYxB,SAE5CyB,MAAM9D,aAAa+D,cAIzBxC,MAAK,KACFY,eAAe6B,aAEhBF,MAAM9D,aAAa+D,WA6CdE,CAAYlD,aAGTgB,SACR+B,MAAM9D,aAAa+D,kBAwBsC,CAC5DG,KAhBO,SAASC,UAChB/D,aAAe+D,SAGfxE,EAAE,sCAAsCyE,MAAK,SAASC,MAAOC,SACzDrE,aAAaP,OAAO4E,QAAS,CAACrE,aAAasE,OAAOC,cAKtD7E,EAAE,wBAAwBsC,GAAGhC,aAAasE,OAAOC,SAAU,iBAAiB,SAASjE,EAAGC,MACpFF,YAAYC,EAAGC"}