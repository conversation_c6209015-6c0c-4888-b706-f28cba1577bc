<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Lang strings for theme usage report.
 *
 * @package    report_themeusage
 * @copyright  2023 David <PERSON> <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

$string['getreport'] = 'Get report';
$string['invalidparametertheme'] = 'Invalid parameter set for theme.';
$string['pluginname'] = 'Theme usage';
$string['privacy:metadata'] = 'The theme report plugin does not store any personal data.';
$string['themename'] = 'Theme name';
$string['themeusage'] = 'Theme usage';
$string['themeusagereport'] = 'Theme usage report';
$string['themeusagereportall'] = 'All uses of {$a}';
$string['themeusagereportcategory'] = 'Categories using {$a}';
$string['themeusagereportcohort'] = 'Cohorts using {$a}';
$string['themeusagereportcourse'] = 'Courses using {$a}';
$string['themeusagereportuser'] = 'Users using {$a}';
$string['usagetype'] = 'Usage type';
