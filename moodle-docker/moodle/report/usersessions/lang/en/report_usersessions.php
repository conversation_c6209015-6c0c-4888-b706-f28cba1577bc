<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Lang strings.
 *
 * @package   report_usersessions
 * @copyright 2014 Totara Learning Solutions Ltd {@link http://www.totaralms.com/}
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 * <AUTHOR> <<EMAIL>>
 */

$string['logoutothersessions'] = 'Log out all other browser sessions';
$string['logoutothersessions_help'] = 'Log out of all browser sessions, except for this one. This does not affect web apps.';
$string['logoutothersessionssuccess'] = 'You are logged out of all your other sessions.';
$string['logoutsinglesessionsuccess'] = 'You are logged out of the session at {$a}.';
$string['mysessions'] = 'My active browser sessions';
$string['navigationlink'] = 'Browser sessions';
$string['pluginname'] = 'User sessions report';
$string['thissession'] = 'Current session';
$string['usersessions:manageownsessions'] = 'Manage own browser sessions';
$string['privacy:metadata'] = 'The User sessions report plugin does not store any personal data.';
