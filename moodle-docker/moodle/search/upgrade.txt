=== 4.5 Onwards ===

This file has been replaced by UPGRADING.md. See MDL-81125 for further information.

===
This files describes API changes in /search/*,
information provided here is intended especially for developers.

=== 4.2 ===

* Support for optional final element returned by engine `add_documents()` implementations is now removed, all
  six expected returned array elements must be present

=== 3.10 ===

* Search indexing now supports sending multiple documents to the server in a batch. This is implemented
  for the Solr search engine, where it significantly increases performance. For this to work, engines
  should implement add_document_batch() function and return true to supports_add_document_batch().
  There is also an additional parameter returned from add_documents() with the number of batches
  sent, which is used for the log display. Existing engines should continue to work unmodified.
* Search engines can now implement the optional has_alternate_configuration() function to indicate
  if they provide two different connection configurations (for use when moving between two search
  engines of the same type). The constructor should also accept a boolean value (true = alternate);
  passing this to the base class constructor will automatically switch in the alternate
  configuration settings, provided they begin with 'alternate'.

=== 3.8 ===

* Search indexing supports time limits to make the scheduled task run more neatly since 3.4. In order for
  this to work, search engine plugins will need to implement the 'stopat' parameter if they
  override the add_documents() function, and return an extra parameter from this function (see base
  class in engine.php). Unmodified plugins will not work anymore.
* New search engine functions delete_index_for_context and delete_index_for_course are called by
  the search manager to inform the search engine it can remove some documents from its index.
  (Otherwise, documents from delete courses are never removed unless you reindex.) It is optional
  for search engines to support these; if they don't implement them then behaviour is unchanged.

=== 3.7 ===

* Search areas now have categories and can optionally implement get_category_names method to
  display search results of the area in the required tab on the search results screen (if this
  feature is enabled).
* Added a new call back search_area_categories. Plugins can implement this method in lib.php and
  return a list of custom search area categories (\core_search\area_category) and associated with
  them search areas. This will bring additional custom tabs to the search results screen.
* Added \core_search\manager::clean_up_non_existing_area method to clean up removed or renamed
  search areas. To support that a new adhoc task core\task\clean_up_deleted_search_area_task
  added.

=== 3.5 ===

* Search areas may now optionally implement the get_contexts_to_reindex function (for modules and
  blocks, see also get_contexts_to_reindex_extra_sql). This allows a search area to customise the
  order in which it is reindexed when doing a gradual reindex, so as to reindex the most important
  contexts first. If not implemented, the default behaviour for modules and blocks is to reindex
  the newest items first; for other types of search area it will just index the whole system
  context, oldest data first.
* Search engines may now implement get_supported_orders function to provide multiple ordering
  options (other than 'relevance' which is default). If there is more than one order then a choice
  will be shown to users. (This is an optional feature, existing search engine plugins do not need
  to be modified in order to continue working.)
* Module search areas that wish to support group filtering should set the new optional search
  document field groupid (note: to remain compatible with earlier versions, do this inside an if
  statement so that it only happens on 3.4+) and return true to the supports_group_restriction
  function. See documentation in \core_search\base_mod class and example in \mod_forum\search\post.
* When a search engine supports group filtering, the \core_search\manager::search function now
  accepts the optional 'groupids' parameter in its $data input. This parameter is an array of one
  or more group IDs. If supplied, only results from those groups will be returned.
* Search engine plugins will need to be be modified if they wish to support group filtering.
  (Search engines should continue to work unmodified, but will not then support group filtering.)
  The modification steps are:
  - Implement the new update_schema function to make the schema change (add groupid field).
  - Ensure that the groupid field is stored correctly when provided in a document while indexing.
  - Return true to new supports_group_filtering() function.
  - execute_query should support the new $data->groupids parameter (to allow users to restrict
    search results to specific groups) and the modified meaning of the second parameter,
    $accessinfo (to automatically restrict search results users cannot access due to groups).
    See implementation in Solr search engine.
* Search engine plugins can optionally use a new $this->should_skip_schema_check() function to
  decide when to skip slow schema checking inside the is_server_ready function, improving user
  performance on the search page. The Solr plugin implements this.
* API function \core_search\manager::instance() now includes a $fast parameter to skip schema
  checks (as above).

* Search engines should now implement the 'userids' option to restrict search results to those from
  specific users, and return true to the new supports_users() function. The supplied Solr search
  engine includes this feature. If this is not implemented, the search engine will continue to work
  but the 'Users' search option will not appear.

=== 3.4 ===

* Search indexing now supports time limits to make the scheduled task run more neatly. In order for
  this to work, search engine plugins will need to implement the 'stopat' parameter if they
  override the add_documents() function, and return an extra parameter from this function (see base
  class in engine.php). Unmodified plugins will still work, but without supporting time limits.
* Search areas should now implement the get_document_recordset function instead of the old
  get_recordset_by_timestamp API (implement both if the area should work in older Moodle versions
  as well). The new function is the same as the old one, but has an additional context parameter.
  There is a helper function get_context_restriction_sql to make this easy to implement; see code
  in base_activity.php for an example of how to implement this in your search area. (The
  change was required to make search work after restoring sites. It also allows more flexible
  reindexing in other cases.)

=== 3.2 ===

* Base search area classes have been renamed, please update your search areas to use the classes below:
  - \core_search\area\base has been renamed to \core_search\base
  - \core_search\area\base_mod has been renamed to \core_search\base_mod
  - \core_search\area\base_activity has been renamed to \core_search\base_activity
