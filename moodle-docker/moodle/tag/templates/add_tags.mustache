{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_tag/add_tags

    Renders the form for adding tags.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * sesskey string The session key

    Example context (json):
    {
        "actionurl" : "#",
        "sesskey" : "UniqueSesskey1a2b3c"
    }

}}
<form id="addtags_form" method="post" action="{{actionurl}}" class="needs-validation">
    <input type="hidden" name="action" value="addstandardtag"/>
    <input type="hidden" name="sesskey" value="{{sesskey}}"/>
    <div class="mb-3 control-group" data-region="tagslistinput">
        <label for="id_tagslist">
            {{#str}}inputstandardtags, tag{{/str}}
            <span>
                <abbr class="initialism text-danger" title="{{#str}}required{{/str}}">
                    {{#pix}}req, core, {{#str}}required{{/str}}{{/pix}}
                </abbr>
            </span>
        </label>
        {{! Enclosing these controls in a div with the controls class for BS2 compatibility when showing validation errors.}}
        <div class="controls">
            <input type="text" id="id_tagslist" class="form-control" name="tagslist" aria-required="true" required />
            <div class="form-control-feedback invalid-feedback" id="id_tagslist_error_message" hidden>
                {{#str}}required{{/str}}
            </div>
        </div>
    </div>
</form>
