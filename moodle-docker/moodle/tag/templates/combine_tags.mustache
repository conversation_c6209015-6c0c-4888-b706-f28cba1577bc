{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_tag/combine_tags

    Renders the form for combining tags.

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * tags Array The list of tags to combine.

    Example context (json):
    {
        "tags": [
            {
                "id": 1,
                "name": "Cat"
            },
            {
                "id": 2,
                "name": "<PERSON>"
            },
            {
                "id": 3,
                "name": "<PERSON>"
            }
        ]
    }

}}
<form id="combinetags_form">
    <div class="description">{{#str}}selectmaintag, tag{{/str}}</div>
    <div class="mb-3 options d-flex flex-wrap align-items-center">
        {{#tags}}
            <div class="form-check w-100 justify-content-start">
                <input type="radio" class="form-check-input" name="maintag" id="combinetags_maintag_{{id}}" value="{{id}}"/>
                <label class="form-check-label" for="combinetags_maintag_{{id}}">{{name}}</label>
            </div>
        {{/tags}}
    </div>
</form>
