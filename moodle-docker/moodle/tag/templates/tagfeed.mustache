{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_tag/tagfeed

    Displays list of items tagged with a tag

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * none

    Example context (json):
    {
    "items": [
            {"img":"<img class=\"smallicon\" src=\"https://moodle.org/pix/i/siteevent.png\">","heading":"<a href=\"#\">First post</a>","details":"Posted by user 1"},
            {"img":"<img class=\"smallicon\" src=\"https://moodle.org/pix/i/siteevent.png\">","heading":"<a href=\"#\">Second post without details</a>"},
            {"heading":"<a href=\"#\">Third post without image</a>","details":"Posted by <a href=\"#\">user 3</a>"}
    ]
    }

}}
<ul class="tag_feed">
    {{#items}}
        <li class="d-flex mb-1">
            {{#img}}
                <div class="flex-shrink-0">
                    {{{img}}}
                </div>
            {{/img}}
            <div class="flex-grow-1 px-2">
                {{#heading}}
                    <div class="item-heading">
                        {{{heading}}}
                    </div>
                {{/heading}}
                {{#details}}
                    <div class="muted">
                        {{{details}}}
                    </div>
                {{/details}}
            </div>
        </li>
    {{/items}}
</ul>
