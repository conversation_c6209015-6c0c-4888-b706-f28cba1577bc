{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_tag/taglist

    Displays a tag list, usually the list of tags some entry is tagged with

    Classes required for JS:
    * hideoverlimit
    * tagmorelink
    * taglesslink

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * none

    Example context (json):
    {
    "tags": [
        {"id":1,"name":"Cats","viewurl":"http://moodle.org/tag/index.php?tag=Cats","isstandard":"1","flag":0},
        {"id":1,"name":"Dogs","viewurl":"http://moodle.org/tag/index.php?tag=Dogs","isstandard":"0","flag":1},
        {"id":1,"name":"Mice","viewurl":"http://moodle.org/tag/index.php?tag=Mice","isstandard":"0","flag":0}
    ],
        "label": "Tags",
        "accesshidelabel": false,
        "tagscount": 3,
        "overflow": 1,
        "classes": "someadditionalclass",
        "displaylink": false
    }

}}
{{#tagscount}}
    <div class="tag_list hideoverlimit {{classes}}">
    {{#label}}
        <b{{#accesshidelabel}} class="accesshide"{{/accesshidelabel}}>{{label}}:</b>
    {{/label}}
    <ul class="inline-list">
        {{#tags}}
            <li {{#overlimit}}class="overlimit"{{/overlimit}}>
                {{#displaylink}}
                    <a href="{{viewurl}}" class="badge bg-info text-white {{#isstandard}}standardtag{{/isstandard}}">
                        {{#flag}}
                            <span class="flagged-tag">{{name}}</span>
                        {{/flag}}
                        {{^flag}}
                            {{name}}
                        {{/flag}}
                    </a>
                {{/displaylink}}
                {{^displaylink}}
                    <span class="badge bg-info {{#flag}}flagged-tag{{/flag}} {{^flag}}text-white{{/flag}} {{#isstandard}}standardtag{{/isstandard}}">
                        {{name}}
                    </span>
                {{/displaylink}}
            </li>
        {{/tags}}
        {{#overflow}}
            <li class="visibleifjs">
                <a href="#" class="tagmorelink">{{#str}}moretags, core_tag{{/str}}</a>
            </li>
            <li class="visibleifjs">
                <a href="#" class="taglesslink">{{#str}}lesstags, core_tag{{/str}}</a>
            </li>
        {{/overflow}}
    </ul>
    </div>
{{/tagscount}}

{{#js}}
require(['jquery'], function($) {
    $('.tag_list .tagmorelink').click(function(e) {
        e.preventDefault();
        $(this).closest('.tag_list').removeClass('hideoverlimit');
    });
    $('.tag_list .taglesslink').click(function(e) {
        e.preventDefault();
        $(this).closest('.tag_list').addClass('hideoverlimit');
    });
});
{{/js}}
