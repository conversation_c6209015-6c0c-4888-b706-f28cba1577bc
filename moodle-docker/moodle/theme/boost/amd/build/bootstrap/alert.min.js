define("theme_boost/bootstrap/alert",["exports","./base-component","./dom/event-handler","./util/component-functions","./util/index"],(function(_exports,_baseComponent,_eventHandler,_componentFunctions,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler);const EVENT_KEY=".".concat("bs.alert"),EVENT_CLOSE="close".concat(EVENT_KEY),EVENT_CLOSED="closed".concat(EVENT_KEY);class Alert extends _baseComponent.default{static get NAME(){return"alert"}close(){if(_eventHandler.default.trigger(this._element,EVENT_CLOSE).defaultPrevented)return;this._element.classList.remove("show");const isAnimated=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,isAnimated)}_destroyElement(){this._element.remove(),_eventHandler.default.trigger(this._element,EVENT_CLOSED),this.dispose()}static jQueryInterface(config){return this.each((function(){const data=Alert.getOrCreateInstance(this);if("string"==typeof config){if(void 0===data[config]||config.startsWith("_")||"constructor"===config)throw new TypeError('No method named "'.concat(config,'"'));data[config](this)}}))}}(0,_componentFunctions.enableDismissTrigger)(Alert,"close"),(0,_index.defineJQueryPlugin)(Alert);var _default=Alert;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=alert.min.js.map