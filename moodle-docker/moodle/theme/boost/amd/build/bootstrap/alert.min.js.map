{"version": 3, "file": "alert.min.js", "sources": ["../../src/bootstrap/alert.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport { enableDismissTrigger } from './util/component-functions'\nimport { defineJQueryPlugin } from './util/index'\n\n/**\n * Constants\n */\n\nconst NAME = 'alert'\nconst DATA_KEY = 'bs.alert'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_CLOSE = `close${EVENT_KEY}`\nconst EVENT_CLOSED = `closed${EVENT_KEY}`\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE)\n\n    if (closeEvent.defaultPrevented) {\n      return\n    }\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE)\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated)\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove()\n    EventHandler.trigger(this._element, EVENT_CLOSED)\n    this.dispose()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close')\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert)\n\nexport default Alert\n"], "names": ["EVENT_KEY", "EVENT_CLOSE", "EVENT_CLOSED", "<PERSON><PERSON>", "BaseComponent", "NAME", "close", "EventHandler", "trigger", "this", "_element", "defaultPrevented", "classList", "remove", "isAnimated", "contains", "_queueCallback", "_destroyElement", "dispose", "config", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError"], "mappings": "oeAkBMA,qBADW,YAGXC,2BAAsBD,WACtBE,6BAAwBF,iBAQxBG,cAAcC,uBAEPC,wBAfA,QAoBXC,WACqBC,sBAAaC,QAAQC,KAAKC,SAAUT,aAExCU,6BAIVD,SAASE,UAAUC,OApBJ,cAsBdC,WAAaL,KAAKC,SAASE,UAAUG,SAvBvB,aAwBfC,gBAAe,IAAMP,KAAKQ,mBAAmBR,KAAKC,SAAUI,YAInEG,uBACOP,SAASG,+BACDL,QAAQC,KAAKC,SAAUR,mBAC/BgB,iCAIgBC,eACdV,KAAKW,MAAK,iBACTC,KAAOlB,MAAMmB,oBAAoBb,SAEjB,iBAAXU,gBAIUI,IAAjBF,KAAKF,SAAyBA,OAAOK,WAAW,MAAmB,gBAAXL,aACpD,IAAIM,qCAA8BN,aAG1CE,KAAKF,QAAQV,wDASEN,MAAO,uCAMTA,oBAEJA"}