define("theme_boost/bootstrap/base-component",["exports","./dom/data","./dom/event-handler","./util/config","./util/index"],(function(_exports,_data,_eventHandler,_config,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_data=_interopRequireDefault(_data),_eventHandler=_interopRequireDefault(_eventHandler),_config=_interopRequireDefault(_config);class BaseComponent extends _config.default{constructor(element,config){super(),(element=(0,_index.getElement)(element))&&(this._element=element,this._config=this._getConfig(config),_data.default.set(this._element,this.constructor.DATA_KEY,this))}dispose(){_data.default.remove(this._element,this.constructor.DATA_KEY),_eventHandler.default.off(this._element,this.constructor.EVENT_KEY);for(const propertyName of Object.getOwnPropertyNames(this))this[propertyName]=null}_queueCallback(callback,element){let isAnimated=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];(0,_index.executeAfterTransition)(callback,element,isAnimated)}_getConfig(config){return config=this._mergeConfigObj(config,this._element),config=this._configAfterMerge(config),this._typeCheckConfig(config),config}static getInstance(element){return _data.default.get((0,_index.getElement)(element),this.DATA_KEY)}static getOrCreateInstance(element){let config=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return this.getInstance(element)||new this(element,"object"==typeof config?config:null)}static get VERSION(){return"5.3.3"}static get DATA_KEY(){return"bs.".concat(this.NAME)}static get EVENT_KEY(){return".".concat(this.DATA_KEY)}static eventName(name){return"".concat(name).concat(this.EVENT_KEY)}}var _default=BaseComponent;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=base-component.min.js.map