{"version": 3, "file": "base-component.min.js", "sources": ["../../src/bootstrap/base-component.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Data from './dom/data'\nimport EventHandler from './dom/event-handler'\nimport Config from './util/config'\nimport { executeAfterTransition, getElement } from './util/index'\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.3'\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super()\n\n    element = getElement(element)\n    if (!element) {\n      return\n    }\n\n    this._element = element\n    this._config = this._getConfig(config)\n\n    Data.set(this._element, this.constructor.DATA_KEY, this)\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY)\n    EventHandler.off(this._element, this.constructor.EVENT_KEY)\n\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null\n    }\n  }\n\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated)\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY)\n  }\n\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null)\n  }\n\n  static get VERSION() {\n    return VERSION\n  }\n\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`\n  }\n\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`\n  }\n\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`\n  }\n}\n\nexport default BaseComponent\n"], "names": ["BaseComponent", "Config", "constructor", "element", "config", "_element", "_config", "this", "_getConfig", "set", "DATA_KEY", "dispose", "remove", "off", "EVENT_KEY", "propertyName", "Object", "getOwnPropertyNames", "_queueCallback", "callback", "isAnimated", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "Data", "get", "getInstance", "VERSION", "NAME", "name"], "mappings": "2dAsBMA,sBAAsBC,gBAC1BC,YAAYC,QAASC,iBAGnBD,SAAU,qBAAWA,iBAKhBE,SAAWF,aACXG,QAAUC,KAAKC,WAAWJ,sBAE1BK,IAAIF,KAAKF,SAAUE,KAAKL,YAAYQ,SAAUH,OAIrDI,wBACOC,OAAOL,KAAKF,SAAUE,KAAKL,YAAYQ,gCAC/BG,IAAIN,KAAKF,SAAUE,KAAKL,YAAYY,eAE5C,MAAMC,gBAAgBC,OAAOC,oBAAoBV,WAC/CQ,cAAgB,KAIzBG,eAAeC,SAAUhB,aAASiB,wGACTD,SAAUhB,QAASiB,YAG5CZ,WAAWJ,eACTA,OAASG,KAAKc,gBAAgBjB,OAAQG,KAAKF,UAC3CD,OAASG,KAAKe,kBAAkBlB,aAC3BmB,iBAAiBnB,QACfA,0BAIUD,gBACVqB,cAAKC,KAAI,qBAAWtB,SAAUI,KAAKG,qCAGjBP,aAASC,8DAAS,UACpCG,KAAKmB,YAAYvB,UAAY,IAAII,KAAKJ,QAA2B,iBAAXC,OAAsBA,OAAS,MAGnFuB,2BAnDG,QAuDHjB,yCACIH,KAAKqB,MAGTd,wCACEP,KAAKG,2BAGDmB,sBACLA,aAAOtB,KAAKO,yBAIXd"}