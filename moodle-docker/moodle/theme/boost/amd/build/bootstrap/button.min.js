define("theme_boost/bootstrap/button",["exports","./base-component","./dom/event-handler","./util/index"],(function(_exports,_baseComponent,_eventHandler,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler);const EVENT_KEY=".".concat("bs.button"),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api");class Button extends _baseComponent.default{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(config){return this.each((function(){const data=Button.getOrCreateInstance(this);"toggle"===config&&data[config]()}))}}_eventHandler.default.on(document,EVENT_CLICK_DATA_API,'[data-bs-toggle="button"]',(event=>{event.preventDefault();const button=event.target.closest('[data-bs-toggle="button"]');Button.getOrCreateInstance(button).toggle()})),(0,_index.defineJQueryPlugin)(Button);var _default=Button;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=button.min.js.map