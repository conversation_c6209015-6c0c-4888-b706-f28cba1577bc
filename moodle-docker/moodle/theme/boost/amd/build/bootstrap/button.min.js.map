{"version": 3, "file": "button.min.js", "sources": ["../../src/bootstrap/button.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport { defineJQueryPlugin } from './util/index'\n\n/**\n * Constants\n */\n\nconst NAME = 'button'\nconst DATA_KEY = 'bs.button'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"button\"]'\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE))\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this)\n\n      if (config === 'toggle') {\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, event => {\n  event.preventDefault()\n\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE)\n  const data = Button.getOrCreateInstance(button)\n\n  data.toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button)\n\nexport default Button\n"], "names": ["EVENT_KEY", "EVENT_CLICK_DATA_API", "<PERSON><PERSON>", "BaseComponent", "NAME", "toggle", "_element", "setAttribute", "this", "classList", "config", "each", "data", "getOrCreateInstance", "on", "document", "event", "preventDefault", "button", "target", "closest"], "mappings": "obAiBMA,qBADW,aAMXC,oCAA+BD,kBAJhB,mBAUfE,eAAeC,uBAERC,wBAfA,SAoBXC,cAEOC,SAASC,aAAa,eAAgBC,KAAKF,SAASG,UAAUJ,OAjB7C,kCAqBDK,eACdF,KAAKG,MAAK,iBACTC,KAAOV,OAAOW,oBAAoBL,MAEzB,WAAXE,QACFE,KAAKF,oCAUAI,GAAGC,SAAUd,qBAnCG,6BAmCyCe,QACpEA,MAAMC,uBAEAC,OAASF,MAAMG,OAAOC,QAtCD,6BAuCdlB,OAAOW,oBAAoBK,QAEnCb,0CAOYH,qBAEJA"}