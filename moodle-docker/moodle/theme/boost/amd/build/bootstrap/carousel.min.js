define("theme_boost/bootstrap/carousel",["exports","./base-component","./dom/event-handler","./dom/manipulator","./dom/selector-engine","./util/index","./util/swipe"],(function(_exports,_baseComponent,_eventHandler,_manipulator,_selectorEngine,_index,_swipe){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler),_manipulator=_interopRequireDefault(_manipulator),_selectorEngine=_interopRequireDefault(_selectorEngine),_swipe=_interopRequireDefault(_swipe);const EVENT_KEY=".".concat("bs.carousel"),EVENT_SLIDE="slide".concat(EVENT_KEY),EVENT_SLID="slid".concat(EVENT_KEY),EVENT_KEYDOWN="keydown".concat(EVENT_KEY),EVENT_MOUSEENTER="mouseenter".concat(EVENT_KEY),EVENT_MOUSELEAVE="mouseleave".concat(EVENT_KEY),EVENT_DRAG_START="dragstart".concat(EVENT_KEY),EVENT_LOAD_DATA_API="load".concat(EVENT_KEY).concat(".data-api"),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),KEY_TO_DIRECTION={ArrowLeft:"right",ArrowRight:"left"},Default={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},DefaultType={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Carousel extends _baseComponent.default{constructor(element,config){super(element,config),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=_selectorEngine.default.findOne(".carousel-indicators",this._element),this._addEventListeners(),"carousel"===this._config.ride&&this.cycle()}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"carousel"}next(){this._slide("next")}nextWhenVisible(){!document.hidden&&(0,_index.isVisible)(this._element)&&this.next()}prev(){this._slide("prev")}pause(){this._isSliding&&(0,_index.triggerTransitionEnd)(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?_eventHandler.default.one(this._element,EVENT_SLID,(()=>this.cycle())):this.cycle())}to(index){const items=this._getItems();if(index>items.length-1||index<0)return;if(this._isSliding)return void _eventHandler.default.one(this._element,EVENT_SLID,(()=>this.to(index)));const activeIndex=this._getItemIndex(this._getActive());if(activeIndex===index)return;const order=index>activeIndex?"next":"prev";this._slide(order,items[index])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(config){return config.defaultInterval=config.interval,config}_addEventListeners(){this._config.keyboard&&_eventHandler.default.on(this._element,EVENT_KEYDOWN,(event=>this._keydown(event))),"hover"===this._config.pause&&(_eventHandler.default.on(this._element,EVENT_MOUSEENTER,(()=>this.pause())),_eventHandler.default.on(this._element,EVENT_MOUSELEAVE,(()=>this._maybeEnableCycle()))),this._config.touch&&_swipe.default.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const img of _selectorEngine.default.find(".carousel-item img",this._element))_eventHandler.default.on(img,EVENT_DRAG_START,(event=>event.preventDefault()));const swipeConfig={leftCallback:()=>this._slide(this._directionToOrder("left")),rightCallback:()=>this._slide(this._directionToOrder("right")),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new _swipe.default(this._element,swipeConfig)}_keydown(event){if(/input|textarea/i.test(event.target.tagName))return;const direction=KEY_TO_DIRECTION[event.key];direction&&(event.preventDefault(),this._slide(this._directionToOrder(direction)))}_getItemIndex(element){return this._getItems().indexOf(element)}_setActiveIndicatorElement(index){if(!this._indicatorsElement)return;const activeIndicator=_selectorEngine.default.findOne(".active",this._indicatorsElement);activeIndicator.classList.remove("active"),activeIndicator.removeAttribute("aria-current");const newActiveIndicator=_selectorEngine.default.findOne('[data-bs-slide-to="'.concat(index,'"]'),this._indicatorsElement);newActiveIndicator&&(newActiveIndicator.classList.add("active"),newActiveIndicator.setAttribute("aria-current","true"))}_updateInterval(){const element=this._activeElement||this._getActive();if(!element)return;const elementInterval=Number.parseInt(element.getAttribute("data-bs-interval"),10);this._config.interval=elementInterval||this._config.defaultInterval}_slide(order){let element=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this._isSliding)return;const activeElement=this._getActive(),isNext="next"===order,nextElement=element||(0,_index.getNextActiveElement)(this._getItems(),activeElement,isNext,this._config.wrap);if(nextElement===activeElement)return;const nextElementIndex=this._getItemIndex(nextElement),triggerEvent=eventName=>_eventHandler.default.trigger(this._element,eventName,{relatedTarget:nextElement,direction:this._orderToDirection(order),from:this._getItemIndex(activeElement),to:nextElementIndex});if(triggerEvent(EVENT_SLIDE).defaultPrevented)return;if(!activeElement||!nextElement)return;const isCycling=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(nextElementIndex),this._activeElement=nextElement;const directionalClassName=isNext?"carousel-item-start":"carousel-item-end",orderClassName=isNext?"carousel-item-next":"carousel-item-prev";nextElement.classList.add(orderClassName),(0,_index.reflow)(nextElement),activeElement.classList.add(directionalClassName),nextElement.classList.add(directionalClassName);this._queueCallback((()=>{nextElement.classList.remove(directionalClassName,orderClassName),nextElement.classList.add("active"),activeElement.classList.remove("active",orderClassName,directionalClassName),this._isSliding=!1,triggerEvent(EVENT_SLID)}),activeElement,this._isAnimated()),isCycling&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return _selectorEngine.default.findOne(".active.carousel-item",this._element)}_getItems(){return _selectorEngine.default.find(".carousel-item",this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(direction){return(0,_index.isRTL)()?"left"===direction?"prev":"next":"left"===direction?"next":"prev"}_orderToDirection(order){return(0,_index.isRTL)()?"prev"===order?"left":"right":"prev"===order?"right":"left"}static jQueryInterface(config){return this.each((function(){const data=Carousel.getOrCreateInstance(this,config);if("number"!=typeof config){if("string"==typeof config){if(void 0===data[config]||config.startsWith("_")||"constructor"===config)throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}else data.to(config)}))}}_eventHandler.default.on(document,EVENT_CLICK_DATA_API,"[data-bs-slide], [data-bs-slide-to]",(function(event){const target=_selectorEngine.default.getElementFromSelector(this);if(!target||!target.classList.contains("carousel"))return;event.preventDefault();const carousel=Carousel.getOrCreateInstance(target),slideIndex=this.getAttribute("data-bs-slide-to");return slideIndex?(carousel.to(slideIndex),void carousel._maybeEnableCycle()):"next"===_manipulator.default.getDataAttribute(this,"slide")?(carousel.next(),void carousel._maybeEnableCycle()):(carousel.prev(),void carousel._maybeEnableCycle())})),_eventHandler.default.on(window,EVENT_LOAD_DATA_API,(()=>{const carousels=_selectorEngine.default.find('[data-bs-ride="carousel"]');for(const carousel of carousels)Carousel.getOrCreateInstance(carousel)})),(0,_index.defineJQueryPlugin)(Carousel);var _default=Carousel;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=carousel.min.js.map