{"version": 3, "file": "carousel.min.js", "sources": ["../../src/bootstrap/carousel.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport {\n  defineJQueryPlugin,\n  getNextActiveElement,\n  isRTL,\n  isVisible,\n  reflow,\n  triggerTransitionEnd\n} from './util/index'\nimport Swipe from './util/swipe'\n\n/**\n * Constants\n */\n\nconst NAME = 'carousel'\nconst DATA_KEY = 'bs.carousel'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next'\nconst ORDER_PREV = 'prev'\nconst DIRECTION_LEFT = 'left'\nconst DIRECTION_RIGHT = 'right'\n\nconst EVENT_SLIDE = `slide${EVENT_KEY}`\nconst EVENT_SLID = `slid${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_MOUSEENTER = `mouseenter${EVENT_KEY}`\nconst EVENT_MOUSELEAVE = `mouseleave${EVENT_KEY}`\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_CAROUSEL = 'carousel'\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_SLIDE = 'slide'\nconst CLASS_NAME_END = 'carousel-item-end'\nconst CLASS_NAME_START = 'carousel-item-start'\nconst CLASS_NAME_NEXT = 'carousel-item-next'\nconst CLASS_NAME_PREV = 'carousel-item-prev'\n\nconst SELECTOR_ACTIVE = '.active'\nconst SELECTOR_ITEM = '.carousel-item'\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM\nconst SELECTOR_ITEM_IMG = '.carousel-item img'\nconst SELECTOR_INDICATORS = '.carousel-indicators'\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]'\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]'\n\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY]: DIRECTION_LEFT\n}\n\nconst Default = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n}\n\nconst DefaultType = {\n  interval: '(number|boolean)', // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._interval = null\n    this._activeElement = null\n    this._isSliding = false\n    this.touchTimeout = null\n    this._swipeHelper = null\n\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element)\n    this._addEventListeners()\n\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT)\n  }\n\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next()\n    }\n  }\n\n  prev() {\n    this._slide(ORDER_PREV)\n  }\n\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element)\n    }\n\n    this._clearInterval()\n  }\n\n  cycle() {\n    this._clearInterval()\n    this._updateInterval()\n\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval)\n  }\n\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle())\n      return\n    }\n\n    this.cycle()\n  }\n\n  to(index) {\n    const items = this._getItems()\n    if (index > items.length - 1 || index < 0) {\n      return\n    }\n\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index))\n      return\n    }\n\n    const activeIndex = this._getItemIndex(this._getActive())\n    if (activeIndex === index) {\n      return\n    }\n\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV\n\n    this._slide(order, items[index])\n  }\n\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose()\n    }\n\n    super.dispose()\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval\n    return config\n  }\n\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n    }\n\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER, () => this.pause())\n      EventHandler.on(this._element, EVENT_MOUSELEAVE, () => this._maybeEnableCycle())\n    }\n\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners()\n    }\n  }\n\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault())\n    }\n\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause()\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout)\n      }\n\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n    }\n\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    }\n\n    this._swipeHelper = new Swipe(this._element, swipeConfig)\n  }\n\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return\n    }\n\n    const direction = KEY_TO_DIRECTION[event.key]\n    if (direction) {\n      event.preventDefault()\n      this._slide(this._directionToOrder(direction))\n    }\n  }\n\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element)\n  }\n\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return\n    }\n\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement)\n\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE)\n    activeIndicator.removeAttribute('aria-current')\n\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement)\n\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE)\n      newActiveIndicator.setAttribute('aria-current', 'true')\n    }\n  }\n\n  _updateInterval() {\n    const element = this._activeElement || this._getActive()\n\n    if (!element) {\n      return\n    }\n\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10)\n\n    this._config.interval = elementInterval || this._config.defaultInterval\n  }\n\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return\n    }\n\n    const activeElement = this._getActive()\n    const isNext = order === ORDER_NEXT\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap)\n\n    if (nextElement === activeElement) {\n      return\n    }\n\n    const nextElementIndex = this._getItemIndex(nextElement)\n\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      })\n    }\n\n    const slideEvent = triggerEvent(EVENT_SLIDE)\n\n    if (slideEvent.defaultPrevented) {\n      return\n    }\n\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // TODO: change tests that use empty divs to avoid this check\n      return\n    }\n\n    const isCycling = Boolean(this._interval)\n    this.pause()\n\n    this._isSliding = true\n\n    this._setActiveIndicatorElement(nextElementIndex)\n    this._activeElement = nextElement\n\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV\n\n    nextElement.classList.add(orderClassName)\n\n    reflow(nextElement)\n\n    activeElement.classList.add(directionalClassName)\n    nextElement.classList.add(directionalClassName)\n\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName)\n      nextElement.classList.add(CLASS_NAME_ACTIVE)\n\n      activeElement.classList.remove(CLASS_NAME_ACTIVE, orderClassName, directionalClassName)\n\n      this._isSliding = false\n\n      triggerEvent(EVENT_SLID)\n    }\n\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated())\n\n    if (isCycling) {\n      this.cycle()\n    }\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE)\n  }\n\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element)\n  }\n\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element)\n  }\n\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval)\n      this._interval = null\n    }\n  }\n\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT\n    }\n\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV\n  }\n\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT\n    }\n\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config)\n\n      if (typeof config === 'number') {\n        data.to(config)\n        return\n      }\n\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return\n  }\n\n  event.preventDefault()\n\n  const carousel = Carousel.getOrCreateInstance(target)\n  const slideIndex = this.getAttribute('data-bs-slide-to')\n\n  if (slideIndex) {\n    carousel.to(slideIndex)\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next()\n    carousel._maybeEnableCycle()\n    return\n  }\n\n  carousel.prev()\n  carousel._maybeEnableCycle()\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE)\n\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel)\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel)\n\nexport default Carousel\n"], "names": ["EVENT_KEY", "EVENT_SLIDE", "EVENT_SLID", "EVENT_KEYDOWN", "EVENT_MOUSEENTER", "EVENT_MOUSELEAVE", "EVENT_DRAG_START", "EVENT_LOAD_DATA_API", "EVENT_CLICK_DATA_API", "KEY_TO_DIRECTION", "<PERSON><PERSON><PERSON>", "interval", "keyboard", "pause", "ride", "touch", "wrap", "DefaultType", "Carousel", "BaseComponent", "constructor", "element", "config", "_interval", "_activeElement", "_isSliding", "touchTimeout", "_swipe<PERSON><PERSON>per", "_indicatorsElement", "SelectorEngine", "findOne", "this", "_element", "_addEventListeners", "_config", "cycle", "NAME", "next", "_slide", "nextWhenVisible", "document", "hidden", "prev", "_clearInterval", "_updateInterval", "setInterval", "_maybeEnableCycle", "one", "to", "index", "items", "_getItems", "length", "activeIndex", "_getItemIndex", "_getActive", "order", "dispose", "_configAfterMerge", "defaultInterval", "on", "event", "_keydown", "Swipe", "isSupported", "_addTouchEventListeners", "img", "find", "preventDefault", "swipeConfig", "leftCallback", "_directionToOrder", "<PERSON><PERSON><PERSON><PERSON>", "endCallback", "clearTimeout", "setTimeout", "test", "target", "tagName", "direction", "key", "indexOf", "_setActiveIndicatorElement", "activeIndicator", "classList", "remove", "removeAttribute", "newActiveIndicator", "add", "setAttribute", "elementInterval", "Number", "parseInt", "getAttribute", "activeElement", "isNext", "nextElement", "nextElementIndex", "triggerEvent", "eventName", "EventHandler", "trigger", "relatedTarget", "_orderToDirection", "from", "defaultPrevented", "isCycling", "Boolean", "directionalClassName", "orderClassName", "_queueCallback", "_isAnimated", "contains", "SELECTOR_ACTIVE", "clearInterval", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError", "getElementFromSelector", "carousel", "slideIndex", "Manipulator", "getDataAttribute", "window", "carousels"], "mappings": "qqBA2BMA,qBADW,eAaXC,2BAAsBD,WACtBE,yBAAoBF,WACpBG,+BAA0BH,WAC1BI,qCAAgCJ,WAChCK,qCAAgCL,WAChCM,oCAA+BN,WAC/BO,kCAA6BP,kBAjBd,aAkBfQ,oCAA+BR,kBAlBhB,aAoCfS,iBAAmB,WA3BD,mBADD,QAiCjBC,QAAU,CACdC,SAAU,IACVC,UAAU,EACVC,MAAO,QACPC,MAAM,EACNC,OAAO,EACPC,MAAM,GAGFC,YAAc,CAClBN,SAAU,mBACVC,SAAU,UACVC,MAAO,mBACPC,KAAM,mBACNC,MAAO,UACPC,KAAM,iBAOFE,iBAAiBC,uBACrBC,YAAYC,QAASC,cACbD,QAASC,aAEVC,UAAY,UACZC,eAAiB,UACjBC,YAAa,OACbC,aAAe,UACfC,aAAe,UAEfC,mBAAqBC,wBAAeC,QAzCjB,uBAyC8CC,KAAKC,eACtEC,qBAtDmB,aAwDpBF,KAAKG,QAAQpB,WACVqB,QAKEzB,4BACFA,QAGEO,gCACFA,YAGEmB,wBA7FA,WAkGXC,YACOC,OA1FU,QA6FjBC,mBAIOC,SAASC,SAAU,oBAAUV,KAAKC,gBAChCK,OAITK,YACOJ,OAtGU,QAyGjBzB,QACMkB,KAAKN,4CACcM,KAAKC,eAGvBW,iBAGPR,aACOQ,sBACAC,uBAEArB,UAAYsB,aAAY,IAAMd,KAAKQ,mBAAmBR,KAAKG,QAAQvB,UAG1EmC,oBACOf,KAAKG,QAAQpB,OAIdiB,KAAKN,iCACMsB,IAAIhB,KAAKC,SAAU9B,YAAY,IAAM6B,KAAKI,eAIpDA,SAGPa,GAAGC,aACKC,MAAQnB,KAAKoB,eACfF,MAAQC,MAAME,OAAS,GAAKH,MAAQ,YAIpClB,KAAKN,6CACMsB,IAAIhB,KAAKC,SAAU9B,YAAY,IAAM6B,KAAKiB,GAAGC,eAItDI,YAActB,KAAKuB,cAAcvB,KAAKwB,iBACxCF,cAAgBJ,mBAIdO,MAAQP,MAAQI,YAtJP,OACA,YAuJVf,OAAOkB,MAAON,MAAMD,QAG3BQ,UACM1B,KAAKJ,mBACFA,aAAa8B,gBAGdA,UAIRC,kBAAkBpC,eAChBA,OAAOqC,gBAAkBrC,OAAOX,SACzBW,OAGTW,qBACMF,KAAKG,QAAQtB,gCACFgD,GAAG7B,KAAKC,SAAU7B,eAAe0D,OAAS9B,KAAK+B,SAASD,SAG5C,UAAvB9B,KAAKG,QAAQrB,8BACF+C,GAAG7B,KAAKC,SAAU5B,kBAAkB,IAAM2B,KAAKlB,gCAC/C+C,GAAG7B,KAAKC,SAAU3B,kBAAkB,IAAM0B,KAAKe,uBAG1Df,KAAKG,QAAQnB,OAASgD,eAAMC,oBACzBC,0BAITA,8BACO,MAAMC,OAAOrC,wBAAesC,KAhKX,qBAgKmCpC,KAAKC,gCAC/C4B,GAAGM,IAAK5D,kBAAkBuD,OAASA,MAAMO,yBAwBlDC,YAAc,CAClBC,aAAc,IAAMvC,KAAKO,OAAOP,KAAKwC,kBAjNpB,SAkNjBC,cAAe,IAAMzC,KAAKO,OAAOP,KAAKwC,kBAjNpB,UAkNlBE,YAxBkB,KACS,UAAvB1C,KAAKG,QAAQrB,aAYZA,QACDkB,KAAKL,cACPgD,aAAa3C,KAAKL,mBAGfA,aAAeiD,YAAW,IAAM5C,KAAKe,qBAjNjB,IAiN+Df,KAAKG,QAAQvB,kBASlGgB,aAAe,IAAIoC,eAAMhC,KAAKC,SAAUqC,aAG/CP,SAASD,UACH,kBAAkBe,KAAKf,MAAMgB,OAAOC,sBAIlCC,UAAYtE,iBAAiBoD,MAAMmB,KACrCD,YACFlB,MAAMO,sBACD9B,OAAOP,KAAKwC,kBAAkBQ,aAIvCzB,cAAcjC,gBACLU,KAAKoB,YAAY8B,QAAQ5D,SAGlC6D,2BAA2BjC,WACpBlB,KAAKH,gCAIJuD,gBAAkBtD,wBAAeC,QA1NnB,UA0N4CC,KAAKH,oBAErEuD,gBAAgBC,UAAUC,OAnOJ,UAoOtBF,gBAAgBG,gBAAgB,sBAE1BC,mBAAqB1D,wBAAeC,qCAA8BmB,YAAWlB,KAAKH,oBAEpF2D,qBACFA,mBAAmBH,UAAUI,IAzOT,UA0OpBD,mBAAmBE,aAAa,eAAgB,SAIpD7C,wBACQvB,QAAUU,KAAKP,gBAAkBO,KAAKwB,iBAEvClC,qBAICqE,gBAAkBC,OAAOC,SAASvE,QAAQwE,aAAa,oBAAqB,SAE7E3D,QAAQvB,SAAW+E,iBAAmB3D,KAAKG,QAAQyB,gBAG1DrB,OAAOkB,WAAOnC,+DAAU,QAClBU,KAAKN,wBAIHqE,cAAgB/D,KAAKwB,aACrBwC,OA/QS,SA+QAvC,MACTwC,YAAc3E,UAAW,+BAAqBU,KAAKoB,YAAa2C,cAAeC,OAAQhE,KAAKG,QAAQlB,SAEtGgF,cAAgBF,2BAIdG,iBAAmBlE,KAAKuB,cAAc0C,aAEtCE,aAAeC,WACZC,sBAAaC,QAAQtE,KAAKC,SAAUmE,UAAW,CACpDG,cAAeN,YACfjB,UAAWhD,KAAKwE,kBAAkB/C,OAClCgD,KAAMzE,KAAKuB,cAAcwC,eACzB9C,GAAIiD,sBAIWC,aAAajG,aAEjBwG,4BAIVX,gBAAkBE,yBAMjBU,UAAYC,QAAQ5E,KAAKR,gBAC1BV,aAEAY,YAAa,OAEbyD,2BAA2Be,uBAC3BzE,eAAiBwE,kBAEhBY,qBAAuBb,OAnSR,sBADF,oBAqSbc,eAAiBd,OAnSH,qBACA,qBAoSpBC,YAAYZ,UAAUI,IAAIqB,kCAEnBb,aAEPF,cAAcV,UAAUI,IAAIoB,sBAC5BZ,YAAYZ,UAAUI,IAAIoB,2BAarBE,gBAXoB,KACvBd,YAAYZ,UAAUC,OAAOuB,qBAAsBC,gBACnDb,YAAYZ,UAAUI,IAlTF,UAoTpBM,cAAcV,UAAUC,OApTJ,SAoT8BwB,eAAgBD,2BAE7DnF,YAAa,EAElByE,aAAahG,cAGuB4F,cAAe/D,KAAKgF,eAEtDL,gBACGvE,QAIT4E,qBACShF,KAAKC,SAASoD,UAAU4B,SAlUV,SAqUvBzD,oBACS1B,wBAAeC,QA9TGmF,wBA8T2BlF,KAAKC,UAG3DmB,mBACStB,wBAAesC,KAnUJ,iBAmUwBpC,KAAKC,UAGjDW,iBACMZ,KAAKR,YACP2F,cAAcnF,KAAKR,gBACdA,UAAY,MAIrBgD,kBAAkBQ,kBACZ,kBAnWe,SAoWVA,UArWM,OADA,OAEI,SAuWZA,UAzWQ,OACA,OA2WjBwB,kBAAkB/C,cACZ,kBA5WW,SA6WNA,MA5WU,OACC,QAFL,SAgXRA,MA9Wa,QADD,8BAmXElC,eACdS,KAAKoF,MAAK,iBACTC,KAAOlG,SAASmG,oBAAoBtF,KAAMT,WAE1B,iBAAXA,WAKW,iBAAXA,OAAqB,SACTgG,IAAjBF,KAAK9F,SAAyBA,OAAOiG,WAAW,MAAmB,gBAAXjG,aACpD,IAAIkG,qCAA8BlG,aAG1C8F,KAAK9F,gBATL8F,KAAKpE,GAAG1B,kCAmBHsC,GAAGpB,SAAUhC,qBAlXE,uCAkXyC,SAAUqD,aACvEgB,OAAShD,wBAAe4F,uBAAuB1F,UAEhD8C,SAAWA,OAAOO,UAAU4B,SAlYP,mBAsY1BnD,MAAMO,uBAEAsD,SAAWxG,SAASmG,oBAAoBxC,QACxC8C,WAAa5F,KAAK8D,aAAa,2BAEjC8B,YACFD,SAAS1E,GAAG2E,iBACZD,SAAS5E,qBAIyC,SAAhD8E,qBAAYC,iBAAiB9F,KAAM,UACrC2F,SAASrF,YACTqF,SAAS5E,sBAIX4E,SAAShF,YACTgF,SAAS5E,8CAGEc,GAAGkE,OAAQvH,qBAAqB,WACrCwH,UAAYlG,wBAAesC,KA9YR,iCAgZpB,MAAMuD,YAAYK,UACrB7G,SAASmG,oBAAoBK,2CAQdxG,uBAEJA"}