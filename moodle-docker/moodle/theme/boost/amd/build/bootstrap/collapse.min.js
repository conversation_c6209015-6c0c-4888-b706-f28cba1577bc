define("theme_boost/bootstrap/collapse",["exports","./base-component","./dom/event-handler","./dom/selector-engine","./util/index"],(function(_exports,_baseComponent,_eventHandler,_selectorEngine,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler),_selectorEngine=_interopRequireDefault(_selectorEngine);const EVENT_KEY=".".concat("bs.collapse"),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),CLASS_NAME_DEEPER_CHILDREN=":scope .".concat("collapse"," .").concat("collapse"),Default={parent:null,toggle:!0},DefaultType={parent:"(null|element)",toggle:"boolean"};class Collapse extends _baseComponent.default{constructor(element,config){super(element,config),this._isTransitioning=!1,this._triggerArray=[];const toggleList=_selectorEngine.default.find('[data-bs-toggle="collapse"]');for(const elem of toggleList){const selector=_selectorEngine.default.getSelectorFromElement(elem),filterElement=_selectorEngine.default.find(selector).filter((foundElement=>foundElement===this._element));null!==selector&&filterElement.length&&this._triggerArray.push(elem)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let activeChildren=[];if(this._config.parent&&(activeChildren=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((element=>element!==this._element)).map((element=>Collapse.getOrCreateInstance(element,{toggle:!1})))),activeChildren.length&&activeChildren[0]._isTransitioning)return;if(_eventHandler.default.trigger(this._element,EVENT_SHOW).defaultPrevented)return;for(const activeInstance of activeChildren)activeInstance.hide();const dimension=this._getDimension();this._element.classList.remove("collapse"),this._element.classList.add("collapsing"),this._element.style[dimension]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const capitalizedDimension=dimension[0].toUpperCase()+dimension.slice(1),scrollSize="scroll".concat(capitalizedDimension);this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove("collapsing"),this._element.classList.add("collapse","show"),this._element.style[dimension]="",_eventHandler.default.trigger(this._element,EVENT_SHOWN)}),this._element,!0),this._element.style[dimension]="".concat(this._element[scrollSize],"px")}hide(){if(this._isTransitioning||!this._isShown())return;if(_eventHandler.default.trigger(this._element,EVENT_HIDE).defaultPrevented)return;const dimension=this._getDimension();this._element.style[dimension]="".concat(this._element.getBoundingClientRect()[dimension],"px"),(0,_index.reflow)(this._element),this._element.classList.add("collapsing"),this._element.classList.remove("collapse","show");for(const trigger of this._triggerArray){const element=_selectorEngine.default.getElementFromSelector(trigger);element&&!this._isShown(element)&&this._addAriaAndCollapsedClass([trigger],!1)}this._isTransitioning=!0;this._element.style[dimension]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove("collapsing"),this._element.classList.add("collapse"),_eventHandler.default.trigger(this._element,EVENT_HIDDEN)}),this._element,!0)}_isShown(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._element).classList.contains("show")}_configAfterMerge(config){return config.toggle=Boolean(config.toggle),config.parent=(0,_index.getElement)(config.parent),config}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const children=this._getFirstLevelChildren('[data-bs-toggle="collapse"]');for(const element of children){const selected=_selectorEngine.default.getElementFromSelector(element);selected&&this._addAriaAndCollapsedClass([element],this._isShown(selected))}}_getFirstLevelChildren(selector){const children=_selectorEngine.default.find(CLASS_NAME_DEEPER_CHILDREN,this._config.parent);return _selectorEngine.default.find(selector,this._config.parent).filter((element=>!children.includes(element)))}_addAriaAndCollapsedClass(triggerArray,isOpen){if(triggerArray.length)for(const element of triggerArray)element.classList.toggle("collapsed",!isOpen),element.setAttribute("aria-expanded",isOpen)}static jQueryInterface(config){const _config={};return"string"==typeof config&&/show|hide/.test(config)&&(_config.toggle=!1),this.each((function(){const data=Collapse.getOrCreateInstance(this,_config);if("string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}_eventHandler.default.on(document,EVENT_CLICK_DATA_API,'[data-bs-toggle="collapse"]',(function(event){("A"===event.target.tagName||event.delegateTarget&&"A"===event.delegateTarget.tagName)&&event.preventDefault();for(const element of _selectorEngine.default.getMultipleElementsFromSelector(this))Collapse.getOrCreateInstance(element,{toggle:!1}).toggle()})),(0,_index.defineJQueryPlugin)(Collapse);var _default=Collapse;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=collapse.min.js.map