{"version": 3, "file": "collapse.min.js", "sources": ["../../src/bootstrap/collapse.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport {\n  defineJQueryPlugin,\n  getElement,\n  reflow\n} from './util/index'\n\n/**\n * Constants\n */\n\nconst NAME = 'collapse'\nconst DATA_KEY = 'bs.collapse'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_COLLAPSE = 'collapse'\nconst CLASS_NAME_COLLAPSING = 'collapsing'\nconst CLASS_NAME_COLLAPSED = 'collapsed'\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal'\n\nconst WIDTH = 'width'\nconst HEIGHT = 'height'\n\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"collapse\"]'\n\nconst Default = {\n  parent: null,\n  toggle: true\n}\n\nconst DefaultType = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isTransitioning = false\n    this._triggerArray = []\n\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE)\n\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem)\n      const filterElement = SelectorEngine.find(selector)\n        .filter(foundElement => foundElement === this._element)\n\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem)\n      }\n    }\n\n    this._initializeChildren()\n\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown())\n    }\n\n    if (this._config.toggle) {\n      this.toggle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide()\n    } else {\n      this.show()\n    }\n  }\n\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return\n    }\n\n    let activeChildren = []\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES)\n        .filter(element => element !== this._element)\n        .map(element => Collapse.getOrCreateInstance(element, { toggle: false }))\n    }\n\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide()\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.classList.remove(CLASS_NAME_COLLAPSE)\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n\n    this._element.style[dimension] = 0\n\n    this._addAriaAndCollapsedClass(this._triggerArray, true)\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n      this._element.style[dimension] = ''\n\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n    }\n\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n    const scrollSize = `scroll${capitalizedDimension}`\n\n    this._queueCallback(complete, this._element, true)\n    this._element.style[dimension] = `${this._element[scrollSize]}px`\n  }\n\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return\n    }\n\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n    if (startEvent.defaultPrevented) {\n      return\n    }\n\n    const dimension = this._getDimension()\n\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_COLLAPSING)\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW)\n\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger)\n\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false)\n      }\n    }\n\n    this._isTransitioning = true\n\n    const complete = () => {\n      this._isTransitioning = false\n      this._element.classList.remove(CLASS_NAME_COLLAPSING)\n      this._element.classList.add(CLASS_NAME_COLLAPSE)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.style[dimension] = ''\n\n    this._queueCallback(complete, this._element, true)\n  }\n\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle) // Coerce string values\n    config.parent = getElement(config.parent)\n    return config\n  }\n\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT\n  }\n\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return\n    }\n\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE)\n\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element)\n\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected))\n      }\n    }\n  }\n\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent)\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element))\n  }\n\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return\n    }\n\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen)\n      element.setAttribute('aria-expanded', isOpen)\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {}\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false\n    }\n\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config]()\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || (event.delegateTarget && event.delegateTarget.tagName === 'A')) {\n    event.preventDefault()\n  }\n\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, { toggle: false }).toggle()\n  }\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse)\n\nexport default Collapse\n"], "names": ["EVENT_KEY", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_CLICK_DATA_API", "CLASS_NAME_DEEPER_CHILDREN", "<PERSON><PERSON><PERSON>", "parent", "toggle", "DefaultType", "Collapse", "BaseComponent", "constructor", "element", "config", "_isTransitioning", "_triggerArray", "toggleList", "SelectorEngine", "find", "elem", "selector", "getSelectorFromElement", "filterElement", "filter", "foundElement", "this", "_element", "length", "push", "_initializeC<PERSON><PERSON>n", "_config", "_addAriaAndCollapsedClass", "_isShown", "NAME", "hide", "show", "activeC<PERSON><PERSON>n", "_getFirstLevelChildren", "map", "getOrCreateInstance", "EventHandler", "trigger", "defaultPrevented", "activeInstance", "dimension", "_getDimension", "classList", "remove", "add", "style", "capitalizedDimension", "toUpperCase", "slice", "scrollSize", "_queueCallback", "getBoundingClientRect", "getElementFromSelector", "contains", "_configAfterMerge", "Boolean", "children", "selected", "includes", "trigger<PERSON><PERSON>y", "isOpen", "setAttribute", "test", "each", "data", "TypeError", "on", "document", "event", "target", "tagName", "<PERSON><PERSON><PERSON><PERSON>", "preventDefault", "getMultipleElementsFromSelector"], "mappings": "shBAsBMA,qBADW,eAIXC,yBAAoBD,WACpBE,2BAAsBF,WACtBG,yBAAoBH,WACpBI,6BAAwBJ,WACxBK,oCAA+BL,kBANhB,aAYfM,6CAHsB,wBAAA,YAYtBC,QAAU,CACdC,OAAQ,KACRC,QAAQ,GAGJC,YAAc,CAClBF,OAAQ,iBACRC,OAAQ,iBAOJE,iBAAiBC,uBACrBC,YAAYC,QAASC,cACbD,QAASC,aAEVC,kBAAmB,OACnBC,cAAgB,SAEfC,WAAaC,wBAAeC,KAvBT,mCAyBpB,MAAMC,QAAQH,WAAY,OACvBI,SAAWH,wBAAeI,uBAAuBF,MACjDG,cAAgBL,wBAAeC,KAAKE,UACvCG,QAAOC,cAAgBA,eAAiBC,KAAKC,WAE/B,OAAbN,UAAqBE,cAAcK,aAChCZ,cAAca,KAAKT,WAIvBU,sBAEAJ,KAAKK,QAAQxB,aACXyB,0BAA0BN,KAAKV,cAAeU,KAAKO,YAGtDP,KAAKK,QAAQvB,aACVA,SAKEF,4BACFA,QAGEG,gCACFA,YAGEyB,wBA7EA,WAkFX1B,SACMkB,KAAKO,gBACFE,YAEAC,OAITA,UACMV,KAAKX,kBAAoBW,KAAKO,sBAI9BI,eAAiB,MAGjBX,KAAKK,QAAQxB,SACf8B,eAAiBX,KAAKY,uBA9EH,wCA+EhBd,QAAOX,SAAWA,UAAYa,KAAKC,WACnCY,KAAI1B,SAAWH,SAAS8B,oBAAoB3B,QAAS,CAAEL,QAAQ,OAGhE6B,eAAeT,QAAUS,eAAe,GAAGtB,2BAI5B0B,sBAAaC,QAAQhB,KAAKC,SAAU3B,YACxC2C,4BAIV,MAAMC,kBAAkBP,eAC3BO,eAAeT,aAGXU,UAAYnB,KAAKoB,qBAElBnB,SAASoB,UAAUC,OA3GA,iBA4GnBrB,SAASoB,UAAUE,IA3GE,mBA6GrBtB,SAASuB,MAAML,WAAa,OAE5Bb,0BAA0BN,KAAKV,eAAe,QAC9CD,kBAAmB,QAalBoC,qBAAuBN,UAAU,GAAGO,cAAgBP,UAAUQ,MAAM,GACpEC,2BAAsBH,2BAEvBI,gBAdY,UACVxC,kBAAmB,OAEnBY,SAASoB,UAAUC,OArHA,mBAsHnBrB,SAASoB,UAAUE,IAvHF,WADJ,aA0HbtB,SAASuB,MAAML,WAAa,yBAEpBH,QAAQhB,KAAKC,SAAU1B,eAMRyB,KAAKC,UAAU,QACxCA,SAASuB,MAAML,qBAAgBnB,KAAKC,SAAS2B,kBAGpDnB,UACMT,KAAKX,mBAAqBW,KAAKO,qBAIhBQ,sBAAaC,QAAQhB,KAAKC,SAAUzB,YACxCyC,8BAITE,UAAYnB,KAAKoB,qBAElBnB,SAASuB,MAAML,qBAAgBnB,KAAKC,SAAS6B,wBAAwBX,mCAEnEnB,KAAKC,eAEPA,SAASoB,UAAUE,IApJE,mBAqJrBtB,SAASoB,UAAUC,OAtJA,WADJ,YAyJf,MAAMN,WAAWhB,KAAKV,cAAe,OAClCH,QAAUK,wBAAeuC,uBAAuBf,SAElD7B,UAAYa,KAAKO,SAASpB,eACvBmB,0BAA0B,CAACU,UAAU,QAIzC3B,kBAAmB,OASnBY,SAASuB,MAAML,WAAa,QAE5BU,gBATY,UACVxC,kBAAmB,OACnBY,SAASoB,UAAUC,OAnKA,mBAoKnBrB,SAASoB,UAAUE,IArKF,kCAsKTP,QAAQhB,KAAKC,SAAUxB,gBAKRuB,KAAKC,UAAU,GAG/CM,yEAAmBP,KAAKC,UACPoB,UAAUW,SAhLL,QAoLtBC,kBAAkB7C,eAChBA,OAAON,OAASoD,QAAQ9C,OAAON,QAC/BM,OAAOP,QAAS,qBAAWO,OAAOP,QAC3BO,OAGTgC,uBACSpB,KAAKC,SAASoB,UAAUW,SAtLL,uBAEhB,QACC,SAsLb5B,0BACOJ,KAAKK,QAAQxB,oBAIZsD,SAAWnC,KAAKY,uBAxLG,mCA0LpB,MAAMzB,WAAWgD,SAAU,OACxBC,SAAW5C,wBAAeuC,uBAAuB5C,SAEnDiD,eACG9B,0BAA0B,CAACnB,SAAUa,KAAKO,SAAS6B,YAK9DxB,uBAAuBjB,gBACfwC,SAAW3C,wBAAeC,KAAKd,2BAA4BqB,KAAKK,QAAQxB,eAEvEW,wBAAeC,KAAKE,SAAUK,KAAKK,QAAQxB,QAAQiB,QAAOX,UAAYgD,SAASE,SAASlD,WAGjGmB,0BAA0BgC,aAAcC,WACjCD,aAAapC,WAIb,MAAMf,WAAWmD,aACpBnD,QAAQkC,UAAUvC,OAvNK,aAuNyByD,QAChDpD,QAAQqD,aAAa,gBAAiBD,+BAKnBnD,cACfiB,QAAU,SACM,iBAAXjB,QAAuB,YAAYqD,KAAKrD,UACjDiB,QAAQvB,QAAS,GAGZkB,KAAK0C,MAAK,iBACTC,KAAO3D,SAAS8B,oBAAoBd,KAAMK,YAE1B,iBAAXjB,OAAqB,SACF,IAAjBuD,KAAKvD,cACR,IAAIwD,qCAA8BxD,aAG1CuD,KAAKvD,qCAUAyD,GAAGC,SAAUpE,qBA7OG,+BA6OyC,SAAUqE,QAEjD,MAAzBA,MAAMC,OAAOC,SAAoBF,MAAMG,gBAAmD,MAAjCH,MAAMG,eAAeD,UAChFF,MAAMI,qBAGH,MAAMhE,WAAWK,wBAAe4D,gCAAgCpD,MACnEhB,SAAS8B,oBAAoB3B,QAAS,CAAEL,QAAQ,IAASA,0CAQ1CE,uBAEJA"}