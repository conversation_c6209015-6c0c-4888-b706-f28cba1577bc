define("theme_boost/bootstrap/dom/data",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;const elementMap=new Map;var _default={set(element,key,instance){elementMap.has(element)||elementMap.set(element,new Map);const instanceMap=elementMap.get(element);instanceMap.has(key)||0===instanceMap.size?instanceMap.set(key,instance):console.error("Bootstrap doesn't allow more than one instance per element. Bound instance: ".concat(Array.from(instanceMap.keys())[0],"."))},get:(element,key)=>elementMap.has(element)&&elementMap.get(element).get(key)||null,remove(element,key){if(!elementMap.has(element))return;const instanceMap=elementMap.get(element);instanceMap.delete(key),0===instanceMap.size&&elementMap.delete(element)}};return _exports.default=_default,_exports.default}));

//# sourceMappingURL=data.min.js.map