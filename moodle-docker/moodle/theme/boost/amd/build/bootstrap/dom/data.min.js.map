{"version": 3, "file": "data.min.js", "sources": ["../../../src/bootstrap/dom/data.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map()\n\nexport default {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map())\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`)\n      return\n    }\n\n    instanceMap.set(key, instance)\n  },\n\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null\n    }\n\n    return null\n  },\n\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return\n    }\n\n    const instanceMap = elementMap.get(element)\n\n    instanceMap.delete(key)\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element)\n    }\n  }\n}\n"], "names": ["elementMap", "Map", "set", "element", "key", "instance", "has", "instanceMap", "get", "size", "console", "error", "Array", "from", "keys", "remove", "delete"], "mappings": "8JAWMA,WAAa,IAAIC,iBAER,CACbC,IAAIC,QAASC,IAAKC,UACXL,WAAWM,IAAIH,UAClBH,WAAWE,IAAIC,QAAS,IAAIF,WAGxBM,YAAcP,WAAWQ,IAAIL,SAI9BI,YAAYD,IAAIF,MAA6B,IAArBG,YAAYE,KAMzCF,YAAYL,IAAIE,IAAKC,UAJnBK,QAAQC,4FAAqFC,MAAMC,KAAKN,YAAYO,QAAQ,UAOhIN,IAAG,CAACL,QAASC,MACPJ,WAAWM,IAAIH,UACVH,WAAWQ,IAAIL,SAASK,IAAIJ,MAG9B,KAGTW,OAAOZ,QAASC,SACTJ,WAAWM,IAAIH,sBAIdI,YAAcP,WAAWQ,IAAIL,SAEnCI,YAAYS,OAAOZ,KAGM,IAArBG,YAAYE,MACdT,WAAWgB,OAAOb"}