define("theme_boost/bootstrap/dom/event-handler",["exports","../util/index"],(function(_exports,_index){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;const namespaceRegex=/[^.]*(?=\..*)\.|.*/,stripNameRegex=/\..*/,stripUidRegex=/::\d+$/,eventRegistry={};let uidEvent=1;const customEvents={mouseenter:"mouseover",mouseleave:"mouseout"},nativeEvents=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function makeEventUid(element,uid){return uid&&"".concat(uid,"::").concat(uidEvent++)||element.uidEvent||uidEvent++}function getElementEvents(element){const uid=makeEventUid(element);return element.uidEvent=uid,eventRegistry[uid]=eventRegistry[uid]||{},eventRegistry[uid]}function findHandler(events,callable){let delegationSelector=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return Object.values(events).find((event=>event.callable===callable&&event.delegationSelector===delegationSelector))}function normalizeParameters(originalTypeEvent,handler,delegationFunction){const isDelegated="string"==typeof handler,callable=isDelegated?delegationFunction:handler||delegationFunction;let typeEvent=getTypeEvent(originalTypeEvent);return nativeEvents.has(typeEvent)||(typeEvent=originalTypeEvent),[isDelegated,callable,typeEvent]}function addHandler(element,originalTypeEvent,handler,delegationFunction,oneOff){if("string"!=typeof originalTypeEvent||!element)return;let[isDelegated,callable,typeEvent]=normalizeParameters(originalTypeEvent,handler,delegationFunction);if(originalTypeEvent in customEvents){const wrapFunction=fn=>function(event){if(!event.relatedTarget||event.relatedTarget!==event.delegateTarget&&!event.delegateTarget.contains(event.relatedTarget))return fn.call(this,event)};callable=wrapFunction(callable)}const events=getElementEvents(element),handlers=events[typeEvent]||(events[typeEvent]={}),previousFunction=findHandler(handlers,callable,isDelegated?handler:null);if(previousFunction)return void(previousFunction.oneOff=previousFunction.oneOff&&oneOff);const uid=makeEventUid(callable,originalTypeEvent.replace(namespaceRegex,"")),fn=isDelegated?function(element,selector,fn){return function handler(event){const domElements=element.querySelectorAll(selector);for(let{target:target}=event;target&&target!==this;target=target.parentNode)for(const domElement of domElements)if(domElement===target)return hydrateObj(event,{delegateTarget:target}),handler.oneOff&&EventHandler.off(element,event.type,selector,fn),fn.apply(target,[event])}}(element,handler,callable):function(element,fn){return function handler(event){return hydrateObj(event,{delegateTarget:element}),handler.oneOff&&EventHandler.off(element,event.type,fn),fn.apply(element,[event])}}(element,callable);fn.delegationSelector=isDelegated?handler:null,fn.callable=callable,fn.oneOff=oneOff,fn.uidEvent=uid,handlers[uid]=fn,element.addEventListener(typeEvent,fn,isDelegated)}function removeHandler(element,events,typeEvent,handler,delegationSelector){const fn=findHandler(events[typeEvent],handler,delegationSelector);fn&&(element.removeEventListener(typeEvent,fn,Boolean(delegationSelector)),delete events[typeEvent][fn.uidEvent])}function removeNamespacedHandlers(element,events,typeEvent,namespace){const storeElementEvent=events[typeEvent]||{};for(const[handlerKey,event]of Object.entries(storeElementEvent))handlerKey.includes(namespace)&&removeHandler(element,events,typeEvent,event.callable,event.delegationSelector)}function getTypeEvent(event){return event=event.replace(stripNameRegex,""),customEvents[event]||event}const EventHandler={on(element,event,handler,delegationFunction){addHandler(element,event,handler,delegationFunction,!1)},one(element,event,handler,delegationFunction){addHandler(element,event,handler,delegationFunction,!0)},off(element,originalTypeEvent,handler,delegationFunction){if("string"!=typeof originalTypeEvent||!element)return;const[isDelegated,callable,typeEvent]=normalizeParameters(originalTypeEvent,handler,delegationFunction),inNamespace=typeEvent!==originalTypeEvent,events=getElementEvents(element),storeElementEvent=events[typeEvent]||{},isNamespace=originalTypeEvent.startsWith(".");if(void 0===callable){if(isNamespace)for(const elementEvent of Object.keys(events))removeNamespacedHandlers(element,events,elementEvent,originalTypeEvent.slice(1));for(const[keyHandlers,event]of Object.entries(storeElementEvent)){const handlerKey=keyHandlers.replace(stripUidRegex,"");inNamespace&&!originalTypeEvent.includes(handlerKey)||removeHandler(element,events,typeEvent,event.callable,event.delegationSelector)}}else{if(!Object.keys(storeElementEvent).length)return;removeHandler(element,events,typeEvent,callable,isDelegated?handler:null)}},trigger(element,event,args){if("string"!=typeof event||!element)return null;const $=(0,_index.getjQuery)();let jQueryEvent=null,bubbles=!0,nativeDispatch=!0,defaultPrevented=!1;event!==getTypeEvent(event)&&$&&(jQueryEvent=$.Event(event,args),$(element).trigger(jQueryEvent),bubbles=!jQueryEvent.isPropagationStopped(),nativeDispatch=!jQueryEvent.isImmediatePropagationStopped(),defaultPrevented=jQueryEvent.isDefaultPrevented());const evt=hydrateObj(new Event(event,{bubbles:bubbles,cancelable:!0}),args);return defaultPrevented&&evt.preventDefault(),nativeDispatch&&element.dispatchEvent(evt),evt.defaultPrevented&&jQueryEvent&&jQueryEvent.preventDefault(),evt}};function hydrateObj(obj){let meta=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(const[key,value]of Object.entries(meta))try{obj[key]=value}catch{Object.defineProperty(obj,key,{configurable:!0,get:()=>value})}return obj}var _default=EventHandler;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=event-handler.min.js.map