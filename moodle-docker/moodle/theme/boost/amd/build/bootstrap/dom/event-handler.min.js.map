{"version": 3, "file": "event-handler.min.js", "sources": ["../../../src/bootstrap/dom/event-handler.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { getjQuery } from '../util/index'\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/\nconst stripNameRegex = /\\..*/\nconst stripUidRegex = /::\\d+$/\nconst eventRegistry = {} // Events storage\nlet uidEvent = 1\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n}\n\nconst nativeEvents = new Set([\n  'click',\n  'dblclick',\n  'mouseup',\n  'mousedown',\n  'contextmenu',\n  'mousewheel',\n  'DOMMouseScroll',\n  'mouseover',\n  'mouseout',\n  'mousemove',\n  'selectstart',\n  'selectend',\n  'keydown',\n  'keypress',\n  'keyup',\n  'orientationchange',\n  'touchstart',\n  'touchmove',\n  'touchend',\n  'touchcancel',\n  'pointerdown',\n  'pointermove',\n  'pointerup',\n  'pointerleave',\n  'pointercancel',\n  'gesturestart',\n  'gesturechange',\n  'gestureend',\n  'focus',\n  'blur',\n  'change',\n  'reset',\n  'select',\n  'submit',\n  'focusin',\n  'focusout',\n  'load',\n  'unload',\n  'beforeunload',\n  'resize',\n  'move',\n  'DOMContentLoaded',\n  'readystatechange',\n  'error',\n  'abort',\n  'scroll'\n])\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return (uid && `${uid}::${uidEvent++}`) || element.uidEvent || uidEvent++\n}\n\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element)\n\n  element.uidEvent = uid\n  eventRegistry[uid] = eventRegistry[uid] || {}\n\n  return eventRegistry[uid]\n}\n\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, { delegateTarget: element })\n\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn)\n    }\n\n    return fn.apply(element, [event])\n  }\n}\n\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector)\n\n    for (let { target } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue\n        }\n\n        hydrateObj(event, { delegateTarget: target })\n\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn)\n        }\n\n        return fn.apply(target, [event])\n      }\n    }\n  }\n}\n\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events)\n    .find(event => event.callable === callable && event.delegationSelector === delegationSelector)\n}\n\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string'\n  // TODO: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : (handler || delegationFunction)\n  let typeEvent = getTypeEvent(originalTypeEvent)\n\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent\n  }\n\n  return [isDelegated, callable, typeEvent]\n}\n\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return\n  }\n\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || (event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget))) {\n          return fn.call(this, event)\n        }\n      }\n    }\n\n    callable = wrapFunction(callable)\n  }\n\n  const events = getElementEvents(element)\n  const handlers = events[typeEvent] || (events[typeEvent] = {})\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null)\n\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff\n\n    return\n  }\n\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''))\n  const fn = isDelegated ?\n    bootstrapDelegationHandler(element, handler, callable) :\n    bootstrapHandler(element, callable)\n\n  fn.delegationSelector = isDelegated ? handler : null\n  fn.callable = callable\n  fn.oneOff = oneOff\n  fn.uidEvent = uid\n  handlers[uid] = fn\n\n  element.addEventListener(typeEvent, fn, isDelegated)\n}\n\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector)\n\n  if (!fn) {\n    return\n  }\n\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector))\n  delete events[typeEvent][fn.uidEvent]\n}\n\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {}\n\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n    }\n  }\n}\n\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '')\n  return customEvents[event] || event\n}\n\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false)\n  },\n\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true)\n  },\n\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return\n    }\n\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction)\n    const inNamespace = typeEvent !== originalTypeEvent\n    const events = getElementEvents(element)\n    const storeElementEvent = events[typeEvent] || {}\n    const isNamespace = originalTypeEvent.startsWith('.')\n\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return\n      }\n\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null)\n      return\n    }\n\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1))\n      }\n    }\n\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '')\n\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector)\n      }\n    }\n  },\n\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null\n    }\n\n    const $ = getjQuery()\n    const typeEvent = getTypeEvent(event)\n    const inNamespace = event !== typeEvent\n\n    let jQueryEvent = null\n    let bubbles = true\n    let nativeDispatch = true\n    let defaultPrevented = false\n\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args)\n\n      $(element).trigger(jQueryEvent)\n      bubbles = !jQueryEvent.isPropagationStopped()\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped()\n      defaultPrevented = jQueryEvent.isDefaultPrevented()\n    }\n\n    const evt = hydrateObj(new Event(event, { bubbles, cancelable: true }), args)\n\n    if (defaultPrevented) {\n      evt.preventDefault()\n    }\n\n    if (nativeDispatch) {\n      element.dispatchEvent(evt)\n    }\n\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault()\n    }\n\n    return evt\n  }\n}\n\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value\n    } catch {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value\n        }\n      })\n    }\n  }\n\n  return obj\n}\n\nexport default EventHandler\n"], "names": ["namespaceRegex", "stripNameRegex", "stripUidRegex", "eventRegistry", "uidEvent", "customEvents", "mouseenter", "mouseleave", "nativeEvents", "Set", "makeEventUid", "element", "uid", "getElementEvents", "<PERSON><PERSON><PERSON><PERSON>", "events", "callable", "delegationSelector", "Object", "values", "find", "event", "normalizeParameters", "originalTypeEvent", "handler", "delegationFunction", "isDelegated", "typeEvent", "getTypeEvent", "has", "add<PERSON><PERSON><PERSON>", "oneOff", "wrapFunction", "fn", "relatedTarget", "<PERSON><PERSON><PERSON><PERSON>", "contains", "call", "this", "handlers", "previousFunction", "replace", "selector", "dom<PERSON><PERSON>s", "querySelectorAll", "target", "parentNode", "dom<PERSON>lement", "hydrateObj", "EventHandler", "off", "type", "apply", "bootstrapDelegationHandler", "bootstrapHandler", "addEventListener", "<PERSON><PERSON><PERSON><PERSON>", "removeEventListener", "Boolean", "removeNamespacedHandlers", "namespace", "storeElementEvent", "handler<PERSON><PERSON>", "entries", "includes", "on", "one", "inNamespace", "isNamespace", "startsWith", "elementEvent", "keys", "slice", "keyHandlers", "length", "trigger", "args", "$", "jQueryEvent", "bubbles", "nativeDispatch", "defaultPrevented", "Event", "isPropagationStopped", "isImmediatePropagationStopped", "isDefaultPrevented", "evt", "cancelable", "preventDefault", "dispatchEvent", "obj", "meta", "key", "value", "defineProperty", "configurable", "get"], "mappings": "8LAaMA,eAAiB,qBACjBC,eAAiB,OACjBC,cAAgB,SAChBC,cAAgB,OAClBC,SAAW,QACTC,aAAe,CACnBC,WAAY,YACZC,WAAY,YAGRC,aAAe,IAAIC,IAAI,CAC3B,QACA,WACA,UACA,YACA,cACA,aACA,iBACA,YACA,WACA,YACA,cACA,YACA,UACA,WACA,QACA,oBACA,aACA,YACA,WACA,cACA,cACA,cACA,YACA,eACA,gBACA,eACA,gBACA,aACA,QACA,OACA,SACA,QACA,SACA,SACA,UACA,WACA,OACA,SACA,eACA,SACA,OACA,mBACA,mBACA,QACA,QACA,oBAOOC,aAAaC,QAASC,YACrBA,eAAUA,iBAAQR,aAAiBO,QAAQP,UAAYA,oBAGxDS,iBAAiBF,eAClBC,IAAMF,aAAaC,gBAEzBA,QAAQP,SAAWQ,IACnBT,cAAcS,KAAOT,cAAcS,MAAQ,GAEpCT,cAAcS,cAqCdE,YAAYC,OAAQC,cAAUC,0EAAqB,YACnDC,OAAOC,OAAOJ,QAClBK,MAAKC,OAASA,MAAML,WAAaA,UAAYK,MAAMJ,qBAAuBA,8BAGtEK,oBAAoBC,kBAAmBC,QAASC,0BACjDC,YAAiC,iBAAZF,QAErBR,SAAWU,YAAcD,mBAAsBD,SAAWC,uBAC5DE,UAAYC,aAAaL,0BAExBf,aAAaqB,IAAIF,aACpBA,UAAYJ,mBAGP,CAACG,YAAaV,SAAUW,oBAGxBG,WAAWnB,QAASY,kBAAmBC,QAASC,mBAAoBM,WAC1C,iBAAtBR,oBAAmCZ,mBAIzCe,YAAaV,SAAUW,WAAaL,oBAAoBC,kBAAmBC,QAASC,uBAIrFF,qBAAqBlB,aAAc,OAC/B2B,aAAeC,IACZ,SAAUZ,WACVA,MAAMa,eAAkBb,MAAMa,gBAAkBb,MAAMc,iBAAmBd,MAAMc,eAAeC,SAASf,MAAMa,sBACzGD,GAAGI,KAAKC,KAAMjB,QAK3BL,SAAWgB,aAAahB,gBAGpBD,OAASF,iBAAiBF,SAC1B4B,SAAWxB,OAAOY,aAAeZ,OAAOY,WAAa,IACrDa,iBAAmB1B,YAAYyB,SAAUvB,SAAUU,YAAcF,QAAU,SAE7EgB,6BACFA,iBAAiBT,OAASS,iBAAiBT,QAAUA,cAKjDnB,IAAMF,aAAaM,SAAUO,kBAAkBkB,QAAQzC,eAAgB,KACvEiC,GAAKP,qBAxEuBf,QAAS+B,SAAUT,WAC9C,SAAST,QAAQH,aAChBsB,YAAchC,QAAQiC,iBAAiBF,cAExC,IAAIG,OAAEA,QAAWxB,MAAOwB,QAAUA,SAAWP,KAAMO,OAASA,OAAOC,eACjE,MAAMC,cAAcJ,eACnBI,aAAeF,cAInBG,WAAW3B,MAAO,CAAEc,eAAgBU,SAEhCrB,QAAQO,QACVkB,aAAaC,IAAIvC,QAASU,MAAM8B,KAAMT,SAAUT,IAG3CA,GAAGmB,MAAMP,OAAQ,CAACxB,SAyD7BgC,CAA2B1C,QAASa,QAASR,mBArFvBL,QAASsB,WAC1B,SAAST,QAAQH,cACtB2B,WAAW3B,MAAO,CAAEc,eAAgBxB,UAEhCa,QAAQO,QACVkB,aAAaC,IAAIvC,QAASU,MAAM8B,KAAMlB,IAGjCA,GAAGmB,MAAMzC,QAAS,CAACU,SA8E1BiC,CAAiB3C,QAASK,UAE5BiB,GAAGhB,mBAAqBS,YAAcF,QAAU,KAChDS,GAAGjB,SAAWA,SACdiB,GAAGF,OAASA,OACZE,GAAG7B,SAAWQ,IACd2B,SAAS3B,KAAOqB,GAEhBtB,QAAQ4C,iBAAiB5B,UAAWM,GAAIP,sBAGjC8B,cAAc7C,QAASI,OAAQY,UAAWH,QAASP,0BACpDgB,GAAKnB,YAAYC,OAAOY,WAAYH,QAASP,oBAE9CgB,KAILtB,QAAQ8C,oBAAoB9B,UAAWM,GAAIyB,QAAQzC,4BAC5CF,OAAOY,WAAWM,GAAG7B,oBAGrBuD,yBAAyBhD,QAASI,OAAQY,UAAWiC,iBACtDC,kBAAoB9C,OAAOY,YAAc,OAE1C,MAAOmC,WAAYzC,SAAUH,OAAO6C,QAAQF,mBAC3CC,WAAWE,SAASJ,YACtBJ,cAAc7C,QAASI,OAAQY,UAAWN,MAAML,SAAUK,MAAMJ,6BAK7DW,aAAaP,cAEpBA,MAAQA,MAAMoB,QAAQxC,eAAgB,IAC/BI,aAAagB,QAAUA,YAG1B4B,aAAe,CACnBgB,GAAGtD,QAASU,MAAOG,QAASC,oBAC1BK,WAAWnB,QAASU,MAAOG,QAASC,oBAAoB,IAG1DyC,IAAIvD,QAASU,MAAOG,QAASC,oBAC3BK,WAAWnB,QAASU,MAAOG,QAASC,oBAAoB,IAG1DyB,IAAIvC,QAASY,kBAAmBC,QAASC,uBACN,iBAAtBF,oBAAmCZ,qBAIvCe,YAAaV,SAAUW,WAAaL,oBAAoBC,kBAAmBC,QAASC,oBACrF0C,YAAcxC,YAAcJ,kBAC5BR,OAASF,iBAAiBF,SAC1BkD,kBAAoB9C,OAAOY,YAAc,GACzCyC,YAAc7C,kBAAkB8C,WAAW,aAEzB,IAAbrD,aAUPoD,gBACG,MAAME,gBAAgBpD,OAAOqD,KAAKxD,QACrC4C,yBAAyBhD,QAASI,OAAQuD,aAAc/C,kBAAkBiD,MAAM,QAI/E,MAAOC,YAAapD,SAAUH,OAAO6C,QAAQF,mBAAoB,OAC9DC,WAAaW,YAAYhC,QAAQvC,cAAe,IAEjDiE,cAAe5C,kBAAkByC,SAASF,aAC7CN,cAAc7C,QAASI,OAAQY,UAAWN,MAAML,SAAUK,MAAMJ,8BAlB7DC,OAAOqD,KAAKV,mBAAmBa,cAIpClB,cAAc7C,QAASI,OAAQY,UAAWX,SAAUU,YAAcF,QAAU,QAmBhFmD,QAAQhE,QAASU,MAAOuD,SACD,iBAAVvD,QAAuBV,eACzB,WAGHkE,GAAI,0BAINC,YAAc,KACdC,SAAU,EACVC,gBAAiB,EACjBC,kBAAmB,EALH5D,QADFO,aAAaP,QAQZwD,IACjBC,YAAcD,EAAEK,MAAM7D,MAAOuD,MAE7BC,EAAElE,SAASgE,QAAQG,aACnBC,SAAWD,YAAYK,uBACvBH,gBAAkBF,YAAYM,gCAC9BH,iBAAmBH,YAAYO,4BAG3BC,IAAMtC,WAAW,IAAIkC,MAAM7D,MAAO,CAAE0D,QAAAA,QAASQ,YAAY,IAASX,aAEpEK,kBACFK,IAAIE,iBAGFR,gBACFrE,QAAQ8E,cAAcH,KAGpBA,IAAIL,kBAAoBH,aAC1BA,YAAYU,iBAGPF,eAIFtC,WAAW0C,SAAKC,4DAAO,OACzB,MAAOC,IAAKC,SAAU3E,OAAO6C,QAAQ4B,UAEtCD,IAAIE,KAAOC,MACX,MACA3E,OAAO4E,eAAeJ,IAAKE,IAAK,CAC9BG,cAAc,EACdC,IAAG,IACMH,eAMRH,iBAGMzC"}