define("theme_boost/bootstrap/dom/manipulator",["exports"],(function(_exports){function normalizeData(value){if("true"===value)return!0;if("false"===value)return!1;if(value===Number(value).toString())return Number(value);if(""===value||"null"===value)return null;if("string"!=typeof value)return value;try{return JSON.parse(decodeURIComponent(value))}catch{return value}}function normalizeDataKey(key){return key.replace(/[A-Z]/g,(chr=>"-".concat(chr.toLowerCase())))}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;var _default={setDataAttribute(element,key,value){element.setAttribute("data-bs-".concat(normalizeDataKey(key)),value)},removeDataAttribute(element,key){element.removeAttribute("data-bs-".concat(normalizeDataKey(key)))},getDataAttributes(element){if(!element)return{};const attributes={},bsKeys=Object.keys(element.dataset).filter((key=>key.startsWith("bs")&&!key.startsWith("bsConfig")));for(const key of bsKeys){let pureKey=key.replace(/^bs/,"");pureKey=pureKey.charAt(0).toLowerCase()+pureKey.slice(1,pureKey.length),attributes[pureKey]=normalizeData(element.dataset[key])}return attributes},getDataAttribute:(element,key)=>normalizeData(element.getAttribute("data-bs-".concat(normalizeDataKey(key))))};return _exports.default=_default,_exports.default}));

//# sourceMappingURL=manipulator.min.js.map