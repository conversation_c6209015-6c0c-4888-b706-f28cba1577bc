{"version": 3, "file": "manipulator.min.js", "sources": ["../../../src/bootstrap/dom/manipulator.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  if (value === Number(value).toString()) {\n    return Number(value)\n  }\n\n  if (value === '' || value === 'null') {\n    return null\n  }\n\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  try {\n    return JSON.parse(decodeURIComponent(value))\n  } catch {\n    return value\n  }\n}\n\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`)\n}\n\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value)\n  },\n\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`)\n  },\n\n  getDataAttributes(element) {\n    if (!element) {\n      return {}\n    }\n\n    const attributes = {}\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'))\n\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '')\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length)\n      attributes[pureKey] = normalizeData(element.dataset[key])\n    }\n\n    return attributes\n  },\n\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`))\n  }\n}\n\nexport default Manipulator\n"], "names": ["normalizeData", "value", "Number", "toString", "JSON", "parse", "decodeURIComponent", "normalizeDataKey", "key", "replace", "chr", "toLowerCase", "setDataAttribute", "element", "setAttribute", "removeDataAttribute", "removeAttribute", "getDataAttributes", "attributes", "bs<PERSON><PERSON>s", "Object", "keys", "dataset", "filter", "startsWith", "pureKey", "char<PERSON>t", "slice", "length", "getDataAttribute", "getAttribute"], "mappings": "wFAOSA,cAAcC,UACP,SAAVA,aACK,KAGK,UAAVA,aACK,KAGLA,QAAUC,OAAOD,OAAOE,kBACnBD,OAAOD,UAGF,KAAVA,OAA0B,SAAVA,aACX,QAGY,iBAAVA,aACFA,iBAIAG,KAAKC,MAAMC,mBAAmBL,QACrC,aACOA,gBAIFM,iBAAiBC,YACjBA,IAAIC,QAAQ,UAAUC,gBAAWA,IAAIC,8GAG1B,CAClBC,iBAAiBC,QAASL,IAAKP,OAC7BY,QAAQC,+BAAwBP,iBAAiBC,MAAQP,QAG3Dc,oBAAoBF,QAASL,KAC3BK,QAAQG,kCAA2BT,iBAAiBC,QAGtDS,kBAAkBJ,aACXA,cACI,SAGHK,WAAa,GACbC,OAASC,OAAOC,KAAKR,QAAQS,SAASC,QAAOf,KAAOA,IAAIgB,WAAW,QAAUhB,IAAIgB,WAAW,kBAE7F,MAAMhB,OAAOW,OAAQ,KACpBM,QAAUjB,IAAIC,QAAQ,MAAO,IACjCgB,QAAUA,QAAQC,OAAO,GAAGf,cAAgBc,QAAQE,MAAM,EAAGF,QAAQG,QACrEV,WAAWO,SAAWzB,cAAca,QAAQS,QAAQd,aAG/CU,YAGTW,iBAAgB,CAAChB,QAASL,MACjBR,cAAca,QAAQiB,+BAAwBvB,iBAAiBC"}