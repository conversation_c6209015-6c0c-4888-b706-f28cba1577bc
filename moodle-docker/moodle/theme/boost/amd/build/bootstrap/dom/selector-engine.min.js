define("theme_boost/bootstrap/dom/selector-engine",["exports","../util/index"],(function(_exports,_index){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0;const getSelector=element=>{let selector=element.getAttribute("data-bs-target");if(!selector||"#"===selector){let hrefAttribute=element.getAttribute("href");if(!hrefAttribute||!hrefAttribute.includes("#")&&!hrefAttribute.startsWith("."))return null;hrefAttribute.includes("#")&&!hrefAttribute.startsWith("#")&&(hrefAttribute="#".concat(hrefAttribute.split("#")[1])),selector=hrefAttribute&&"#"!==hrefAttribute?hrefAttribute.trim():null}return selector?selector.split(",").map((sel=>(0,_index.parseSelector)(sel))).join(","):null},SelectorEngine={find(selector){let element=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.documentElement;return[].concat(...Element.prototype.querySelectorAll.call(element,selector))},findOne(selector){let element=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document.documentElement;return Element.prototype.querySelector.call(element,selector)},children:(element,selector)=>[].concat(...element.children).filter((child=>child.matches(selector))),parents(element,selector){const parents=[];let ancestor=element.parentNode.closest(selector);for(;ancestor;)parents.push(ancestor),ancestor=ancestor.parentNode.closest(selector);return parents},prev(element,selector){let previous=element.previousElementSibling;for(;previous;){if(previous.matches(selector))return[previous];previous=previous.previousElementSibling}return[]},next(element,selector){let next=element.nextElementSibling;for(;next;){if(next.matches(selector))return[next];next=next.nextElementSibling}return[]},focusableChildren(element){const focusables=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((selector=>"".concat(selector,':not([tabindex^="-"])'))).join(",");return this.find(focusables,element).filter((el=>!(0,_index.isDisabled)(el)&&(0,_index.isVisible)(el)))},getSelectorFromElement(element){const selector=getSelector(element);return selector&&SelectorEngine.findOne(selector)?selector:null},getElementFromSelector(element){const selector=getSelector(element);return selector?SelectorEngine.findOne(selector):null},getMultipleElementsFromSelector(element){const selector=getSelector(element);return selector?SelectorEngine.find(selector):[]}};var _default=SelectorEngine;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=selector-engine.min.js.map