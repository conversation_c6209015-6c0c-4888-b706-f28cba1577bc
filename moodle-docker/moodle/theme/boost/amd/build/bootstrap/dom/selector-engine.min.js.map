{"version": 3, "file": "selector-engine.min.js", "sources": ["../../../src/bootstrap/dom/selector-engine.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport { isDisabled, isVisible, parseSelector } from '../util/index'\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target')\n\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href')\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || (!hrefAttribute.includes('#') && !hrefAttribute.startsWith('.'))) {\n      return null\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`\n    }\n\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null\n  }\n\n  return selector ? selector.split(',').map(sel => parseSelector(sel)).join(',') : null\n}\n\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector))\n  },\n\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector)\n  },\n\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector))\n  },\n\n  parents(element, selector) {\n    const parents = []\n    let ancestor = element.parentNode.closest(selector)\n\n    while (ancestor) {\n      parents.push(ancestor)\n      ancestor = ancestor.parentNode.closest(selector)\n    }\n\n    return parents\n  },\n\n  prev(element, selector) {\n    let previous = element.previousElementSibling\n\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous]\n      }\n\n      previous = previous.previousElementSibling\n    }\n\n    return []\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling\n\n    while (next) {\n      if (next.matches(selector)) {\n        return [next]\n      }\n\n      next = next.nextElementSibling\n    }\n\n    return []\n  },\n\n  focusableChildren(element) {\n    const focusables = [\n      'a',\n      'button',\n      'input',\n      'textarea',\n      'select',\n      'details',\n      '[tabindex]',\n      '[contenteditable=\"true\"]'\n    ].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',')\n\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el))\n  },\n\n  getSelectorFromElement(element) {\n    const selector = getSelector(element)\n\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null\n    }\n\n    return null\n  },\n\n  getElementFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.findOne(selector) : null\n  },\n\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element)\n\n    return selector ? SelectorEngine.find(selector) : []\n  }\n}\n\nexport default SelectorEngine\n"], "names": ["getSelector", "element", "selector", "getAttribute", "hrefAttribute", "includes", "startsWith", "split", "trim", "map", "sel", "join", "SelectorEngine", "find", "document", "documentElement", "concat", "Element", "prototype", "querySelectorAll", "call", "findOne", "querySelector", "children", "filter", "child", "matches", "parents", "ancestor", "parentNode", "closest", "push", "prev", "previous", "previousElementSibling", "next", "nextElement<PERSON><PERSON>ling", "focusableC<PERSON><PERSON>n", "focusables", "this", "el", "getSelectorFromElement", "getElementFromSelector", "getMultipleElementsFromSelector"], "mappings": "gMASMA,YAAcC,cACdC,SAAWD,QAAQE,aAAa,sBAE/BD,UAAyB,MAAbA,SAAkB,KAC7BE,cAAgBH,QAAQE,aAAa,YAMpCC,gBAAmBA,cAAcC,SAAS,OAASD,cAAcE,WAAW,YACxE,KAILF,cAAcC,SAAS,OAASD,cAAcE,WAAW,OAC3DF,yBAAoBA,cAAcG,MAAM,KAAK,KAG/CL,SAAWE,eAAmC,MAAlBA,cAAwBA,cAAcI,OAAS,YAGtEN,SAAWA,SAASK,MAAM,KAAKE,KAAIC,MAAO,wBAAcA,OAAMC,KAAK,KAAO,MAG7EC,eAAiB,CACrBC,KAAKX,cAAUD,+DAAUa,SAASC,sBACzB,GAAGC,UAAUC,QAAQC,UAAUC,iBAAiBC,KAAKnB,QAASC,YAGvEmB,QAAQnB,cAAUD,+DAAUa,SAASC,uBAC5BE,QAAQC,UAAUI,cAAcF,KAAKnB,QAASC,WAGvDqB,SAAQ,CAACtB,QAASC,WACT,GAAGc,UAAUf,QAAQsB,UAAUC,QAAOC,OAASA,MAAMC,QAAQxB,YAGtEyB,QAAQ1B,QAASC,gBACTyB,QAAU,OACZC,SAAW3B,QAAQ4B,WAAWC,QAAQ5B,eAEnC0B,UACLD,QAAQI,KAAKH,UACbA,SAAWA,SAASC,WAAWC,QAAQ5B,iBAGlCyB,SAGTK,KAAK/B,QAASC,cACR+B,SAAWhC,QAAQiC,4BAEhBD,UAAU,IACXA,SAASP,QAAQxB,gBACZ,CAAC+B,UAGVA,SAAWA,SAASC,6BAGf,IAGTC,KAAKlC,QAASC,cACRiC,KAAOlC,QAAQmC,wBAEZD,MAAM,IACPA,KAAKT,QAAQxB,gBACR,CAACiC,MAGVA,KAAOA,KAAKC,yBAGP,IAGTC,kBAAkBpC,eACVqC,WAAa,CACjB,IACA,SACA,QACA,WACA,SACA,UACA,aACA,4BACA7B,KAAIP,oBAAeA,oCAAiCS,KAAK,YAEpD4B,KAAK1B,KAAKyB,WAAYrC,SAASuB,QAAOgB,MAAO,qBAAWA,MAAO,oBAAUA,OAGlFC,uBAAuBxC,eACfC,SAAWF,YAAYC,gBAEzBC,UACKU,eAAeS,QAAQnB,UAAYA,SAGrC,MAGTwC,uBAAuBzC,eACfC,SAAWF,YAAYC,gBAEtBC,SAAWU,eAAeS,QAAQnB,UAAY,MAGvDyC,gCAAgC1C,eACxBC,SAAWF,YAAYC,gBAEtBC,SAAWU,eAAeC,KAAKX,UAAY,kBAIvCU"}