define("theme_boost/bootstrap/dropdown",["exports","core/popper2","./base-component","./dom/event-handler","./dom/manipulator","./dom/selector-engine","./util/index"],(function(_exports,Popper,_baseComponent,_eventHandler,_manipulator,_selectorEngine,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,Popper=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Popper),_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler),_manipulator=_interopRequireDefault(_manipulator),_selectorEngine=_interopRequireDefault(_selectorEngine);const EVENT_KEY=".".concat("bs.dropdown"),ARROW_UP_KEY="ArrowUp",ARROW_DOWN_KEY="ArrowDown",EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),EVENT_KEYDOWN_DATA_API="keydown".concat(EVENT_KEY).concat(".data-api"),EVENT_KEYUP_DATA_API="keyup".concat(EVENT_KEY).concat(".data-api"),SELECTOR_DATA_TOGGLE='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',SELECTOR_DATA_TOGGLE_SHOWN="".concat(SELECTOR_DATA_TOGGLE,".").concat("show"),PLACEMENT_TOP=(0,_index.isRTL)()?"top-end":"top-start",PLACEMENT_TOPEND=(0,_index.isRTL)()?"top-start":"top-end",PLACEMENT_BOTTOM=(0,_index.isRTL)()?"bottom-end":"bottom-start",PLACEMENT_BOTTOMEND=(0,_index.isRTL)()?"bottom-start":"bottom-end",PLACEMENT_RIGHT=(0,_index.isRTL)()?"left-start":"right-start",PLACEMENT_LEFT=(0,_index.isRTL)()?"right-start":"left-start",Default={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},DefaultType={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Dropdown extends _baseComponent.default{constructor(element,config){super(element,config),this._popper=null,this._parent=this._element.parentNode,this._menu=_selectorEngine.default.next(this._element,".dropdown-menu")[0]||_selectorEngine.default.prev(this._element,".dropdown-menu")[0]||_selectorEngine.default.findOne(".dropdown-menu",this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"dropdown"}toggle(){return this._isShown()?this.hide():this.show()}show(){if((0,_index.isDisabled)(this._element)||this._isShown())return;const relatedTarget={relatedTarget:this._element};if(!_eventHandler.default.trigger(this._element,EVENT_SHOW,relatedTarget).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const element of[].concat(...document.body.children))_eventHandler.default.on(element,"mouseover",_index.noop);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add("show"),this._element.classList.add("show"),_eventHandler.default.trigger(this._element,EVENT_SHOWN,relatedTarget)}}hide(){if((0,_index.isDisabled)(this._element)||!this._isShown())return;const relatedTarget={relatedTarget:this._element};this._completeHide(relatedTarget)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(relatedTarget){if(!_eventHandler.default.trigger(this._element,EVENT_HIDE,relatedTarget).defaultPrevented){if("ontouchstart"in document.documentElement)for(const element of[].concat(...document.body.children))_eventHandler.default.off(element,"mouseover",_index.noop);this._popper&&this._popper.destroy(),this._menu.classList.remove("show"),this._element.classList.remove("show"),this._element.setAttribute("aria-expanded","false"),_manipulator.default.removeDataAttribute(this._menu,"popper"),_eventHandler.default.trigger(this._element,EVENT_HIDDEN,relatedTarget)}}_getConfig(config){if("object"==typeof(config=super._getConfig(config)).reference&&!(0,_index.isElement)(config.reference)&&"function"!=typeof config.reference.getBoundingClientRect)throw new TypeError("".concat("dropdown".toUpperCase(),': Option "reference" provided type "object" without a required "getBoundingClientRect" method.'));return config}_createPopper(){if(void 0===Popper)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let referenceElement=this._element;"parent"===this._config.reference?referenceElement=this._parent:(0,_index.isElement)(this._config.reference)?referenceElement=(0,_index.getElement)(this._config.reference):"object"==typeof this._config.reference&&(referenceElement=this._config.reference);const popperConfig=this._getPopperConfig();this._popper=Popper.createPopper(referenceElement,this._menu,popperConfig)}_isShown(){return this._menu.classList.contains("show")}_getPlacement(){const parentDropdown=this._parent;if(parentDropdown.classList.contains("dropend"))return PLACEMENT_RIGHT;if(parentDropdown.classList.contains("dropstart"))return PLACEMENT_LEFT;if(parentDropdown.classList.contains("dropup-center"))return"top";if(parentDropdown.classList.contains("dropdown-center"))return"bottom";const isEnd="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return parentDropdown.classList.contains("dropup")?isEnd?PLACEMENT_TOPEND:PLACEMENT_TOP:isEnd?PLACEMENT_BOTTOMEND:PLACEMENT_BOTTOM}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:offset}=this._config;return"string"==typeof offset?offset.split(",").map((value=>Number.parseInt(value,10))):"function"==typeof offset?popperData=>offset(popperData,this._element):offset}_getPopperConfig(){const defaultBsPopperConfig={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(_manipulator.default.setDataAttribute(this._menu,"popper","static"),defaultBsPopperConfig.modifiers=[{name:"applyStyles",enabled:!1}]),{...defaultBsPopperConfig,...(0,_index.execute)(this._config.popperConfig,[defaultBsPopperConfig])}}_selectMenuItem(_ref){let{key:key,target:target}=_ref;const items=_selectorEngine.default.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((element=>(0,_index.isVisible)(element)));items.length&&(0,_index.getNextActiveElement)(items,target,key===ARROW_DOWN_KEY,!items.includes(target)).focus()}static jQueryInterface(config){return this.each((function(){const data=Dropdown.getOrCreateInstance(this,config);if("string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}static clearMenus(event){if(2===event.button||"keyup"===event.type&&"Tab"!==event.key)return;const openToggles=_selectorEngine.default.find(SELECTOR_DATA_TOGGLE_SHOWN);for(const toggle of openToggles){const context=Dropdown.getInstance(toggle);if(!context||!1===context._config.autoClose)continue;const composedPath=event.composedPath(),isMenuTarget=composedPath.includes(context._menu);if(composedPath.includes(context._element)||"inside"===context._config.autoClose&&!isMenuTarget||"outside"===context._config.autoClose&&isMenuTarget)continue;if(context._menu.contains(event.target)&&("keyup"===event.type&&"Tab"===event.key||/input|select|option|textarea|form/i.test(event.target.tagName)))continue;const relatedTarget={relatedTarget:context._element};"click"===event.type&&(relatedTarget.clickEvent=event),context._completeHide(relatedTarget)}}static dataApiKeydownHandler(event){const isInput=/input|textarea/i.test(event.target.tagName),isEscapeEvent="Escape"===event.key,isUpOrDownEvent=[ARROW_UP_KEY,ARROW_DOWN_KEY].includes(event.key);if(!isUpOrDownEvent&&!isEscapeEvent)return;if(isInput&&!isEscapeEvent)return;event.preventDefault();const getToggleButton=this.matches(SELECTOR_DATA_TOGGLE)?this:_selectorEngine.default.prev(this,SELECTOR_DATA_TOGGLE)[0]||_selectorEngine.default.next(this,SELECTOR_DATA_TOGGLE)[0]||_selectorEngine.default.findOne(SELECTOR_DATA_TOGGLE,event.delegateTarget.parentNode),instance=Dropdown.getOrCreateInstance(getToggleButton);if(isUpOrDownEvent)return event.stopPropagation(),instance.show(),void instance._selectMenuItem(event);instance._isShown()&&(event.stopPropagation(),instance.hide(),getToggleButton.focus())}}_eventHandler.default.on(document,EVENT_KEYDOWN_DATA_API,SELECTOR_DATA_TOGGLE,Dropdown.dataApiKeydownHandler),_eventHandler.default.on(document,EVENT_KEYDOWN_DATA_API,".dropdown-menu",Dropdown.dataApiKeydownHandler),_eventHandler.default.on(document,EVENT_CLICK_DATA_API,Dropdown.clearMenus),_eventHandler.default.on(document,EVENT_KEYUP_DATA_API,Dropdown.clearMenus),_eventHandler.default.on(document,EVENT_CLICK_DATA_API,SELECTOR_DATA_TOGGLE,(function(event){event.preventDefault(),Dropdown.getOrCreateInstance(this).toggle()})),(0,_index.defineJQueryPlugin)(Dropdown);var _default=Dropdown;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=dropdown.min.js.map