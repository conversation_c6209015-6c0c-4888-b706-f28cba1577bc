{"version": 3, "file": "dropdown.min.js", "sources": ["../../src/bootstrap/dropdown.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from 'core/popper2'\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport SelectorEngine from './dom/selector-engine'\nimport {\n  defineJQueryPlugin,\n  execute,\n  getElement,\n  getNextActiveElement,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop\n} from './util/index'\n\n/**\n * Constants\n */\n\nconst NAME = 'dropdown'\nconst DATA_KEY = 'bs.dropdown'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\n\nconst ESCAPE_KEY = 'Escape'\nconst TAB_KEY = 'Tab'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst RIGHT_MOUSE_BUTTON = 2 // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_DROPUP = 'dropup'\nconst CLASS_NAME_DROPEND = 'dropend'\nconst CLASS_NAME_DROPSTART = 'dropstart'\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center'\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center'\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)'\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE}.${CLASS_NAME_SHOW}`\nconst SELECTOR_MENU = '.dropdown-menu'\nconst SELECTOR_NAVBAR = '.navbar'\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav'\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)'\n\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start'\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end'\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start'\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end'\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start'\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start'\nconst PLACEMENT_TOPCENTER = 'top'\nconst PLACEMENT_BOTTOMCENTER = 'bottom'\n\nconst Default = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n}\n\nconst DefaultType = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n}\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._popper = null\n    this._parent = this._element.parentNode // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.prev(this._element, SELECTOR_MENU)[0] ||\n      SelectorEngine.findOne(SELECTOR_MENU, this._parent)\n    this._inNavbar = this._detectNavbar()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show()\n  }\n\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, relatedTarget)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._createPopper()\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    this._element.focus()\n    this._element.setAttribute('aria-expanded', true)\n\n    this._menu.classList.add(CLASS_NAME_SHOW)\n    this._element.classList.add(CLASS_NAME_SHOW)\n    EventHandler.trigger(this._element, EVENT_SHOWN, relatedTarget)\n  }\n\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return\n    }\n\n    const relatedTarget = {\n      relatedTarget: this._element\n    }\n\n    this._completeHide(relatedTarget)\n  }\n\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    super.dispose()\n  }\n\n  update() {\n    this._inNavbar = this._detectNavbar()\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE, relatedTarget)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    if (this._popper) {\n      this._popper.destroy()\n    }\n\n    this._menu.classList.remove(CLASS_NAME_SHOW)\n    this._element.classList.remove(CLASS_NAME_SHOW)\n    this._element.setAttribute('aria-expanded', 'false')\n    Manipulator.removeDataAttribute(this._menu, 'popper')\n    EventHandler.trigger(this._element, EVENT_HIDDEN, relatedTarget)\n  }\n\n  _getConfig(config) {\n    config = super._getConfig(config)\n\n    if (typeof config.reference === 'object' && !isElement(config.reference) &&\n      typeof config.reference.getBoundingClientRect !== 'function'\n    ) {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`)\n    }\n\n    return config\n  }\n\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)')\n    }\n\n    let referenceElement = this._element\n\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference)\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference\n    }\n\n    const popperConfig = this._getPopperConfig()\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig)\n  }\n\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _getPlacement() {\n    const parentDropdown = this._parent\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER\n    }\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end'\n\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP\n    }\n\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM\n  }\n\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      },\n      {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    }\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static') // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _selectMenuItem({ key, target }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element))\n\n    if (!items.length) {\n      return\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY, !items.includes(target)).focus()\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || (event.type === 'keyup' && event.key !== TAB_KEY)) {\n      return\n    }\n\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN)\n\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle)\n      if (!context || context._config.autoClose === false) {\n        continue\n      }\n\n      const composedPath = event.composedPath()\n      const isMenuTarget = composedPath.includes(context._menu)\n      if (\n        composedPath.includes(context._element) ||\n        (context._config.autoClose === 'inside' && !isMenuTarget) ||\n        (context._config.autoClose === 'outside' && isMenuTarget)\n      ) {\n        continue\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && ((event.type === 'keyup' && event.key === TAB_KEY) || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue\n      }\n\n      const relatedTarget = { relatedTarget: context._element }\n\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event\n      }\n\n      context._completeHide(relatedTarget)\n    }\n  }\n\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName)\n    const isEscapeEvent = event.key === ESCAPE_KEY\n    const isUpOrDownEvent = [ARROW_UP_KEY, ARROW_DOWN_KEY].includes(event.key)\n\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return\n    }\n\n    if (isInput && !isEscapeEvent) {\n      return\n    }\n\n    event.preventDefault()\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE) ?\n      this :\n      (SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.next(this, SELECTOR_DATA_TOGGLE)[0] ||\n        SelectorEngine.findOne(SELECTOR_DATA_TOGGLE, event.delegateTarget.parentNode))\n\n    const instance = Dropdown.getOrCreateInstance(getToggleButton)\n\n    if (isUpOrDownEvent) {\n      event.stopPropagation()\n      instance.show()\n      instance._selectMenuItem(event)\n      return\n    }\n\n    if (instance._isShown()) { // else is escape and we check if it is shown\n      event.stopPropagation()\n      instance.hide()\n      getToggleButton.focus()\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler)\nEventHandler.on(document, EVENT_CLICK_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus)\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  event.preventDefault()\n  Dropdown.getOrCreateInstance(this).toggle()\n})\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown)\n\nexport default Dropdown\n"], "names": ["EVENT_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DATA_API", "EVENT_KEYUP_DATA_API", "SELECTOR_DATA_TOGGLE", "SELECTOR_DATA_TOGGLE_SHOWN", "PLACEMENT_TOP", "PLACEMENT_TOPEND", "PLACEMENT_BOTTOM", "PLACEMENT_BOTTOMEND", "PLACEMENT_RIGHT", "PLACEMENT_LEFT", "<PERSON><PERSON><PERSON>", "autoClose", "boundary", "display", "offset", "popperConfig", "reference", "DefaultType", "Dropdown", "BaseComponent", "constructor", "element", "config", "_popper", "_parent", "this", "_element", "parentNode", "_menu", "SelectorEngine", "next", "prev", "findOne", "_inNavbar", "_detectNavbar", "NAME", "toggle", "_isShown", "hide", "show", "relatedTarget", "EventHandler", "trigger", "defaultPrevented", "_createPopper", "document", "documentElement", "closest", "concat", "body", "children", "on", "noop", "focus", "setAttribute", "classList", "add", "_completeHide", "dispose", "destroy", "update", "off", "remove", "removeDataAttribute", "_getConfig", "super", "getBoundingClientRect", "TypeError", "toUpperCase", "<PERSON><PERSON>", "referenceElement", "_config", "_getPopperConfig", "createPopper", "contains", "_getPlacement", "parentDropdown", "isEnd", "getComputedStyle", "getPropertyValue", "trim", "_getOffset", "split", "map", "value", "Number", "parseInt", "popperData", "defaultBsPopperConfig", "placement", "modifiers", "name", "options", "setDataAttribute", "enabled", "_selectMenuItem", "key", "target", "items", "find", "filter", "length", "includes", "each", "data", "getOrCreateInstance", "event", "button", "type", "openToggles", "context", "getInstance", "<PERSON><PERSON><PERSON>", "isMenuTarget", "test", "tagName", "clickEvent", "isInput", "isEscapeEvent", "isUpOrDownEvent", "preventDefault", "getToggleButton", "matches", "<PERSON><PERSON><PERSON><PERSON>", "instance", "stopPropagation", "dataApiKeydownHandler", "clearMenus"], "mappings": "8iDA8BMA,qBADW,eAMXC,aAAe,UACfC,eAAiB,YAGjBC,yBAAoBH,WACpBI,6BAAwBJ,WACxBK,yBAAoBL,WACpBM,2BAAsBN,WACtBO,oCAA+BP,kBAZhB,aAafQ,wCAAmCR,kBAbpB,aAcfS,oCAA+BT,kBAdhB,aAuBfU,qBAAuB,4DACvBC,qCAAgCD,iCARd,QAclBE,eAAgB,kBAAU,UAAY,YACtCC,kBAAmB,kBAAU,YAAc,UAC3CC,kBAAmB,kBAAU,aAAe,eAC5CC,qBAAsB,kBAAU,eAAiB,aACjDC,iBAAkB,kBAAU,aAAe,cAC3CC,gBAAiB,kBAAU,cAAgB,aAI3CC,QAAU,CACdC,WAAW,EACXC,SAAU,kBACVC,QAAS,UACTC,OAAQ,CAAC,EAAG,GACZC,aAAc,KACdC,UAAW,UAGPC,YAAc,CAClBN,UAAW,mBACXC,SAAU,mBACVC,QAAS,SACTC,OAAQ,0BACRC,aAAc,yBACdC,UAAW,iCAOPE,iBAAiBC,uBACrBC,YAAYC,QAASC,cACbD,QAASC,aAEVC,QAAU,UACVC,QAAUC,KAAKC,SAASC,gBAExBC,MAAQC,wBAAeC,KAAKL,KAAKC,SA3CpB,kBA2C6C,IAC7DG,wBAAeE,KAAKN,KAAKC,SA5CT,kBA4CkC,IAClDG,wBAAeG,QA7CC,iBA6CsBP,KAAKD,cACxCS,UAAYR,KAAKS,gBAIbxB,4BACFA,QAGEO,gCACFA,YAGEkB,wBAtFA,WA2FXC,gBACSX,KAAKY,WAAaZ,KAAKa,OAASb,KAAKc,OAG9CA,WACM,qBAAWd,KAAKC,WAAaD,KAAKY,wBAIhCG,cAAgB,CACpBA,cAAef,KAAKC,cAGJe,sBAAaC,QAAQjB,KAAKC,SAAU7B,WAAY2C,eAEpDG,0BAITC,gBAMD,iBAAkBC,SAASC,kBAAoBrB,KAAKD,QAAQuB,QAtFxC,mBAuFjB,MAAM1B,UAAW,GAAG2B,UAAUH,SAASI,KAAKC,gCAClCC,GAAG9B,QAAS,YAAa+B,kBAIrC1B,SAAS2B,aACT3B,SAAS4B,aAAa,iBAAiB,QAEvC1B,MAAM2B,UAAUC,IA1GD,aA2Gf9B,SAAS6B,UAAUC,IA3GJ,8BA4GPd,QAAQjB,KAAKC,SAAU5B,YAAa0C,gBAGnDF,WACM,qBAAWb,KAAKC,YAAcD,KAAKY,wBAIjCG,cAAgB,CACpBA,cAAef,KAAKC,eAGjB+B,cAAcjB,eAGrBkB,UACMjC,KAAKF,cACFA,QAAQoC,gBAGTD,UAGRE,cACO3B,UAAYR,KAAKS,gBAClBT,KAAKF,cACFA,QAAQqC,SAKjBH,cAAcjB,mBACMC,sBAAaC,QAAQjB,KAAKC,SAAU/B,WAAY6C,eACpDG,qBAMV,iBAAkBE,SAASC,oBACxB,MAAMzB,UAAW,GAAG2B,UAAUH,SAASI,KAAKC,gCAClCW,IAAIxC,QAAS,YAAa+B,aAIvC3B,KAAKF,cACFA,QAAQoC,eAGV/B,MAAM2B,UAAUO,OA7JD,aA8JfpC,SAAS6B,UAAUO,OA9JJ,aA+JfpC,SAAS4B,aAAa,gBAAiB,8BAChCS,oBAAoBtC,KAAKG,MAAO,gCAC/Bc,QAAQjB,KAAKC,SAAU9B,aAAc4C,gBAGpDwB,WAAW1C,WAGuB,iBAFhCA,OAAS2C,MAAMD,WAAW1C,SAERN,aAA2B,oBAAUM,OAAON,YACV,mBAA3CM,OAAON,UAAUkD,4BAGlB,IAAIC,oBA9LH,WA8LqBC,wHAGvB9C,OAGTsB,wBACwB,IAAXyB,aACH,IAAIF,UAAU,oEAGlBG,iBAAmB7C,KAAKC,SAEG,WAA3BD,KAAK8C,QAAQvD,UACfsD,iBAAmB7C,KAAKD,SACf,oBAAUC,KAAK8C,QAAQvD,WAChCsD,kBAAmB,qBAAW7C,KAAK8C,QAAQvD,WACA,iBAA3BS,KAAK8C,QAAQvD,YAC7BsD,iBAAmB7C,KAAK8C,QAAQvD,iBAG5BD,aAAeU,KAAK+C,wBACrBjD,QAAU8C,OAAOI,aAAaH,iBAAkB7C,KAAKG,MAAOb,cAGnEsB,kBACSZ,KAAKG,MAAM2B,UAAUmB,SArMR,QAwMtBC,sBACQC,eAAiBnD,KAAKD,WAExBoD,eAAerB,UAAUmB,SAzMN,kBA0MdlE,mBAGLoE,eAAerB,UAAUmB,SA5MJ,oBA6MhBjE,kBAGLmE,eAAerB,UAAUmB,SA/MA,uBAgBL,SAmMpBE,eAAerB,UAAUmB,SAlNE,yBAgBJ,eAuMrBG,MAAkF,QAA1EC,iBAAiBrD,KAAKG,OAAOmD,iBAAiB,iBAAiBC,cAEzEJ,eAAerB,UAAUmB,SA7NP,UA8NbG,MAAQxE,iBAAmBD,cAG7ByE,MAAQtE,oBAAsBD,iBAGvC4B,uBACoD,OAA3CT,KAAKC,SAASqB,QA5ND,WA+NtBkC,mBACQnE,OAAEA,QAAWW,KAAK8C,cAEF,iBAAXzD,OACFA,OAAOoE,MAAM,KAAKC,KAAIC,OAASC,OAAOC,SAASF,MAAO,MAGzC,mBAAXtE,OACFyE,YAAczE,OAAOyE,WAAY9D,KAAKC,UAGxCZ,OAGT0D,yBACQgB,sBAAwB,CAC5BC,UAAWhE,KAAKkD,gBAChBe,UAAW,CAAC,CACVC,KAAM,kBACNC,QAAS,CACPhF,SAAUa,KAAK8C,QAAQ3D,WAG3B,CACE+E,KAAM,SACNC,QAAS,CACP9E,OAAQW,KAAKwD,wBAMfxD,KAAKQ,WAAsC,WAAzBR,KAAK8C,QAAQ1D,gCACrBgF,iBAAiBpE,KAAKG,MAAO,SAAU,UACnD4D,sBAAsBE,UAAY,CAAC,CACjCC,KAAM,cACNG,SAAS,KAIN,IACFN,0BACA,kBAAQ/D,KAAK8C,QAAQxD,aAAc,CAACyE,yBAI3CO,0BAAgBC,IAAEA,IAAFC,OAAOA,mBACfC,MAAQrE,wBAAesE,KA5QF,8DA4Q+B1E,KAAKG,OAAOwE,QAAO/E,UAAW,oBAAUA,WAE7F6E,MAAMG,wCAMUH,MAAOD,OAAQD,MAAQtG,gBAAiBwG,MAAMI,SAASL,SAAS5C,+BAIhE/B,eACdG,KAAK8E,MAAK,iBACTC,KAAOtF,SAASuF,oBAAoBhF,KAAMH,WAE1B,iBAAXA,gBAIiB,IAAjBkF,KAAKlF,cACR,IAAI6C,qCAA8B7C,aAG1CkF,KAAKlF,gCAISoF,UA9TO,IA+TnBA,MAAMC,QAAiD,UAAfD,MAAME,MAlUtC,QAkU0DF,MAAMV,iBAItEa,YAAchF,wBAAesE,KAAKhG,gCAEnC,MAAMiC,UAAUyE,YAAa,OAC1BC,QAAU5F,SAAS6F,YAAY3E,YAChC0E,UAAyC,IAA9BA,QAAQvC,QAAQ5D,yBAI1BqG,aAAeN,MAAMM,eACrBC,aAAeD,aAAaV,SAASQ,QAAQlF,UAEjDoF,aAAaV,SAASQ,QAAQpF,WACC,WAA9BoF,QAAQvC,QAAQ5D,YAA2BsG,cACb,YAA9BH,QAAQvC,QAAQ5D,WAA2BsG,yBAM1CH,QAAQlF,MAAM8C,SAASgC,MAAMT,UAA4B,UAAfS,MAAME,MAzV1C,QAyV8DF,MAAMV,KAAoB,qCAAqCkB,KAAKR,MAAMT,OAAOkB,yBAInJ3E,cAAgB,CAAEA,cAAesE,QAAQpF,UAE5B,UAAfgF,MAAME,OACRpE,cAAc4E,WAAaV,OAG7BI,QAAQrD,cAAcjB,6CAIGkE,aAIrBW,QAAU,kBAAkBH,KAAKR,MAAMT,OAAOkB,SAC9CG,cA7WS,WA6WOZ,MAAMV,IACtBuB,gBAAkB,CAAC9H,aAAcC,gBAAgB4G,SAASI,MAAMV,SAEjEuB,kBAAoBD,wBAIrBD,UAAYC,qBAIhBZ,MAAMc,uBAGAC,gBAAkBhG,KAAKiG,QAAQxH,sBACnCuB,KACCI,wBAAeE,KAAKN,KAAMvB,sBAAsB,IAC/C2B,wBAAeC,KAAKL,KAAMvB,sBAAsB,IAChD2B,wBAAeG,QAAQ9B,qBAAsBwG,MAAMiB,eAAehG,YAEhEiG,SAAW1G,SAASuF,oBAAoBgB,oBAE1CF,uBACFb,MAAMmB,kBACND,SAASrF,YACTqF,SAAS7B,gBAAgBW,OAIvBkB,SAASvF,aACXqE,MAAMmB,kBACND,SAAStF,OACTmF,gBAAgBpE,gCASTF,GAAGN,SAAU7C,uBAAwBE,qBAAsBgB,SAAS4G,6CACpE3E,GAAGN,SAAU7C,uBAhYJ,iBAgY2CkB,SAAS4G,6CAC7D3E,GAAGN,SAAU9C,qBAAsBmB,SAAS6G,kCAC5C5E,GAAGN,SAAU5C,qBAAsBiB,SAAS6G,kCAC5C5E,GAAGN,SAAU9C,qBAAsBG,sBAAsB,SAAUwG,OAC9EA,MAAMc,iBACNtG,SAASuF,oBAAoBhF,MAAMW,0CAOlBlB,uBAEJA"}