define("theme_boost/bootstrap/modal",["exports","./base-component","./dom/event-handler","./dom/selector-engine","./util/backdrop","./util/component-functions","./util/focustrap","./util/index","./util/scrollbar"],(function(_exports,_baseComponent,_eventHandler,_selectorEngine,_backdrop,_componentFunctions,_focustrap,_index,_scrollbar){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler),_selectorEngine=_interopRequireDefault(_selectorEngine),_backdrop=_interopRequireDefault(_backdrop),_focustrap=_interopRequireDefault(_focustrap),_scrollbar=_interopRequireDefault(_scrollbar);const EVENT_KEY=".".concat("bs.modal"),EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDE_PREVENTED="hidePrevented".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),EVENT_RESIZE="resize".concat(EVENT_KEY),EVENT_CLICK_DISMISS="click.dismiss".concat(EVENT_KEY),EVENT_MOUSEDOWN_DISMISS="mousedown.dismiss".concat(EVENT_KEY),EVENT_KEYDOWN_DISMISS="keydown.dismiss".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),Default={backdrop:!0,focus:!0,keyboard:!0},DefaultType={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Modal extends _baseComponent.default{constructor(element,config){super(element,config),this._dialog=_selectorEngine.default.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new _scrollbar.default,this._addEventListeners()}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"modal"}toggle(relatedTarget){return this._isShown?this.hide():this.show(relatedTarget)}show(relatedTarget){if(this._isShown||this._isTransitioning)return;_eventHandler.default.trigger(this._element,EVENT_SHOW,{relatedTarget:relatedTarget}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add("modal-open"),this._adjustDialog(),this._backdrop.show((()=>this._showElement(relatedTarget))))}hide(){if(!this._isShown||this._isTransitioning)return;_eventHandler.default.trigger(this._element,EVENT_HIDE).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove("show"),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated()))}dispose(){_eventHandler.default.off(window,EVENT_KEY),_eventHandler.default.off(this._dialog,EVENT_KEY),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new _backdrop.default({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new _focustrap.default({trapElement:this._element})}_showElement(relatedTarget){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const modalBody=_selectorEngine.default.findOne(".modal-body",this._dialog);modalBody&&(modalBody.scrollTop=0),(0,_index.reflow)(this._element),this._element.classList.add("show");this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,_eventHandler.default.trigger(this._element,EVENT_SHOWN,{relatedTarget:relatedTarget})}),this._dialog,this._isAnimated())}_addEventListeners(){_eventHandler.default.on(this._element,EVENT_KEYDOWN_DISMISS,(event=>{"Escape"===event.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),_eventHandler.default.on(window,EVENT_RESIZE,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),_eventHandler.default.on(this._element,EVENT_MOUSEDOWN_DISMISS,(event=>{_eventHandler.default.one(this._element,EVENT_CLICK_DISMISS,(event2=>{this._element===event.target&&this._element===event2.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove("modal-open"),this._resetAdjustments(),this._scrollBar.reset(),_eventHandler.default.trigger(this._element,EVENT_HIDDEN)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(_eventHandler.default.trigger(this._element,EVENT_HIDE_PREVENTED).defaultPrevented)return;const isModalOverflowing=this._element.scrollHeight>document.documentElement.clientHeight,initialOverflowY=this._element.style.overflowY;"hidden"===initialOverflowY||this._element.classList.contains("modal-static")||(isModalOverflowing||(this._element.style.overflowY="hidden"),this._element.classList.add("modal-static"),this._queueCallback((()=>{this._element.classList.remove("modal-static"),this._queueCallback((()=>{this._element.style.overflowY=initialOverflowY}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const isModalOverflowing=this._element.scrollHeight>document.documentElement.clientHeight,scrollbarWidth=this._scrollBar.getWidth(),isBodyOverflowing=scrollbarWidth>0;if(isBodyOverflowing&&!isModalOverflowing){const property=(0,_index.isRTL)()?"paddingLeft":"paddingRight";this._element.style[property]="".concat(scrollbarWidth,"px")}if(!isBodyOverflowing&&isModalOverflowing){const property=(0,_index.isRTL)()?"paddingRight":"paddingLeft";this._element.style[property]="".concat(scrollbarWidth,"px")}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(config,relatedTarget){return this.each((function(){const data=Modal.getOrCreateInstance(this,config);if("string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config](relatedTarget)}}))}}_eventHandler.default.on(document,EVENT_CLICK_DATA_API,'[data-bs-toggle="modal"]',(function(event){const target=_selectorEngine.default.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&event.preventDefault(),_eventHandler.default.one(target,EVENT_SHOW,(showEvent=>{showEvent.defaultPrevented||_eventHandler.default.one(target,EVENT_HIDDEN,(()=>{(0,_index.isVisible)(this)&&this.focus()}))}));const alreadyOpen=_selectorEngine.default.findOne(".modal.show");alreadyOpen&&Modal.getInstance(alreadyOpen).hide();Modal.getOrCreateInstance(target).toggle(this)})),(0,_componentFunctions.enableDismissTrigger)(Modal),(0,_index.defineJQueryPlugin)(Modal);var _default=Modal;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=modal.min.js.map