{"version": 3, "file": "modal.min.js", "sources": ["../../src/bootstrap/modal.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport Backdrop from './util/backdrop'\nimport { enableDismissTrigger } from './util/component-functions'\nimport FocusTrap from './util/focustrap'\nimport {\n  defineJQueryPlugin, isRTL, isVisible, reflow\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\n\n/**\n * Constants\n */\n\nconst NAME = 'modal'\nconst DATA_KEY = 'bs.modal'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst ESCAPE_KEY = 'Escape'\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY}`\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\n\nconst CLASS_NAME_OPEN = 'modal-open'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_STATIC = 'modal-static'\n\nconst OPEN_SELECTOR = '.modal.show'\nconst SELECTOR_DIALOG = '.modal-dialog'\nconst SELECTOR_MODAL_BODY = '.modal-body'\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"modal\"]'\n\nconst Default = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element)\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._isShown = false\n    this._isTransitioning = false\n    this._scrollBar = new ScrollBarHelper()\n\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, {\n      relatedTarget\n    })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._isTransitioning = true\n\n    this._scrollBar.hide()\n\n    document.body.classList.add(CLASS_NAME_OPEN)\n\n    this._adjustDialog()\n\n    this._backdrop.show(() => this._showElement(relatedTarget))\n  }\n\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = false\n    this._isTransitioning = true\n    this._focustrap.deactivate()\n\n    this._element.classList.remove(CLASS_NAME_SHOW)\n\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated())\n  }\n\n  dispose() {\n    EventHandler.off(window, EVENT_KEY)\n    EventHandler.off(this._dialog, EVENT_KEY)\n\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n\n    super.dispose()\n  }\n\n  handleUpdate() {\n    this._adjustDialog()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop), // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element)\n    }\n\n    this._element.style.display = 'block'\n    this._element.removeAttribute('aria-hidden')\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.scrollTop = 0\n\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog)\n    if (modalBody) {\n      modalBody.scrollTop = 0\n    }\n\n    reflow(this._element)\n\n    this._element.classList.add(CLASS_NAME_SHOW)\n\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate()\n      }\n\n      this._isTransitioning = false\n      EventHandler.trigger(this._element, EVENT_SHOWN, {\n        relatedTarget\n      })\n    }\n\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated())\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      this._triggerBackdropTransition()\n    })\n\n    EventHandler.on(window, EVENT_RESIZE, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog()\n      }\n    })\n\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return\n        }\n\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition()\n          return\n        }\n\n        if (this._config.backdrop) {\n          this.hide()\n        }\n      })\n    })\n  }\n\n  _hideModal() {\n    this._element.style.display = 'none'\n    this._element.setAttribute('aria-hidden', true)\n    this._element.removeAttribute('aria-modal')\n    this._element.removeAttribute('role')\n    this._isTransitioning = false\n\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN)\n      this._resetAdjustments()\n      this._scrollBar.reset()\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    })\n  }\n\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE)\n  }\n\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const initialOverflowY = this._element.style.overflowY\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return\n    }\n\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden'\n    }\n\n    this._element.classList.add(CLASS_NAME_STATIC)\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC)\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY\n      }, this._dialog)\n    }, this._dialog)\n\n    this._element.focus()\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight\n    const scrollbarWidth = this._scrollBar.getWidth()\n    const isBodyOverflowing = scrollbarWidth > 0\n\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft'\n      this._element.style[property] = `${scrollbarWidth}px`\n    }\n  }\n\n  _resetAdjustments() {\n    this._element.style.paddingLeft = ''\n    this._element.style.paddingRight = ''\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](relatedTarget)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  EventHandler.one(target, EVENT_SHOW, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return\n    }\n\n    EventHandler.one(target, EVENT_HIDDEN, () => {\n      if (isVisible(this)) {\n        this.focus()\n      }\n    })\n  })\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Modal.getOrCreateInstance(target)\n\n  data.toggle(this)\n})\n\nenableDismissTrigger(Modal)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal)\n\nexport default Modal\n"], "names": ["EVENT_KEY", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_RESIZE", "EVENT_CLICK_DISMISS", "EVENT_MOUSEDOWN_DISMISS", "EVENT_KEYDOWN_DISMISS", "EVENT_CLICK_DATA_API", "<PERSON><PERSON><PERSON>", "backdrop", "focus", "keyboard", "DefaultType", "Modal", "BaseComponent", "constructor", "element", "config", "_dialog", "SelectorEngine", "findOne", "this", "_element", "_backdrop", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_isShown", "_isTransitioning", "_scrollBar", "ScrollBarHelper", "_addEventListeners", "NAME", "toggle", "relatedTarget", "hide", "show", "EventHandler", "trigger", "defaultPrevented", "document", "body", "classList", "add", "_adjustDialog", "_showElement", "deactivate", "remove", "_queueCallback", "_hideModal", "_isAnimated", "dispose", "off", "window", "handleUpdate", "Backdrop", "isVisible", "Boolean", "_config", "isAnimated", "FocusTrap", "trapElement", "contains", "append", "style", "display", "removeAttribute", "setAttribute", "scrollTop", "modalBody", "activate", "on", "event", "key", "_triggerBackdropTransition", "one", "event2", "target", "_resetAdjustments", "reset", "isModalOverflowing", "scrollHeight", "documentElement", "clientHeight", "initialOverflowY", "overflowY", "scrollbarWidth", "getWidth", "isBodyOverflowing", "property", "paddingLeft", "paddingRight", "each", "data", "getOrCreateInstance", "TypeError", "getElementFromSelector", "includes", "tagName", "preventDefault", "showEvent", "alreadyOpen", "getInstance"], "mappings": "oyBAwBMA,qBADW,YAKXC,yBAAoBD,WACpBE,4CAAuCF,WACvCG,6BAAwBH,WACxBI,yBAAoBJ,WACpBK,2BAAsBL,WACtBM,6BAAwBN,WACxBO,2CAAsCP,WACtCQ,mDAA8CR,WAC9CS,+CAA0CT,WAC1CU,oCAA+BV,kBAZhB,aAwBfW,QAAU,CACdC,UAAU,EACVC,OAAO,EACPC,UAAU,GAGNC,YAAc,CAClBH,SAAU,mBACVC,MAAO,UACPC,SAAU,iBAONE,cAAcC,uBAClBC,YAAYC,QAASC,cACbD,QAASC,aAEVC,QAAUC,wBAAeC,QAxBV,gBAwBmCC,KAAKC,eACvDC,UAAYF,KAAKG,2BACjBC,WAAaJ,KAAKK,4BAClBC,UAAW,OACXC,kBAAmB,OACnBC,WAAa,IAAIC,wBAEjBC,qBAIIvB,4BACFA,QAGEI,gCACFA,YAGEoB,wBAlEA,QAuEXC,OAAOC,sBACEb,KAAKM,SAAWN,KAAKc,OAASd,KAAKe,KAAKF,eAGjDE,KAAKF,kBACCb,KAAKM,UAAYN,KAAKO,wBAIRS,sBAAaC,QAAQjB,KAAKC,SAAUrB,WAAY,CAChEiC,cAAAA,gBAGYK,wBAITZ,UAAW,OACXC,kBAAmB,OAEnBC,WAAWM,OAEhBK,SAASC,KAAKC,UAAUC,IA5EJ,mBA8EfC,qBAEArB,UAAUa,MAAK,IAAMf,KAAKwB,aAAaX,kBAG9CC,WACOd,KAAKM,UAAYN,KAAKO,wBAITS,sBAAaC,QAAQjB,KAAKC,SAAUxB,YAExCyC,wBAITZ,UAAW,OACXC,kBAAmB,OACnBH,WAAWqB,kBAEXxB,SAASoB,UAAUK,OAhGJ,aAkGfC,gBAAe,IAAM3B,KAAK4B,cAAc5B,KAAKC,SAAUD,KAAK6B,gBAGnEC,gCACeC,IAAIC,OAAQxD,iCACZuD,IAAI/B,KAAKH,QAASrB,gBAE1B0B,UAAU4B,eACV1B,WAAWqB,mBAEVK,UAGRG,oBACOV,gBAIPpB,6BACS,IAAI+B,kBAAS,CAClBC,UAAWC,QAAQpC,KAAKqC,QAAQjD,UAChCkD,WAAYtC,KAAK6B,gBAIrBxB,8BACS,IAAIkC,mBAAU,CACnBC,YAAaxC,KAAKC,WAItBuB,aAAaX,eAENM,SAASC,KAAKqB,SAASzC,KAAKC,WAC/BkB,SAASC,KAAKsB,OAAO1C,KAAKC,eAGvBA,SAAS0C,MAAMC,QAAU,aACzB3C,SAAS4C,gBAAgB,oBACzB5C,SAAS6C,aAAa,cAAc,QACpC7C,SAAS6C,aAAa,OAAQ,eAC9B7C,SAAS8C,UAAY,QAEpBC,UAAYlD,wBAAeC,QAxIT,cAwIsCC,KAAKH,SAC/DmD,YACFA,UAAUD,UAAY,qBAGjB/C,KAAKC,eAEPA,SAASoB,UAAUC,IApJJ,aAiKfK,gBAXsB,KACrB3B,KAAKqC,QAAQhD,YACVe,WAAW6C,gBAGb1C,kBAAmB,wBACXU,QAAQjB,KAAKC,SAAUpB,YAAa,CAC/CgC,cAAAA,kBAIoCb,KAAKH,QAASG,KAAK6B,eAG7DnB,2CACewC,GAAGlD,KAAKC,SAAUhB,uBAAuBkE,QApLvC,WAqLTA,MAAMC,MAINpD,KAAKqC,QAAQ/C,cACVwB,YAIFuC,uDAGMH,GAAGlB,OAAQlD,cAAc,KAChCkB,KAAKM,WAAaN,KAAKO,uBACpBgB,yCAII2B,GAAGlD,KAAKC,SAAUjB,yBAAyBmE,8BAEzCG,IAAItD,KAAKC,SAAUlB,qBAAqBwE,SAC/CvD,KAAKC,WAAakD,MAAMK,QAAUxD,KAAKC,WAAasD,OAAOC,SAIjC,WAA1BxD,KAAKqC,QAAQjD,SAKbY,KAAKqC,QAAQjD,eACV0B,YALAuC,oCAWbzB,kBACO3B,SAAS0C,MAAMC,QAAU,YACzB3C,SAAS6C,aAAa,eAAe,QACrC7C,SAAS4C,gBAAgB,mBACzB5C,SAAS4C,gBAAgB,aACzBtC,kBAAmB,OAEnBL,UAAUY,MAAK,KAClBK,SAASC,KAAKC,UAAUK,OArNN,mBAsNb+B,yBACAjD,WAAWkD,8BACHzC,QAAQjB,KAAKC,SAAUtB,iBAIxCkD,qBACS7B,KAAKC,SAASoB,UAAUoB,SA5NX,QA+NtBY,gCACoBrC,sBAAaC,QAAQjB,KAAKC,SAAUvB,sBACxCwC,8BAIRyC,mBAAqB3D,KAAKC,SAAS2D,aAAezC,SAAS0C,gBAAgBC,aAC3EC,iBAAmB/D,KAAKC,SAAS0C,MAAMqB,UAEpB,WAArBD,kBAAiC/D,KAAKC,SAASoB,UAAUoB,SAtOvC,kBA0OjBkB,0BACE1D,SAAS0C,MAAMqB,UAAY,eAG7B/D,SAASoB,UAAUC,IA9OF,qBA+OjBK,gBAAe,UACb1B,SAASoB,UAAUK,OAhPJ,qBAiPfC,gBAAe,UACb1B,SAAS0C,MAAMqB,UAAYD,mBAC/B/D,KAAKH,WACPG,KAAKH,cAEHI,SAASZ,SAOhBkC,sBACQoC,mBAAqB3D,KAAKC,SAAS2D,aAAezC,SAAS0C,gBAAgBC,aAC3EG,eAAiBjE,KAAKQ,WAAW0D,WACjCC,kBAAoBF,eAAiB,KAEvCE,oBAAsBR,mBAAoB,OACtCS,UAAW,kBAAU,cAAgB,oBACtCnE,SAAS0C,MAAMyB,oBAAeH,yBAGhCE,mBAAqBR,mBAAoB,OACtCS,UAAW,kBAAU,eAAiB,mBACvCnE,SAAS0C,MAAMyB,oBAAeH,sBAIvCR,yBACOxD,SAAS0C,MAAM0B,YAAc,QAC7BpE,SAAS0C,MAAM2B,aAAe,0BAId1E,OAAQiB,sBACtBb,KAAKuE,MAAK,iBACTC,KAAOhF,MAAMiF,oBAAoBzE,KAAMJ,WAEvB,iBAAXA,gBAIiB,IAAjB4E,KAAK5E,cACR,IAAI8E,qCAA8B9E,aAG1C4E,KAAK5E,QAAQiB,0CASNqC,GAAG/B,SAAUjC,qBAnSG,4BAmSyC,SAAUiE,aACxEK,OAAS1D,wBAAe6E,uBAAuB3E,MAEjD,CAAC,IAAK,QAAQ4E,SAAS5E,KAAK6E,UAC9B1B,MAAM2B,uCAGKxB,IAAIE,OAAQ5E,YAAYmG,YAC/BA,UAAU7D,wCAKDoC,IAAIE,OAAQ7E,cAAc,MACjC,oBAAUqB,YACPX,oBAML2F,YAAclF,wBAAeC,QA3Tf,eA4ThBiF,aACFxF,MAAMyF,YAAYD,aAAalE,OAGpBtB,MAAMiF,oBAAoBjB,QAElC5C,OAAOZ,sDAGOR,qCAMFA,oBAEJA"}