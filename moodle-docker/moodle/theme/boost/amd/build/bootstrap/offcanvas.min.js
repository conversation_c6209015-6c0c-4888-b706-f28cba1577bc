define("theme_boost/bootstrap/offcanvas",["exports","./base-component","./dom/event-handler","./dom/selector-engine","./util/backdrop","./util/component-functions","./util/focustrap","./util/index","./util/scrollbar"],(function(_exports,_baseComponent,_eventHandler,_selectorEngine,_backdrop,_componentFunctions,_focustrap,_index,_scrollbar){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler),_selectorEngine=_interopRequireDefault(_selectorEngine),_backdrop=_interopRequireDefault(_backdrop),_focustrap=_interopRequireDefault(_focustrap),_scrollbar=_interopRequireDefault(_scrollbar);const EVENT_KEY=".".concat("bs.offcanvas"),EVENT_LOAD_DATA_API="load".concat(EVENT_KEY).concat(".data-api"),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDE_PREVENTED="hidePrevented".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_RESIZE="resize".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY).concat(".data-api"),EVENT_KEYDOWN_DISMISS="keydown.dismiss".concat(EVENT_KEY),Default={backdrop:!0,keyboard:!0,scroll:!1},DefaultType={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Offcanvas extends _baseComponent.default{constructor(element,config){super(element,config),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"offcanvas"}toggle(relatedTarget){return this._isShown?this.hide():this.show(relatedTarget)}show(relatedTarget){if(this._isShown)return;if(_eventHandler.default.trigger(this._element,EVENT_SHOW,{relatedTarget:relatedTarget}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new _scrollbar.default).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add("showing");this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add("show"),this._element.classList.remove("showing"),_eventHandler.default.trigger(this._element,EVENT_SHOWN,{relatedTarget:relatedTarget})}),this._element,!0)}hide(){if(!this._isShown)return;if(_eventHandler.default.trigger(this._element,EVENT_HIDE).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add("hiding"),this._backdrop.hide();this._queueCallback((()=>{this._element.classList.remove("show","hiding"),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new _scrollbar.default).reset(),_eventHandler.default.trigger(this._element,EVENT_HIDDEN)}),this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const isVisible=Boolean(this._config.backdrop);return new _backdrop.default({className:"offcanvas-backdrop",isVisible:isVisible,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:isVisible?()=>{"static"!==this._config.backdrop?this.hide():_eventHandler.default.trigger(this._element,EVENT_HIDE_PREVENTED)}:null})}_initializeFocusTrap(){return new _focustrap.default({trapElement:this._element})}_addEventListeners(){_eventHandler.default.on(this._element,EVENT_KEYDOWN_DISMISS,(event=>{"Escape"===event.key&&(this._config.keyboard?this.hide():_eventHandler.default.trigger(this._element,EVENT_HIDE_PREVENTED))}))}static jQueryInterface(config){return this.each((function(){const data=Offcanvas.getOrCreateInstance(this,config);if("string"==typeof config){if(void 0===data[config]||config.startsWith("_")||"constructor"===config)throw new TypeError('No method named "'.concat(config,'"'));data[config](this)}}))}}_eventHandler.default.on(document,EVENT_CLICK_DATA_API,'[data-bs-toggle="offcanvas"]',(function(event){const target=_selectorEngine.default.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&event.preventDefault(),(0,_index.isDisabled)(this))return;_eventHandler.default.one(target,EVENT_HIDDEN,(()=>{(0,_index.isVisible)(this)&&this.focus()}));const alreadyOpen=_selectorEngine.default.findOne(".offcanvas.show");alreadyOpen&&alreadyOpen!==target&&Offcanvas.getInstance(alreadyOpen).hide();Offcanvas.getOrCreateInstance(target).toggle(this)})),_eventHandler.default.on(window,EVENT_LOAD_DATA_API,(()=>{for(const selector of _selectorEngine.default.find(".offcanvas.show"))Offcanvas.getOrCreateInstance(selector).show()})),_eventHandler.default.on(window,EVENT_RESIZE,(()=>{for(const element of _selectorEngine.default.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(element).position&&Offcanvas.getOrCreateInstance(element).hide()})),(0,_componentFunctions.enableDismissTrigger)(Offcanvas),(0,_index.defineJQueryPlugin)(Offcanvas);var _default=Offcanvas;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=offcanvas.min.js.map