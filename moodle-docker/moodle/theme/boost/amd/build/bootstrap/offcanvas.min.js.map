{"version": 3, "file": "offcanvas.min.js", "sources": ["../../src/bootstrap/offcanvas.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport Backdrop from './util/backdrop'\nimport { enableDismissTrigger } from './util/component-functions'\nimport FocusTrap from './util/focustrap'\nimport {\n  defineJQueryPlugin,\n  isDisabled,\n  isVisible\n} from './util/index'\nimport ScrollBarHelper from './util/scrollbar'\n\n/**\n * Constants\n */\n\nconst NAME = 'offcanvas'\nconst DATA_KEY = 'bs.offcanvas'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst DATA_API_KEY = '.data-api'\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}${DATA_API_KEY}`\nconst ESCAPE_KEY = 'Escape'\n\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\nconst CLASS_NAME_HIDING = 'hiding'\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop'\nconst OPEN_SELECTOR = '.offcanvas.show'\n\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_RESIZE = `resize${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}${DATA_API_KEY}`\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY}`\n\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"offcanvas\"]'\n\nconst Default = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n}\n\nconst DefaultType = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n}\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._isShown = false\n    this._backdrop = this._initializeBackDrop()\n    this._focustrap = this._initializeFocusTrap()\n    this._addEventListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget)\n  }\n\n  show(relatedTarget) {\n    if (this._isShown) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW, { relatedTarget })\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._isShown = true\n    this._backdrop.show()\n\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide()\n    }\n\n    this._element.setAttribute('aria-modal', true)\n    this._element.setAttribute('role', 'dialog')\n    this._element.classList.add(CLASS_NAME_SHOWING)\n\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate()\n      }\n\n      this._element.classList.add(CLASS_NAME_SHOW)\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN, { relatedTarget })\n    }\n\n    this._queueCallback(completeCallBack, this._element, true)\n  }\n\n  hide() {\n    if (!this._isShown) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    this._focustrap.deactivate()\n    this._element.blur()\n    this._isShown = false\n    this._element.classList.add(CLASS_NAME_HIDING)\n    this._backdrop.hide()\n\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW, CLASS_NAME_HIDING)\n      this._element.removeAttribute('aria-modal')\n      this._element.removeAttribute('role')\n\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset()\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._queueCallback(completeCallback, this._element, true)\n  }\n\n  dispose() {\n    this._backdrop.dispose()\n    this._focustrap.deactivate()\n    super.dispose()\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n        return\n      }\n\n      this.hide()\n    }\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop)\n\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    })\n  }\n\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    })\n  }\n\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return\n      }\n\n      if (this._config.keyboard) {\n        this.hide()\n        return\n      }\n\n      EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED)\n    })\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config](this)\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this)\n\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  EventHandler.one(target, EVENT_HIDDEN, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus()\n    }\n  })\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR)\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide()\n  }\n\n  const data = Offcanvas.getOrCreateInstance(target)\n  data.toggle(this)\n})\n\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show()\n  }\n})\n\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide()\n    }\n  }\n})\n\nenableDismissTrigger(Offcanvas)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas)\n\nexport default Offcanvas\n"], "names": ["EVENT_KEY", "EVENT_LOAD_DATA_API", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_HIDE", "EVENT_HIDE_PREVENTED", "EVENT_HIDDEN", "EVENT_RESIZE", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN_DISMISS", "<PERSON><PERSON><PERSON>", "backdrop", "keyboard", "scroll", "DefaultType", "<PERSON><PERSON><PERSON>", "BaseComponent", "constructor", "element", "config", "_isShown", "_backdrop", "this", "_initializeBackDrop", "_focustrap", "_initializeFocusTrap", "_addEventListeners", "NAME", "toggle", "relatedTarget", "hide", "show", "EventHandler", "trigger", "_element", "defaultPrevented", "_config", "ScrollBarHelper", "setAttribute", "classList", "add", "_queueCallback", "activate", "remove", "deactivate", "blur", "removeAttribute", "reset", "dispose", "isVisible", "Boolean", "Backdrop", "className", "isAnimated", "rootElement", "parentNode", "clickCallback", "FocusTrap", "trapElement", "on", "event", "key", "each", "data", "getOrCreateInstance", "undefined", "startsWith", "TypeError", "document", "target", "SelectorEngine", "getElementFromSelector", "includes", "tagName", "preventDefault", "one", "focus", "alreadyOpen", "findOne", "getInstance", "window", "selector", "find", "getComputedStyle", "position"], "mappings": "wyBA0BMA,qBADW,gBAGXC,kCAA6BD,kBADd,aAUfE,yBAAoBF,WACpBG,2BAAsBH,WACtBI,yBAAoBJ,WACpBK,4CAAuCL,WACvCM,6BAAwBN,WACxBO,6BAAwBP,WACxBQ,oCAA+BR,kBAhBhB,aAiBfS,+CAA0CT,WAI1CU,QAAU,CACdC,UAAU,EACVC,UAAU,EACVC,QAAQ,GAGJC,YAAc,CAClBH,SAAU,mBACVC,SAAU,UACVC,OAAQ,iBAOJE,kBAAkBC,uBACtBC,YAAYC,QAASC,cACbD,QAASC,aAEVC,UAAW,OACXC,UAAYC,KAAKC,2BACjBC,WAAaF,KAAKG,4BAClBC,qBAIIhB,4BACFA,QAGEI,gCACFA,YAGEa,wBA3DA,YAgEXC,OAAOC,sBACEP,KAAKF,SAAWE,KAAKQ,OAASR,KAAKS,KAAKF,eAGjDE,KAAKF,kBACCP,KAAKF,mBAISY,sBAAaC,QAAQX,KAAKY,SAAUhC,WAAY,CAAE2B,cAAAA,gBAEtDM,6BAITf,UAAW,OACXC,UAAUU,OAEVT,KAAKc,QAAQvB,aACZwB,oBAAkBP,YAGnBI,SAASI,aAAa,cAAc,QACpCJ,SAASI,aAAa,OAAQ,eAC9BJ,SAASK,UAAUC,IAhFD,gBA4FlBC,gBAVoB,KAClBnB,KAAKc,QAAQvB,SAAUS,KAAKc,QAAQzB,eAClCa,WAAWkB,gBAGbR,SAASK,UAAUC,IAxFN,aAyFbN,SAASK,UAAUI,OAxFH,iCAyFRV,QAAQX,KAAKY,SAAU/B,YAAa,CAAE0B,cAAAA,kBAGfP,KAAKY,UAAU,GAGvDJ,WACOR,KAAKF,mBAIQY,sBAAaC,QAAQX,KAAKY,SAAU9B,YAExC+B,6BAITX,WAAWoB,kBACXV,SAASW,YACTzB,UAAW,OACXc,SAASK,UAAUC,IA5GF,eA6GjBnB,UAAUS,YAcVW,gBAZoB,UAClBP,SAASK,UAAUI,OAlHN,OAEE,eAiHfT,SAASY,gBAAgB,mBACzBZ,SAASY,gBAAgB,QAEzBxB,KAAKc,QAAQvB,aACZwB,oBAAkBU,8BAGXd,QAAQX,KAAKY,SAAU5B,gBAGAgB,KAAKY,UAAU,GAGvDc,eACO3B,UAAU2B,eACVxB,WAAWoB,mBACVI,UAIRzB,4BAWQ0B,UAAYC,QAAQ5B,KAAKc,QAAQzB,iBAEhC,IAAIwC,kBAAS,CAClBC,UAlJsB,qBAmJtBH,UAAAA,UACAI,YAAY,EACZC,YAAahC,KAAKY,SAASqB,WAC3BC,cAAeP,UAjBK,KACU,WAA1B3B,KAAKc,QAAQzB,cAKZmB,6BAJUG,QAAQX,KAAKY,SAAU7B,uBAeK,OAI/CoB,8BACS,IAAIgC,mBAAU,CACnBC,YAAapC,KAAKY,WAItBR,2CACeiC,GAAGrC,KAAKY,SAAUzB,uBAAuBmD,QAtKvC,WAuKTA,MAAMC,MAINvC,KAAKc,QAAQxB,cACVkB,6BAIMG,QAAQX,KAAKY,SAAU7B,iDAKjBc,eACdG,KAAKwC,MAAK,iBACTC,KAAOhD,UAAUiD,oBAAoB1C,KAAMH,WAE3B,iBAAXA,gBAIU8C,IAAjBF,KAAK5C,SAAyBA,OAAO+C,WAAW,MAAmB,gBAAX/C,aACpD,IAAIgD,qCAA8BhD,aAG1C4C,KAAK5C,QAAQG,iCASNqC,GAAGS,SAAU5D,qBAzLG,gCAyLyC,SAAUoD,aACxES,OAASC,wBAAeC,uBAAuBjD,SAEjD,CAAC,IAAK,QAAQkD,SAASlD,KAAKmD,UAC9Bb,MAAMc,kBAGJ,qBAAWpD,mCAIFqD,IAAIN,OAAQ/D,cAAc,MAEjC,oBAAUgB,YACPsD,iBAKHC,YAAcP,wBAAeQ,QAvNf,mBAwNhBD,aAAeA,cAAgBR,QACjCtD,UAAUgE,YAAYF,aAAa/C,OAGxBf,UAAUiD,oBAAoBK,QACtCzC,OAAON,+BAGDqC,GAAGqB,OAAQ/E,qBAAqB,SACtC,MAAMgF,YAAYX,wBAAeY,KAjOlB,mBAkOlBnE,UAAUiD,oBAAoBiB,UAAUlD,gCAI/B4B,GAAGqB,OAAQzE,cAAc,SAC/B,MAAMW,WAAWoD,wBAAeY,KAAK,gDACG,UAAvCC,iBAAiBjE,SAASkE,UAC5BrE,UAAUiD,oBAAoB9C,SAASY,uDAKxBf,yCAMFA,wBAEJA"}