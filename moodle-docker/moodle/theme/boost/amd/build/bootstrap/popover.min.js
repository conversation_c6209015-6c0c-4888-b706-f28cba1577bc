define("theme_boost/bootstrap/popover",["exports","./tooltip","./util/index"],(function(_exports,_tooltip,_index){var obj;Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_tooltip=(obj=_tooltip)&&obj.__esModule?obj:{default:obj};const Default={..._tooltip.default.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},DefaultType={..._tooltip.default.DefaultType,content:"(null|string|element|function)"};class Popover extends _tooltip.default{static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(config){return this.each((function(){const data=Popover.getOrCreateInstance(this,config);if("string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}(0,_index.defineJQueryPlugin)(Popover);var _default=Popover;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=popover.min.js.map