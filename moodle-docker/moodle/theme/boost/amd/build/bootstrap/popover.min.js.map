{"version": 3, "file": "popover.min.js", "sources": ["../../src/bootstrap/popover.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Tooltip from './tooltip'\nimport { defineJQueryPlugin } from './util/index'\n\n/**\n * Constants\n */\n\nconst NAME = 'popover'\n\nconst SELECTOR_TITLE = '.popover-header'\nconst SELECTOR_CONTENT = '.popover-body'\n\nconst Default = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' +\n    '<div class=\"popover-arrow\"></div>' +\n    '<h3 class=\"popover-header\"></h3>' +\n    '<div class=\"popover-body\"></div>' +\n    '</div>',\n  trigger: 'click'\n}\n\nconst DefaultType = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n}\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent()\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    }\n  }\n\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content)\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover)\n\nexport default Popover\n"], "names": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "content", "offset", "placement", "template", "trigger", "DefaultType", "Popover", "NAME", "_isWithContent", "this", "_getTitle", "_getContent", "_getContentForTemplate", "_resolvePossibleFunction", "_config", "config", "each", "data", "getOrCreateInstance", "TypeError"], "mappings": "0QAmBMA,QAAU,IACXC,iBAAQD,QACXE,QAAS,GACTC,OAAQ,CAAC,EAAG,GACZC,UAAW,QACXC,SAAU,8IAKVC,QAAS,SAGLC,YAAc,IACfN,iBAAQM,YACXL,QAAS,wCAOLM,gBAAgBP,iBAETD,4BACFA,QAGEO,gCACFA,YAGEE,wBArCA,UA0CXC,wBACSC,KAAKC,aAAeD,KAAKE,cAIlCC,+BACS,mBACaH,KAAKC,4BACHD,KAAKE,eAI7BA,qBACSF,KAAKI,yBAAyBJ,KAAKK,QAAQd,gCAI7Be,eACdN,KAAKO,MAAK,iBACTC,KAAOX,QAAQY,oBAAoBT,KAAMM,WAEzB,iBAAXA,gBAIiB,IAAjBE,KAAKF,cACR,IAAII,qCAA8BJ,aAG1CE,KAAKF,6CASQT,sBAEJA"}