define("theme_boost/bootstrap/scrollspy",["exports","./base-component","./dom/event-handler","./dom/selector-engine","./util/index"],(function(_exports,_baseComponent,_eventHandler,_selectorEngine,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler),_selectorEngine=_interopRequireDefault(_selectorEngine);const EVENT_KEY=".".concat("bs.scrollspy"),EVENT_ACTIVATE="activate".concat(EVENT_KEY),EVENT_CLICK="click".concat(EVENT_KEY),EVENT_LOAD_DATA_API="load".concat(EVENT_KEY).concat(".data-api"),SELECTOR_LINK_ITEMS="".concat(".nav-link",", ").concat(".nav-item"," > ").concat(".nav-link",", ").concat(".list-group-item"),Default={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},DefaultType={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class ScrollSpy extends _baseComponent.default{constructor(element,config){super(element,config),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const section of this._observableSections.values())this._observer.observe(section)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(config){return config.target=(0,_index.getElement)(config.target)||document.body,config.rootMargin=config.offset?"".concat(config.offset,"px 0px -30%"):config.rootMargin,"string"==typeof config.threshold&&(config.threshold=config.threshold.split(",").map((value=>Number.parseFloat(value)))),config}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(_eventHandler.default.off(this._config.target,EVENT_CLICK),_eventHandler.default.on(this._config.target,EVENT_CLICK,"[href]",(event=>{const observableSection=this._observableSections.get(event.target.hash);if(observableSection){event.preventDefault();const root=this._rootElement||window,height=observableSection.offsetTop-this._element.offsetTop;if(root.scrollTo)return void root.scrollTo({top:height,behavior:"smooth"});root.scrollTop=height}})))}_getNewObserver(){const options={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((entries=>this._observerCallback(entries)),options)}_observerCallback(entries){const targetElement=entry=>this._targetLinks.get("#".concat(entry.target.id)),activate=entry=>{this._previousScrollData.visibleEntryTop=entry.target.offsetTop,this._process(targetElement(entry))},parentScrollTop=(this._rootElement||document.documentElement).scrollTop,userScrollsDown=parentScrollTop>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=parentScrollTop;for(const entry of entries){if(!entry.isIntersecting){this._activeTarget=null,this._clearActiveClass(targetElement(entry));continue}const entryIsLowerThanPrevious=entry.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(userScrollsDown&&entryIsLowerThanPrevious){if(activate(entry),!parentScrollTop)return}else userScrollsDown||entryIsLowerThanPrevious||activate(entry)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const targetLinks=_selectorEngine.default.find("[href]",this._config.target);for(const anchor of targetLinks){if(!anchor.hash||(0,_index.isDisabled)(anchor))continue;const observableSection=_selectorEngine.default.findOne(decodeURI(anchor.hash),this._element);(0,_index.isVisible)(observableSection)&&(this._targetLinks.set(decodeURI(anchor.hash),anchor),this._observableSections.set(anchor.hash,observableSection))}}_process(target){this._activeTarget!==target&&(this._clearActiveClass(this._config.target),this._activeTarget=target,target.classList.add("active"),this._activateParents(target),_eventHandler.default.trigger(this._element,EVENT_ACTIVATE,{relatedTarget:target}))}_activateParents(target){if(target.classList.contains("dropdown-item"))_selectorEngine.default.findOne(".dropdown-toggle",target.closest(".dropdown")).classList.add("active");else for(const listGroup of _selectorEngine.default.parents(target,".nav, .list-group"))for(const item of _selectorEngine.default.prev(listGroup,SELECTOR_LINK_ITEMS))item.classList.add("active")}_clearActiveClass(parent){parent.classList.remove("active");const activeNodes=_selectorEngine.default.find("".concat("[href]",".").concat("active"),parent);for(const node of activeNodes)node.classList.remove("active")}static jQueryInterface(config){return this.each((function(){const data=ScrollSpy.getOrCreateInstance(this,config);if("string"==typeof config){if(void 0===data[config]||config.startsWith("_")||"constructor"===config)throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}_eventHandler.default.on(window,EVENT_LOAD_DATA_API,(()=>{for(const spy of _selectorEngine.default.find('[data-bs-spy="scroll"]'))ScrollSpy.getOrCreateInstance(spy)})),(0,_index.defineJQueryPlugin)(ScrollSpy);var _default=ScrollSpy;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=scrollspy.min.js.map