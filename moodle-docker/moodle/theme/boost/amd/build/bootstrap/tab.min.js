define("theme_boost/bootstrap/tab",["exports","./base-component","./dom/event-handler","./dom/selector-engine","./util/index"],(function(_exports,_baseComponent,_eventHandler,_selectorEngine,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler),_selectorEngine=_interopRequireDefault(_selectorEngine);const EVENT_KEY=".".concat("bs.tab"),EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),EVENT_CLICK_DATA_API="click".concat(EVENT_KEY),EVENT_KEYDOWN="keydown".concat(EVENT_KEY),EVENT_LOAD_DATA_API="load".concat(EVENT_KEY),ARROW_LEFT_KEY="ArrowLeft",ARROW_RIGHT_KEY="ArrowRight",ARROW_UP_KEY="ArrowUp",ARROW_DOWN_KEY="ArrowDown",HOME_KEY="Home",END_KEY="End",NOT_SELECTOR_DROPDOWN_TOGGLE=":not(".concat(".dropdown-toggle",")"),SELECTOR_INNER=".nav-link".concat(NOT_SELECTOR_DROPDOWN_TOGGLE,", .list-group-item").concat(NOT_SELECTOR_DROPDOWN_TOGGLE,', [role="tab"]').concat(NOT_SELECTOR_DROPDOWN_TOGGLE),SELECTOR_DATA_TOGGLE='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',SELECTOR_INNER_ELEM="".concat(SELECTOR_INNER,", ").concat(SELECTOR_DATA_TOGGLE),SELECTOR_DATA_TOGGLE_ACTIVE=".".concat("active",'[data-bs-toggle="tab"], .').concat("active",'[data-bs-toggle="pill"], .').concat("active",'[data-bs-toggle="list"]');class Tab extends _baseComponent.default{constructor(element){super(element),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),_eventHandler.default.on(this._element,EVENT_KEYDOWN,(event=>this._keydown(event))))}static get NAME(){return"tab"}show(){const innerElem=this._element;if(this._elemIsActive(innerElem))return;const active=this._getActiveElem(),hideEvent=active?_eventHandler.default.trigger(active,EVENT_HIDE,{relatedTarget:innerElem}):null;_eventHandler.default.trigger(innerElem,EVENT_SHOW,{relatedTarget:active}).defaultPrevented||hideEvent&&hideEvent.defaultPrevented||(this._deactivate(active,innerElem),this._activate(innerElem,active))}_activate(element,relatedElem){if(!element)return;element.classList.add("active"),this._activate(_selectorEngine.default.getElementFromSelector(element));this._queueCallback((()=>{"tab"===element.getAttribute("role")?(element.removeAttribute("tabindex"),element.setAttribute("aria-selected",!0),this._toggleDropDown(element,!0),_eventHandler.default.trigger(element,EVENT_SHOWN,{relatedTarget:relatedElem})):element.classList.add("show")}),element,element.classList.contains("fade"))}_deactivate(element,relatedElem){if(!element)return;element.classList.remove("active"),element.blur(),this._deactivate(_selectorEngine.default.getElementFromSelector(element));this._queueCallback((()=>{"tab"===element.getAttribute("role")?(element.setAttribute("aria-selected",!1),element.setAttribute("tabindex","-1"),this._toggleDropDown(element,!1),_eventHandler.default.trigger(element,EVENT_HIDDEN,{relatedTarget:relatedElem})):element.classList.remove("show")}),element,element.classList.contains("fade"))}_keydown(event){if(![ARROW_LEFT_KEY,ARROW_RIGHT_KEY,ARROW_UP_KEY,ARROW_DOWN_KEY,HOME_KEY,END_KEY].includes(event.key))return;event.stopPropagation(),event.preventDefault();const children=this._getChildren().filter((element=>!(0,_index.isDisabled)(element)));let nextActiveElement;if([HOME_KEY,END_KEY].includes(event.key))nextActiveElement=children[event.key===HOME_KEY?0:children.length-1];else{const isNext=[ARROW_RIGHT_KEY,ARROW_DOWN_KEY].includes(event.key);nextActiveElement=(0,_index.getNextActiveElement)(children,event.target,isNext,!0)}nextActiveElement&&(nextActiveElement.focus({preventScroll:!0}),Tab.getOrCreateInstance(nextActiveElement).show())}_getChildren(){return _selectorEngine.default.find(SELECTOR_INNER_ELEM,this._parent)}_getActiveElem(){return this._getChildren().find((child=>this._elemIsActive(child)))||null}_setInitialAttributes(parent,children){this._setAttributeIfNotExists(parent,"role","tablist");for(const child of children)this._setInitialAttributesOnChild(child)}_setInitialAttributesOnChild(child){child=this._getInnerElement(child);const isActive=this._elemIsActive(child),outerElem=this._getOuterElement(child);child.setAttribute("aria-selected",isActive),outerElem!==child&&this._setAttributeIfNotExists(outerElem,"role","presentation"),isActive||child.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(child,"role","tab"),this._setInitialAttributesOnTargetPanel(child)}_setInitialAttributesOnTargetPanel(child){const target=_selectorEngine.default.getElementFromSelector(child);target&&(this._setAttributeIfNotExists(target,"role","tabpanel"),child.id&&this._setAttributeIfNotExists(target,"aria-labelledby","".concat(child.id)))}_toggleDropDown(element,open){const outerElem=this._getOuterElement(element);if(!outerElem.classList.contains("dropdown"))return;const toggle=(selector,className)=>{const element=_selectorEngine.default.findOne(selector,outerElem);element&&element.classList.toggle(className,open)};toggle(".dropdown-toggle","active"),toggle(".dropdown-menu","show"),outerElem.setAttribute("aria-expanded",open)}_setAttributeIfNotExists(element,attribute,value){element.hasAttribute(attribute)||element.setAttribute(attribute,value)}_elemIsActive(elem){return elem.classList.contains("active")}_getInnerElement(elem){return elem.matches(SELECTOR_INNER_ELEM)?elem:_selectorEngine.default.findOne(SELECTOR_INNER_ELEM,elem)}_getOuterElement(elem){return elem.closest(".nav-item, .list-group-item")||elem}static jQueryInterface(config){return this.each((function(){const data=Tab.getOrCreateInstance(this);if("string"==typeof config){if(void 0===data[config]||config.startsWith("_")||"constructor"===config)throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}_eventHandler.default.on(document,EVENT_CLICK_DATA_API,SELECTOR_DATA_TOGGLE,(function(event){["A","AREA"].includes(this.tagName)&&event.preventDefault(),(0,_index.isDisabled)(this)||Tab.getOrCreateInstance(this).show()})),_eventHandler.default.on(window,EVENT_LOAD_DATA_API,(()=>{for(const element of _selectorEngine.default.find(SELECTOR_DATA_TOGGLE_ACTIVE))Tab.getOrCreateInstance(element)})),(0,_index.defineJQueryPlugin)(Tab);var _default=Tab;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=tab.min.js.map