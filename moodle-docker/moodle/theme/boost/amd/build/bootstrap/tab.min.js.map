{"version": 3, "file": "tab.min.js", "sources": ["../../src/bootstrap/tab.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport SelectorEngine from './dom/selector-engine'\nimport { defineJQueryPlugin, getNextActiveElement, isDisabled } from './util/index'\n\n/**\n * Constants\n */\n\nconst NAME = 'tab'\nconst DATA_KEY = 'bs.tab'\nconst EVENT_KEY = `.${D<PERSON><PERSON>_KEY}`\n\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY}`\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY}`\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY}`\n\nconst ARROW_LEFT_KEY = 'ArrowLeft'\nconst ARROW_RIGHT_KEY = 'ArrowRight'\nconst ARROW_UP_KEY = 'ArrowUp'\nconst ARROW_DOWN_KEY = 'ArrowDown'\nconst HOME_KEY = 'Home'\nconst END_KEY = 'End'\n\nconst CLASS_NAME_ACTIVE = 'active'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_DROPDOWN = 'dropdown'\n\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle'\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu'\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = `:not(${SELECTOR_DROPDOWN_TOGGLE})`\n\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]'\nconst SELECTOR_OUTER = '.nav-item, .list-group-item'\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]' // TODO: could only be `tab` in v6\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`\n\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element)\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL)\n\n    if (!this._parent) {\n      return\n      // TODO: should throw exception in v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren())\n\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event))\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() { // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element\n    if (this._elemIsActive(innerElem)) {\n      return\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem()\n\n    const hideEvent = active ?\n      EventHandler.trigger(active, EVENT_HIDE, { relatedTarget: innerElem }) :\n      null\n\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW, { relatedTarget: active })\n\n    if (showEvent.defaultPrevented || (hideEvent && hideEvent.defaultPrevented)) {\n      return\n    }\n\n    this._deactivate(active, innerElem)\n    this._activate(innerElem, active)\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.add(CLASS_NAME_ACTIVE)\n\n    this._activate(SelectorEngine.getElementFromSelector(element)) // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.removeAttribute('tabindex')\n      element.setAttribute('aria-selected', true)\n      this._toggleDropDown(element, true)\n      EventHandler.trigger(element, EVENT_SHOWN, {\n        relatedTarget: relatedElem\n      })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return\n    }\n\n    element.classList.remove(CLASS_NAME_ACTIVE)\n    element.blur()\n\n    this._deactivate(SelectorEngine.getElementFromSelector(element)) // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW)\n        return\n      }\n\n      element.setAttribute('aria-selected', false)\n      element.setAttribute('tabindex', '-1')\n      this._toggleDropDown(element, false)\n      EventHandler.trigger(element, EVENT_HIDDEN, { relatedTarget: relatedElem })\n    }\n\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _keydown(event) {\n    if (!([ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY, HOME_KEY, END_KEY].includes(event.key))) {\n      return\n    }\n\n    event.stopPropagation()// stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault()\n\n    const children = this._getChildren().filter(element => !isDisabled(element))\n    let nextActiveElement\n\n    if ([HOME_KEY, END_KEY].includes(event.key)) {\n      nextActiveElement = children[event.key === HOME_KEY ? 0 : children.length - 1]\n    } else {\n      const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key)\n      nextActiveElement = getNextActiveElement(children, event.target, isNext, true)\n    }\n\n    if (nextActiveElement) {\n      nextActiveElement.focus({ preventScroll: true })\n      Tab.getOrCreateInstance(nextActiveElement).show()\n    }\n  }\n\n  _getChildren() { // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent)\n  }\n\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null\n  }\n\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist')\n\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child)\n    }\n  }\n\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child)\n    const isActive = this._elemIsActive(child)\n    const outerElem = this._getOuterElement(child)\n    child.setAttribute('aria-selected', isActive)\n\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation')\n    }\n\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1')\n    }\n\n    this._setAttributeIfNotExists(child, 'role', 'tab')\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child)\n  }\n\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child)\n\n    if (!target) {\n      return\n    }\n\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel')\n\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `${child.id}`)\n    }\n  }\n\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element)\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return\n    }\n\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem)\n      if (element) {\n        element.classList.toggle(className, open)\n      }\n    }\n\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE)\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW)\n    outerElem.setAttribute('aria-expanded', open)\n  }\n\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value)\n    }\n  }\n\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE)\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem)\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault()\n  }\n\n  if (isDisabled(this)) {\n    return\n  }\n\n  Tab.getOrCreateInstance(this).show()\n})\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element)\n  }\n})\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab)\n\nexport default Tab\n"], "names": ["EVENT_KEY", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "EVENT_CLICK_DATA_API", "EVENT_KEYDOWN", "EVENT_LOAD_DATA_API", "ARROW_LEFT_KEY", "ARROW_RIGHT_KEY", "ARROW_UP_KEY", "ARROW_DOWN_KEY", "HOME_KEY", "END_KEY", "NOT_SELECTOR_DROPDOWN_TOGGLE", "SELECTOR_INNER", "SELECTOR_DATA_TOGGLE", "SELECTOR_INNER_ELEM", "SELECTOR_DATA_TOGGLE_ACTIVE", "Tab", "BaseComponent", "constructor", "element", "_parent", "this", "_element", "closest", "_setInitialAttributes", "_get<PERSON><PERSON><PERSON>n", "on", "event", "_keydown", "NAME", "show", "innerElem", "_elemIsActive", "active", "_getActiveElem", "hideEvent", "EventHandler", "trigger", "relatedTarget", "defaultPrevented", "_deactivate", "_activate", "relatedElem", "classList", "add", "SelectorEngine", "getElementFromSelector", "_queueCallback", "getAttribute", "removeAttribute", "setAttribute", "_toggleDropDown", "contains", "remove", "blur", "includes", "key", "stopPropagation", "preventDefault", "children", "filter", "nextActiveElement", "length", "isNext", "target", "focus", "preventScroll", "getOrCreateInstance", "find", "child", "parent", "_setAttributeIfNotExists", "_setInitialAttributesOnChild", "_getInnerElement", "isActive", "outerElem", "_getOuterElement", "_setInitialAttributesOnTargetPanel", "id", "open", "toggle", "selector", "className", "findOne", "attribute", "value", "hasAttribute", "elem", "matches", "config", "each", "data", "undefined", "startsWith", "TypeError", "document", "tagName", "window"], "mappings": "ihBAkBMA,qBADW,UAGXC,yBAAoBD,WACpBE,6BAAwBF,WACxBG,yBAAoBH,WACpBI,2BAAsBJ,WACtBK,oCAA+BL,WAC/BM,+BAA0BN,WAC1BO,kCAA6BP,WAE7BQ,eAAiB,YACjBC,gBAAkB,aAClBC,aAAe,UACfC,eAAiB,YACjBC,SAAW,OACXC,QAAU,MASVC,4CAF2B,wBAM3BC,kCAA6BD,0DAAiDA,sDAA6CA,8BAC3HE,qBAAuB,2EACvBC,8BAAyBF,4BAAmBC,sBAE5CE,uCAfoB,6CAAA,8CAAA,0CAqBpBC,YAAYC,uBAChBC,YAAYC,eACJA,cACDC,QAAUC,KAAKC,SAASC,QAfN,uCAiBlBF,KAAKD,eAOLI,sBAAsBH,KAAKD,QAASC,KAAKI,sCAEjCC,GAAGL,KAAKC,SAAUnB,eAAewB,OAASN,KAAKO,SAASD,UAI5DE,wBA1DA,MA+DXC,aACQC,UAAYV,KAAKC,YACnBD,KAAKW,cAAcD,wBAKjBE,OAASZ,KAAKa,iBAEdC,UAAYF,OAChBG,sBAAaC,QAAQJ,OAAQnC,WAAY,CAAEwC,cAAeP,YAC1D,KAEgBK,sBAAaC,QAAQN,UAAW/B,WAAY,CAAEsC,cAAeL,SAEjEM,kBAAqBJ,WAAaA,UAAUI,wBAIrDC,YAAYP,OAAQF,gBACpBU,UAAUV,UAAWE,SAI5BQ,UAAUtB,QAASuB,iBACZvB,eAILA,QAAQwB,UAAUC,IAzEI,eA2EjBH,UAAUI,wBAAeC,uBAAuB3B,eAgBhD4B,gBAdY,KACsB,QAAjC5B,QAAQ6B,aAAa,SAKzB7B,QAAQ8B,gBAAgB,YACxB9B,QAAQ+B,aAAa,iBAAiB,QACjCC,gBAAgBhC,SAAS,yBACjBkB,QAAQlB,QAASlB,YAAa,CACzCqC,cAAeI,eARfvB,QAAQwB,UAAUC,IA7EF,UAyFUzB,QAASA,QAAQwB,UAAUS,SA1FrC,SA6FtBZ,YAAYrB,QAASuB,iBACdvB,eAILA,QAAQwB,UAAUU,OAnGI,UAoGtBlC,QAAQmC,YAEHd,YAAYK,wBAAeC,uBAAuB3B,eAclD4B,gBAZY,KACsB,QAAjC5B,QAAQ6B,aAAa,SAKzB7B,QAAQ+B,aAAa,iBAAiB,GACtC/B,QAAQ+B,aAAa,WAAY,WAC5BC,gBAAgBhC,SAAS,yBACjBkB,QAAQlB,QAASpB,aAAc,CAAEuC,cAAeI,eAP3DvB,QAAQwB,UAAUU,OAxGF,UAkHUlC,QAASA,QAAQwB,UAAUS,SAnHrC,SAsHtBxB,SAASD,WACD,CAACtB,eAAgBC,gBAAiBC,aAAcC,eAAgBC,SAAUC,SAAS6C,SAAS5B,MAAM6B,YAIxG7B,MAAM8B,kBACN9B,MAAM+B,uBAEAC,SAAWtC,KAAKI,eAAemC,QAAOzC,WAAY,qBAAWA,eAC/D0C,qBAEA,CAACpD,SAAUC,SAAS6C,SAAS5B,MAAM6B,KACrCK,kBAAoBF,SAAShC,MAAM6B,MAAQ/C,SAAW,EAAIkD,SAASG,OAAS,OACvE,OACCC,OAAS,CAACzD,gBAAiBE,gBAAgB+C,SAAS5B,MAAM6B,KAChEK,mBAAoB,+BAAqBF,SAAUhC,MAAMqC,OAAQD,QAAQ,GAGvEF,oBACFA,kBAAkBI,MAAM,CAAEC,eAAe,IACzClD,IAAImD,oBAAoBN,mBAAmB/B,QAI/CL,sBACSoB,wBAAeuB,KAAKtD,oBAAqBO,KAAKD,SAGvDc,wBACSb,KAAKI,eAAe2C,MAAKC,OAAShD,KAAKW,cAAcqC,UAAW,KAGzE7C,sBAAsB8C,OAAQX,eACvBY,yBAAyBD,OAAQ,OAAQ,eAEzC,MAAMD,SAASV,cACba,6BAA6BH,OAItCG,6BAA6BH,OAC3BA,MAAQhD,KAAKoD,iBAAiBJ,aACxBK,SAAWrD,KAAKW,cAAcqC,OAC9BM,UAAYtD,KAAKuD,iBAAiBP,OACxCA,MAAMnB,aAAa,gBAAiBwB,UAEhCC,YAAcN,YACXE,yBAAyBI,UAAW,OAAQ,gBAG9CD,UACHL,MAAMnB,aAAa,WAAY,WAG5BqB,yBAAyBF,MAAO,OAAQ,YAGxCQ,mCAAmCR,OAG1CQ,mCAAmCR,aAC3BL,OAASnB,wBAAeC,uBAAuBuB,OAEhDL,cAIAO,yBAAyBP,OAAQ,OAAQ,YAE1CK,MAAMS,SACHP,yBAAyBP,OAAQ,4BAAsBK,MAAMS,MAItE3B,gBAAgBhC,QAAS4D,YACjBJ,UAAYtD,KAAKuD,iBAAiBzD,aACnCwD,UAAUhC,UAAUS,SAhMN,yBAoMb4B,OAAS,CAACC,SAAUC,mBAClB/D,QAAU0B,wBAAesC,QAAQF,SAAUN,WAC7CxD,SACFA,QAAQwB,UAAUqC,OAAOE,UAAWH,OAIxCC,OAzM6B,mBALP,UA+MtBA,OAzM2B,iBAJP,QA8MpBL,UAAUzB,aAAa,gBAAiB6B,MAG1CR,yBAAyBpD,QAASiE,UAAWC,OACtClE,QAAQmE,aAAaF,YACxBjE,QAAQ+B,aAAakC,UAAWC,OAIpCrD,cAAcuD,aACLA,KAAK5C,UAAUS,SA1NA,UA8NxBqB,iBAAiBc,aACRA,KAAKC,QAAQ1E,qBAAuByE,KAAO1C,wBAAesC,QAAQrE,oBAAqByE,MAIhGX,iBAAiBW,aACRA,KAAKhE,QA1NO,gCA0NoBgE,4BAIlBE,eACdpE,KAAKqE,MAAK,iBACTC,KAAO3E,IAAImD,oBAAoB9C,SAEf,iBAAXoE,gBAIUG,IAAjBD,KAAKF,SAAyBA,OAAOI,WAAW,MAAmB,gBAAXJ,aACpD,IAAIK,qCAA8BL,aAG1CE,KAAKF,qCASE/D,GAAGqE,SAAU7F,qBAAsBW,sBAAsB,SAAUc,OAC1E,CAAC,IAAK,QAAQ4B,SAASlC,KAAK2E,UAC9BrE,MAAM+B,kBAGJ,qBAAWrC,OAIfL,IAAImD,oBAAoB9C,MAAMS,gCAMnBJ,GAAGuE,OAAQ7F,qBAAqB,SACtC,MAAMe,WAAW0B,wBAAeuB,KAAKrD,6BACxCC,IAAImD,oBAAoBhD,0CAOTH,kBAEJA"}