define("theme_boost/bootstrap/toast",["exports","./base-component","./dom/event-handler","./util/component-functions","./util/index"],(function(_exports,_baseComponent,_eventHandler,_componentFunctions,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler);const EVENT_KEY=".".concat("bs.toast"),EVENT_MOUSEOVER="mouseover".concat(EVENT_KEY),EVENT_MOUSEOUT="mouseout".concat(EVENT_KEY),EVENT_FOCUSIN="focusin".concat(EVENT_KEY),EVENT_FOCUSOUT="focusout".concat(EVENT_KEY),EVENT_HIDE="hide".concat(EVENT_KEY),EVENT_HIDDEN="hidden".concat(EVENT_KEY),EVENT_SHOW="show".concat(EVENT_KEY),EVENT_SHOWN="shown".concat(EVENT_KEY),DefaultType={animation:"boolean",autohide:"boolean",delay:"number"},Default={animation:!0,autohide:!0,delay:5e3};class Toast extends _baseComponent.default{constructor(element,config){super(element,config),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"toast"}show(){if(_eventHandler.default.trigger(this._element,EVENT_SHOW).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");this._element.classList.remove("hide"),(0,_index.reflow)(this._element),this._element.classList.add("show","showing"),this._queueCallback((()=>{this._element.classList.remove("showing"),_eventHandler.default.trigger(this._element,EVENT_SHOWN),this._maybeScheduleHide()}),this._element,this._config.animation)}hide(){if(!this.isShown())return;if(_eventHandler.default.trigger(this._element,EVENT_HIDE).defaultPrevented)return;this._element.classList.add("showing"),this._queueCallback((()=>{this._element.classList.add("hide"),this._element.classList.remove("showing","show"),_eventHandler.default.trigger(this._element,EVENT_HIDDEN)}),this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove("show"),super.dispose()}isShown(){return this._element.classList.contains("show")}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(event,isInteracting){switch(event.type){case"mouseover":case"mouseout":this._hasMouseInteraction=isInteracting;break;case"focusin":case"focusout":this._hasKeyboardInteraction=isInteracting}if(isInteracting)return void this._clearTimeout();const nextElement=event.relatedTarget;this._element===nextElement||this._element.contains(nextElement)||this._maybeScheduleHide()}_setListeners(){_eventHandler.default.on(this._element,EVENT_MOUSEOVER,(event=>this._onInteraction(event,!0))),_eventHandler.default.on(this._element,EVENT_MOUSEOUT,(event=>this._onInteraction(event,!1))),_eventHandler.default.on(this._element,EVENT_FOCUSIN,(event=>this._onInteraction(event,!0))),_eventHandler.default.on(this._element,EVENT_FOCUSOUT,(event=>this._onInteraction(event,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(config){return this.each((function(){const data=Toast.getOrCreateInstance(this,config);if("string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config](this)}}))}}(0,_componentFunctions.enableDismissTrigger)(Toast),(0,_index.defineJQueryPlugin)(Toast);var _default=Toast;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=toast.min.js.map