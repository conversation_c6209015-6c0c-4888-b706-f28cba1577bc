{"version": 3, "file": "toast.min.js", "sources": ["../../src/bootstrap/toast.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport { enableDismissTrigger } from './util/component-functions'\nimport { defineJQueryPlugin, reflow } from './util/index'\n\n/**\n * Constants\n */\n\nconst NAME = 'toast'\nconst DATA_KEY = 'bs.toast'\nconst EVENT_KEY = `.${DATA_KEY}`\n\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`\nconst EVENT_HIDE = `hide${EVENT_KEY}`\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`\nconst EVENT_SHOW = `show${EVENT_KEY}`\nconst EVENT_SHOWN = `shown${EVENT_KEY}`\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_HIDE = 'hide' // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show'\nconst CLASS_NAME_SHOWING = 'showing'\n\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n}\n\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n}\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config)\n\n    this._timeout = null\n    this._hasMouseInteraction = false\n    this._hasKeyboardInteraction = false\n    this._setListeners()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW)\n\n    if (showEvent.defaultPrevented) {\n      return\n    }\n\n    this._clearTimeout()\n\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE)\n    }\n\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING)\n      EventHandler.trigger(this._element, EVENT_SHOWN)\n\n      this._maybeScheduleHide()\n    }\n\n    this._element.classList.remove(CLASS_NAME_HIDE) // @deprecated\n    reflow(this._element)\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING)\n\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  hide() {\n    if (!this.isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE)\n\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE) // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW)\n      EventHandler.trigger(this._element, EVENT_HIDDEN)\n    }\n\n    this._element.classList.add(CLASS_NAME_SHOWING)\n    this._queueCallback(complete, this._element, this._config.animation)\n  }\n\n  dispose() {\n    this._clearTimeout()\n\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW)\n    }\n\n    super.dispose()\n  }\n\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return\n    }\n\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return\n    }\n\n    this._timeout = setTimeout(() => {\n      this.hide()\n    }, this._config.delay)\n  }\n\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout': {\n        this._hasMouseInteraction = isInteracting\n        break\n      }\n\n      case 'focusin':\n      case 'focusout': {\n        this._hasKeyboardInteraction = isInteracting\n        break\n      }\n\n      default: {\n        break\n      }\n    }\n\n    if (isInteracting) {\n      this._clearTimeout()\n      return\n    }\n\n    const nextElement = event.relatedTarget\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return\n    }\n\n    this._maybeScheduleHide()\n  }\n\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false))\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true))\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false))\n  }\n\n  _clearTimeout() {\n    clearTimeout(this._timeout)\n    this._timeout = null\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config)\n\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`)\n        }\n\n        data[config](this)\n      }\n    })\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast)\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast)\n\nexport default Toast\n"], "names": ["EVENT_KEY", "EVENT_MOUSEOVER", "EVENT_MOUSEOUT", "EVENT_FOCUSIN", "EVENT_FOCUSOUT", "EVENT_HIDE", "EVENT_HIDDEN", "EVENT_SHOW", "EVENT_SHOWN", "DefaultType", "animation", "autohide", "delay", "<PERSON><PERSON><PERSON>", "Toast", "BaseComponent", "constructor", "element", "config", "_timeout", "_hasMouseInteraction", "_hasKeyboardInteraction", "_setListeners", "NAME", "show", "EventHandler", "trigger", "this", "_element", "defaultPrevented", "_clearTimeout", "_config", "classList", "add", "remove", "_queueCallback", "_maybeScheduleHide", "hide", "isShown", "dispose", "contains", "setTimeout", "_onInteraction", "event", "isInteracting", "type", "nextElement", "relatedTarget", "on", "clearTimeout", "each", "data", "getOrCreateInstance", "TypeError"], "mappings": "oeAkBMA,qBADW,YAGXC,mCAA8BD,WAC9BE,iCAA4BF,WAC5BG,+BAA0BH,WAC1BI,iCAA4BJ,WAC5BK,yBAAoBL,WACpBM,6BAAwBN,WACxBO,yBAAoBP,WACpBQ,2BAAsBR,WAOtBS,YAAc,CAClBC,UAAW,UACXC,SAAU,UACVC,MAAO,UAGHC,QAAU,CACdH,WAAW,EACXC,UAAU,EACVC,MAAO,WAOHE,cAAcC,uBAClBC,YAAYC,QAASC,cACbD,QAASC,aAEVC,SAAW,UACXC,sBAAuB,OACvBC,yBAA0B,OAC1BC,gBAIIT,4BACFA,QAGEJ,gCACFA,YAGEc,wBArDA,QA0DXC,UACoBC,sBAAaC,QAAQC,KAAKC,SAAUrB,YAExCsB,6BAITC,gBAEDH,KAAKI,QAAQrB,gBACVkB,SAASI,UAAUC,IAvDN,aAiEfL,SAASI,UAAUE,OAhEJ,0BAiEbP,KAAKC,eACPA,SAASI,UAAUC,IAjEJ,OACG,gBAkElBE,gBAXY,UACVP,SAASI,UAAUE,OAxDH,iCAyDRR,QAAQC,KAAKC,SAAUpB,kBAE/B4B,uBAOuBT,KAAKC,SAAUD,KAAKI,QAAQrB,WAG5D2B,WACOV,KAAKW,oBAIQb,sBAAaC,QAAQC,KAAKC,SAAUvB,YAExCwB,6BAUTD,SAASI,UAAUC,IAtFD,gBAuFlBE,gBAPY,UACVP,SAASI,UAAUC,IAnFN,aAoFbL,SAASI,UAAUE,OAlFH,UADH,8BAoFLR,QAAQC,KAAKC,SAAUtB,gBAIRqB,KAAKC,SAAUD,KAAKI,QAAQrB,WAG5D6B,eACOT,gBAEDH,KAAKW,gBACFV,SAASI,UAAUE,OA/FN,cAkGdK,UAGRD,iBACSX,KAAKC,SAASI,UAAUQ,SAtGX,QA2GtBJ,qBACOT,KAAKI,QAAQpB,WAIdgB,KAAKP,sBAAwBO,KAAKN,+BAIjCF,SAAWsB,YAAW,UACpBJ,SACJV,KAAKI,QAAQnB,SAGlB8B,eAAeC,MAAOC,sBACZD,MAAME,UACP,gBACA,gBACEzB,qBAAuBwB,wBAIzB,cACA,gBACEvB,wBAA0BuB,iBAS/BA,+BACGd,sBAIDgB,YAAcH,MAAMI,cACtBpB,KAAKC,WAAakB,aAAenB,KAAKC,SAASY,SAASM,mBAIvDV,qBAGPd,sCACe0B,GAAGrB,KAAKC,SAAU3B,iBAAiB0C,OAAShB,KAAKe,eAAeC,OAAO,2BACvEK,GAAGrB,KAAKC,SAAU1B,gBAAgByC,OAAShB,KAAKe,eAAeC,OAAO,2BACtEK,GAAGrB,KAAKC,SAAUzB,eAAewC,OAAShB,KAAKe,eAAeC,OAAO,2BACrEK,GAAGrB,KAAKC,SAAUxB,gBAAgBuC,OAAShB,KAAKe,eAAeC,OAAO,KAGrFb,gBACEmB,aAAatB,KAAKR,eACbA,SAAW,4BAIKD,eACdS,KAAKuB,MAAK,iBACTC,KAAOrC,MAAMsC,oBAAoBzB,KAAMT,WAEvB,iBAAXA,OAAqB,SACF,IAAjBiC,KAAKjC,cACR,IAAImC,qCAA8BnC,aAG1CiC,KAAKjC,QAAQS,wDAUAb,qCAMFA,oBAEJA"}