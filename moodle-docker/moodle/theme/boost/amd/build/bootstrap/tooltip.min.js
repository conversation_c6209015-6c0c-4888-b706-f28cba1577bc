define("theme_boost/bootstrap/tooltip",["exports","core/popper2","./base-component","./dom/event-handler","./dom/manipulator","./util/index","./util/sanitizer","./util/template-factory"],(function(_exports,Popper,_baseComponent,_eventHandler,_manipulator,_index,_sanitizer,_templateFactory){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,Popper=function(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}newObj.default=obj,cache&&cache.set(obj,newObj);return newObj}(Popper),_baseComponent=_interopRequireDefault(_baseComponent),_eventHandler=_interopRequireDefault(_eventHandler),_manipulator=_interopRequireDefault(_manipulator),_templateFactory=_interopRequireDefault(_templateFactory);const DISALLOWED_ATTRIBUTES=new Set(["sanitize","allowList","sanitizeFn"]),SELECTOR_MODAL=".".concat("modal"),AttachmentMap={AUTO:"auto",TOP:"top",RIGHT:(0,_index.isRTL)()?"left":"right",BOTTOM:"bottom",LEFT:(0,_index.isRTL)()?"right":"left"},Default={allowList:_sanitizer.DefaultAllowlist,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},DefaultType={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Tooltip extends _baseComponent.default{constructor(element,config){if(void 0===Popper)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(element,config),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),_eventHandler.default.off(this._element.closest(SELECTOR_MODAL),"hide.bs.modal",this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const showEvent=_eventHandler.default.trigger(this._element,this.constructor.eventName("show")),isInTheDom=((0,_index.findShadowRoot)(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(showEvent.defaultPrevented||!isInTheDom)return;this._disposePopper();const tip=this._getTipElement();this._element.setAttribute("aria-describedby",tip.getAttribute("id"));const{container:container}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(container.append(tip),_eventHandler.default.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(tip),tip.classList.add("show"),"ontouchstart"in document.documentElement)for(const element of[].concat(...document.body.children))_eventHandler.default.on(element,"mouseover",_index.noop);this._queueCallback((()=>{_eventHandler.default.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(_eventHandler.default.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented)return;if(this._getTipElement().classList.remove("show"),"ontouchstart"in document.documentElement)for(const element of[].concat(...document.body.children))_eventHandler.default.off(element,"mouseover",_index.noop);this._activeTrigger.click=!1,this._activeTrigger.focus=!1,this._activeTrigger.hover=!1,this._isHovered=null;this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),_eventHandler.default.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(content){const tip=this._getTemplateFactory(content).toHtml();if(!tip)return null;tip.classList.remove("fade","show"),tip.classList.add("bs-".concat(this.constructor.NAME,"-auto"));const tipId=(0,_index.getUID)(this.constructor.NAME).toString();return tip.setAttribute("id",tipId),this._isAnimated()&&tip.classList.add("fade"),tip}setContent(content){this._newContent=content,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(content){return this._templateFactory?this._templateFactory.changeContent(content):this._templateFactory=new _templateFactory.default({...this._config,content:content,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(event){return this.constructor.getOrCreateInstance(event.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains("fade")}_isShown(){return this.tip&&this.tip.classList.contains("show")}_createPopper(tip){const placement=(0,_index.execute)(this._config.placement,[this,tip,this._element]),attachment=AttachmentMap[placement.toUpperCase()];return Popper.createPopper(this._element,tip,this._getPopperConfig(attachment))}_getOffset(){const{offset:offset}=this._config;return"string"==typeof offset?offset.split(",").map((value=>Number.parseInt(value,10))):"function"==typeof offset?popperData=>offset(popperData,this._element):offset}_resolvePossibleFunction(arg){return(0,_index.execute)(arg,[this._element])}_getPopperConfig(attachment){const defaultBsPopperConfig={placement:attachment,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:".".concat(this.constructor.NAME,"-arrow")}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:data=>{this._getTipElement().setAttribute("data-popper-placement",data.state.placement)}}]};return{...defaultBsPopperConfig,...(0,_index.execute)(this._config.popperConfig,[defaultBsPopperConfig])}}_setListeners(){const triggers=this._config.trigger.split(" ");for(const trigger of triggers)if("click"===trigger)_eventHandler.default.on(this._element,this.constructor.eventName("click"),this._config.selector,(event=>{this._initializeOnDelegatedTarget(event).toggle()}));else if("manual"!==trigger){const eventIn="hover"===trigger?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),eventOut="hover"===trigger?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");_eventHandler.default.on(this._element,eventIn,this._config.selector,(event=>{const context=this._initializeOnDelegatedTarget(event);context._activeTrigger["focusin"===event.type?"focus":"hover"]=!0,context._enter()})),_eventHandler.default.on(this._element,eventOut,this._config.selector,(event=>{const context=this._initializeOnDelegatedTarget(event);context._activeTrigger["focusout"===event.type?"focus":"hover"]=context._element.contains(event.relatedTarget),context._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},_eventHandler.default.on(this._element.closest(SELECTOR_MODAL),"hide.bs.modal",this._hideModalHandler)}_fixTitle(){const title=this._element.getAttribute("title");title&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",title),this._element.setAttribute("data-bs-original-title",title),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(handler,timeout){clearTimeout(this._timeout),this._timeout=setTimeout(handler,timeout)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(config){const dataAttributes=_manipulator.default.getDataAttributes(this._element);for(const dataAttribute of Object.keys(dataAttributes))DISALLOWED_ATTRIBUTES.has(dataAttribute)&&delete dataAttributes[dataAttribute];return config={...dataAttributes,..."object"==typeof config&&config?config:{}},config=this._mergeConfigObj(config),config=this._configAfterMerge(config),this._typeCheckConfig(config),config}_configAfterMerge(config){return config.container=!1===config.container?document.body:(0,_index.getElement)(config.container),"number"==typeof config.delay&&(config.delay={show:config.delay,hide:config.delay}),"number"==typeof config.title&&(config.title=config.title.toString()),"number"==typeof config.content&&(config.content=config.content.toString()),config}_getDelegateConfig(){const config={};for(const[key,value]of Object.entries(this._config))this.constructor.Default[key]!==value&&(config[key]=value);return config.selector=!1,config.trigger="manual",config}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(config){return this.each((function(){const data=Tooltip.getOrCreateInstance(this,config);if("string"==typeof config){if(void 0===data[config])throw new TypeError('No method named "'.concat(config,'"'));data[config]()}}))}}(0,_index.defineJQueryPlugin)(Tooltip);var _default=Tooltip;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=tooltip.min.js.map