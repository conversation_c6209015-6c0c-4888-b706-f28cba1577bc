{"version": 3, "file": "tooltip.min.js", "sources": ["../../src/bootstrap/tooltip.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport * as Popper from 'core/popper2'\nimport BaseComponent from './base-component'\nimport EventHandler from './dom/event-handler'\nimport Manipulator from './dom/manipulator'\nimport {\n  defineJQueryPlugin, execute, findShadowRoot, getElement, getUID, isRTL, noop\n} from './util/index'\nimport { DefaultAllowlist } from './util/sanitizer'\nimport TemplateFactory from './util/template-factory'\n\n/**\n * Constants\n */\n\nconst NAME = 'tooltip'\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn'])\n\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_MODAL = 'modal'\nconst CLASS_NAME_SHOW = 'show'\n\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner'\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`\n\nconst EVENT_MODAL_HIDE = 'hide.bs.modal'\n\nconst TRIGGER_HOVER = 'hover'\nconst TRIGGER_FOCUS = 'focus'\nconst TRIGGER_CLICK = 'click'\nconst TRIGGER_MANUAL = 'manual'\n\nconst EVENT_HIDE = 'hide'\nconst EVENT_HIDDEN = 'hidden'\nconst EVENT_SHOW = 'show'\nconst EVENT_SHOWN = 'shown'\nconst EVENT_INSERTED = 'inserted'\nconst EVENT_CLICK = 'click'\nconst EVENT_FOCUSIN = 'focusin'\nconst EVENT_FOCUSOUT = 'focusout'\nconst EVENT_MOUSEENTER = 'mouseenter'\nconst EVENT_MOUSELEAVE = 'mouseleave'\n\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n}\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 6],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' +\n            '<div class=\"tooltip-arrow\"></div>' +\n            '<div class=\"tooltip-inner\"></div>' +\n            '</div>',\n  title: '',\n  trigger: 'hover focus'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n}\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)')\n    }\n\n    super(element, config)\n\n    // Private\n    this._isEnabled = true\n    this._timeout = 0\n    this._isHovered = null\n    this._activeTrigger = {}\n    this._popper = null\n    this._templateFactory = null\n    this._newContent = null\n\n    // Protected\n    this.tip = null\n\n    this._setListeners()\n\n    if (!this._config.selector) {\n      this._fixTitle()\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true\n  }\n\n  disable() {\n    this._isEnabled = false\n  }\n\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled\n  }\n\n  toggle() {\n    if (!this._isEnabled) {\n      return\n    }\n\n    this._activeTrigger.click = !this._activeTrigger.click\n    if (this._isShown()) {\n      this._leave()\n      return\n    }\n\n    this._enter()\n  }\n\n  dispose() {\n    clearTimeout(this._timeout)\n\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'))\n    }\n\n    this._disposePopper()\n    super.dispose()\n  }\n\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements')\n    }\n\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return\n    }\n\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW))\n    const shadowRoot = findShadowRoot(this._element)\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element)\n\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper()\n\n    const tip = this._getTipElement()\n\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'))\n\n    const { container } = this._config\n\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip)\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED))\n    }\n\n    this._popper = this._createPopper(tip)\n\n    tip.classList.add(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop)\n      }\n    }\n\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN))\n\n      if (this._isHovered === false) {\n        this._leave()\n      }\n\n      this._isHovered = false\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  hide() {\n    if (!this._isShown()) {\n      return\n    }\n\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE))\n    if (hideEvent.defaultPrevented) {\n      return\n    }\n\n    const tip = this._getTipElement()\n    tip.classList.remove(CLASS_NAME_SHOW)\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop)\n      }\n    }\n\n    this._activeTrigger[TRIGGER_CLICK] = false\n    this._activeTrigger[TRIGGER_FOCUS] = false\n    this._activeTrigger[TRIGGER_HOVER] = false\n    this._isHovered = null // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return\n      }\n\n      if (!this._isHovered) {\n        this._disposePopper()\n      }\n\n      this._element.removeAttribute('aria-describedby')\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN))\n    }\n\n    this._queueCallback(complete, this.tip, this._isAnimated())\n  }\n\n  update() {\n    if (this._popper) {\n      this._popper.update()\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle())\n  }\n\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate())\n    }\n\n    return this.tip\n  }\n\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml()\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null\n    }\n\n    tip.classList.remove(CLASS_NAME_FADE, CLASS_NAME_SHOW)\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`)\n\n    const tipId = getUID(this.constructor.NAME).toString()\n\n    tip.setAttribute('id', tipId)\n\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE)\n    }\n\n    return tip\n  }\n\n  setContent(content) {\n    this._newContent = content\n    if (this._isShown()) {\n      this._disposePopper()\n      this.show()\n    }\n  }\n\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content)\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      })\n    }\n\n    return this._templateFactory\n  }\n\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    }\n  }\n\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title')\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig())\n  }\n\n  _isAnimated() {\n    return this._config.animation || (this.tip && this.tip.classList.contains(CLASS_NAME_FADE))\n  }\n\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW)\n  }\n\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element])\n    const attachment = AttachmentMap[placement.toUpperCase()]\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment))\n  }\n\n  _getOffset() {\n    const { offset } = this._config\n\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10))\n    }\n\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element)\n    }\n\n    return offset\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element])\n  }\n\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [\n        {\n          name: 'flip',\n          options: {\n            fallbackPlacements: this._config.fallbackPlacements\n          }\n        },\n        {\n          name: 'offset',\n          options: {\n            offset: this._getOffset()\n          }\n        },\n        {\n          name: 'preventOverflow',\n          options: {\n            boundary: this._config.boundary\n          }\n        },\n        {\n          name: 'arrow',\n          options: {\n            element: `.${this.constructor.NAME}-arrow`\n          }\n        },\n        {\n          name: 'preSetPlacement',\n          enabled: true,\n          phase: 'beforeMain',\n          fn: data => {\n            // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n            // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n            this._getTipElement().setAttribute('data-popper-placement', data.state.placement)\n          }\n        }\n      ]\n    }\n\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    }\n  }\n\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ')\n\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context.toggle()\n        })\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSEENTER) :\n          this.constructor.eventName(EVENT_FOCUSIN)\n        const eventOut = trigger === TRIGGER_HOVER ?\n          this.constructor.eventName(EVENT_MOUSELEAVE) :\n          this.constructor.eventName(EVENT_FOCUSOUT)\n\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true\n          context._enter()\n        })\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event)\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] =\n            context._element.contains(event.relatedTarget)\n\n          context._leave()\n        })\n      }\n    }\n\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide()\n      }\n    }\n\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler)\n  }\n\n  _fixTitle() {\n    const title = this._element.getAttribute('title')\n\n    if (!title) {\n      return\n    }\n\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title)\n    }\n\n    this._element.setAttribute('data-bs-original-title', title) // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title')\n  }\n\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true\n      return\n    }\n\n    this._isHovered = true\n\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show()\n      }\n    }, this._config.delay.show)\n  }\n\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return\n    }\n\n    this._isHovered = false\n\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide()\n      }\n    }, this._config.delay.hide)\n  }\n\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout)\n    this._timeout = setTimeout(handler, timeout)\n  }\n\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true)\n  }\n\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element)\n\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute]\n      }\n    }\n\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    }\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container)\n\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      }\n    }\n\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString()\n    }\n\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString()\n    }\n\n    return config\n  }\n\n  _getDelegateConfig() {\n    const config = {}\n\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value\n      }\n    }\n\n    config.selector = false\n    config.trigger = 'manual'\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config\n  }\n\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy()\n      this._popper = null\n    }\n\n    if (this.tip) {\n      this.tip.remove()\n      this.tip = null\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config)\n\n      if (typeof config !== 'string') {\n        return\n      }\n\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`)\n      }\n\n      data[config]()\n    })\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip)\n\nexport default Tooltip\n"], "names": ["DISALLOWED_ATTRIBUTES", "Set", "SELECTOR_MODAL", "AttachmentMap", "AUTO", "TOP", "RIGHT", "BOTTOM", "LEFT", "<PERSON><PERSON><PERSON>", "allowList", "DefaultAllowlist", "animation", "boundary", "container", "customClass", "delay", "fallbackPlacements", "html", "offset", "placement", "popperConfig", "sanitize", "sanitizeFn", "selector", "template", "title", "trigger", "DefaultType", "<PERSON><PERSON><PERSON>", "BaseComponent", "constructor", "element", "config", "<PERSON><PERSON>", "TypeError", "_isEnabled", "_timeout", "_isHovered", "_activeTrigger", "_popper", "_templateFactory", "_newContent", "tip", "_setListeners", "this", "_config", "_fixTitle", "NAME", "enable", "disable", "toggle<PERSON>nabled", "toggle", "click", "_isShown", "_leave", "_enter", "dispose", "clearTimeout", "off", "_element", "closest", "_hideModalHandler", "getAttribute", "setAttribute", "_disposePopper", "show", "style", "display", "Error", "_isWithContent", "showEvent", "EventHandler", "eventName", "isInTheDom", "ownerDocument", "documentElement", "contains", "defaultPrevented", "_getTipElement", "append", "_createPopper", "classList", "add", "document", "concat", "body", "children", "on", "noop", "_queueCallback", "_isAnimated", "hide", "remove", "_isWithActiveTrigger", "removeAttribute", "update", "Boolean", "_getTitle", "_createTipElement", "_getContentForTemplate", "content", "_getTemplateFactory", "toHtml", "tipId", "toString", "<PERSON><PERSON><PERSON><PERSON>", "changeContent", "TemplateFactory", "extraClass", "_resolvePossibleFunction", "_initializeOnDelegatedTarget", "event", "getOrCreateInstance", "<PERSON><PERSON><PERSON><PERSON>", "_getDelegateConfig", "attachment", "toUpperCase", "createPopper", "_getPopperConfig", "_getOffset", "split", "map", "value", "Number", "parseInt", "popperData", "arg", "defaultBsPopperConfig", "modifiers", "name", "options", "enabled", "phase", "fn", "data", "state", "triggers", "eventIn", "eventOut", "context", "type", "relatedTarget", "textContent", "trim", "_setTimeout", "handler", "timeout", "setTimeout", "Object", "values", "includes", "_getConfig", "dataAttributes", "Manipulator", "getDataAttributes", "dataAttribute", "keys", "has", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "key", "entries", "destroy", "each"], "mappings": "glDAsBMA,sBAAwB,IAAIC,IAAI,CAAC,WAAY,YAAa,eAO1DC,0BAJmB,SAwBnBC,cAAgB,CACpBC,KAAM,OACNC,IAAK,MACLC,OAAO,kBAAU,OAAS,QAC1BC,OAAQ,SACRC,MAAM,kBAAU,QAAU,QAGtBC,QAAU,CACdC,UAAWC,4BACXC,WAAW,EACXC,SAAU,kBACVC,WAAW,EACXC,YAAa,GACbC,MAAO,EACPC,mBAAoB,CAAC,MAAO,QAAS,SAAU,QAC/CC,MAAM,EACNC,OAAQ,CAAC,EAAG,GACZC,UAAW,MACXC,aAAc,KACdC,UAAU,EACVC,WAAY,KACZC,UAAU,EACVC,SAAU,+GAIVC,MAAO,GACPC,QAAS,eAGLC,YAAc,CAClBlB,UAAW,SACXE,UAAW,UACXC,SAAU,mBACVC,UAAW,2BACXC,YAAa,oBACbC,MAAO,kBACPC,mBAAoB,QACpBC,KAAM,UACNC,OAAQ,0BACRC,UAAW,oBACXC,aAAc,yBACdC,SAAU,UACVC,WAAY,kBACZC,SAAU,mBACVC,SAAU,SACVC,MAAO,4BACPC,QAAS,gBAOLE,gBAAgBC,uBACpBC,YAAYC,QAASC,gBACG,IAAXC,aACH,IAAIC,UAAU,qEAGhBH,QAASC,aAGVG,YAAa,OACbC,SAAW,OACXC,WAAa,UACbC,eAAiB,QACjBC,QAAU,UACVC,iBAAmB,UACnBC,YAAc,UAGdC,IAAM,UAENC,gBAEAC,KAAKC,QAAQtB,eACXuB,YAKEtC,4BACFA,QAGEmB,gCACFA,YAGEoB,wBAvHA,UA4HXC,cACOb,YAAa,EAGpBc,eACOd,YAAa,EAGpBe,qBACOf,YAAcS,KAAKT,WAG1BgB,SACOP,KAAKT,kBAILG,eAAec,OAASR,KAAKN,eAAec,MAC7CR,KAAKS,gBACFC,cAIFC,UAGPC,UACEC,aAAab,KAAKR,gCAELsB,IAAId,KAAKe,SAASC,QAAQ3D,gBA/IlB,gBA+IqD2C,KAAKiB,mBAE3EjB,KAAKe,SAASG,aAAa,gCACxBH,SAASI,aAAa,QAASnB,KAAKe,SAASG,aAAa,gCAG5DE,uBACCR,UAGRS,UACsC,SAAhCrB,KAAKe,SAASO,MAAMC,cAChB,IAAIC,MAAM,2CAGZxB,KAAKyB,mBAAoBzB,KAAKT,wBAI9BmC,UAAYC,sBAAa7C,QAAQkB,KAAKe,SAAUf,KAAKd,YAAY0C,UAzJxD,SA2JTC,aADa,yBAAe7B,KAAKe,WACLf,KAAKe,SAASe,cAAcC,iBAAiBC,SAAShC,KAAKe,aAEzFW,UAAUO,mBAAqBJ,uBAK9BT,uBAECtB,IAAME,KAAKkC,sBAEZnB,SAASI,aAAa,mBAAoBrB,IAAIoB,aAAa,aAE1DjD,UAAEA,WAAc+B,KAAKC,WAEtBD,KAAKe,SAASe,cAAcC,gBAAgBC,SAAShC,KAAKF,OAC7D7B,UAAUkE,OAAOrC,2BACJhB,QAAQkB,KAAKe,SAAUf,KAAKd,YAAY0C,UA1KpC,mBA6KdjC,QAAUK,KAAKoC,cAActC,KAElCA,IAAIuC,UAAUC,IA/LM,QAqMhB,iBAAkBC,SAASR,oBACxB,MAAM5C,UAAW,GAAGqD,UAAUD,SAASE,KAAKC,gCAClCC,GAAGxD,QAAS,YAAayD,kBAcrCC,gBAVY,2BACF/D,QAAQkB,KAAKe,SAAUf,KAAKd,YAAY0C,UA7LvC,WA+LU,IAApB5B,KAAKP,iBACFiB,cAGFjB,YAAa,IAGUO,KAAKF,IAAKE,KAAK8C,eAG/CC,WACO/C,KAAKS,qBAIQkB,sBAAa7C,QAAQkB,KAAKe,SAAUf,KAAKd,YAAY0C,UAjNxD,SAkNDK,2BAIFjC,KAAKkC,iBACbG,UAAUW,OAnOM,QAuOhB,iBAAkBT,SAASR,oBACxB,MAAM5C,UAAW,GAAGqD,UAAUD,SAASE,KAAKC,gCAClC5B,IAAI3B,QAAS,YAAayD,kBAItClD,eAAL,OAAqC,OAChCA,eAAL,OAAqC,OAChCA,eAAL,OAAqC,OAChCD,WAAa,UAeboD,gBAbY,KACX7C,KAAKiD,yBAIJjD,KAAKP,iBACH2B,sBAGFL,SAASmC,gBAAgB,0CACjBpE,QAAQkB,KAAKe,SAAUf,KAAKd,YAAY0C,UA/OtC,cAkPa5B,KAAKF,IAAKE,KAAK8C,eAG/CK,SACMnD,KAAKL,cACFA,QAAQwD,SAKjB1B,wBACS2B,QAAQpD,KAAKqD,aAGtBnB,wBACOlC,KAAKF,WACHA,IAAME,KAAKsD,kBAAkBtD,KAAKH,aAAeG,KAAKuD,2BAGtDvD,KAAKF,IAGdwD,kBAAkBE,eACV1D,IAAME,KAAKyD,oBAAoBD,SAASE,aAGzC5D,WACI,KAGTA,IAAIuC,UAAUW,OA/RM,OAEA,QA+RpBlD,IAAIuC,UAAUC,iBAAUtC,KAAKd,YAAYiB,qBAEnCwD,OAAQ,iBAAO3D,KAAKd,YAAYiB,MAAMyD,kBAE5C9D,IAAIqB,aAAa,KAAMwC,OAEnB3D,KAAK8C,eACPhD,IAAIuC,UAAUC,IAxSI,QA2SbxC,IAGT+D,WAAWL,cACJ3D,YAAc2D,QACfxD,KAAKS,kBACFW,sBACAC,QAIToC,oBAAoBD,gBACdxD,KAAKJ,sBACFA,iBAAiBkE,cAAcN,cAE/B5D,iBAAmB,IAAImE,yBAAgB,IACvC/D,KAAKC,QAGRuD,QAAAA,QACAQ,WAAYhE,KAAKiE,yBAAyBjE,KAAKC,QAAQ/B,eAIpD8B,KAAKJ,iBAGd2D,+BACS,kBACqBvD,KAAKqD,aAInCA,mBACSrD,KAAKiE,yBAAyBjE,KAAKC,QAAQpB,QAAUmB,KAAKe,SAASG,aAAa,0BAIzFgD,6BAA6BC,cACpBnE,KAAKd,YAAYkF,oBAAoBD,MAAME,eAAgBrE,KAAKsE,sBAGzExB,qBACS9C,KAAKC,QAAQlC,WAAciC,KAAKF,KAAOE,KAAKF,IAAIuC,UAAUL,SAtV7C,QAyVtBvB,kBACST,KAAKF,KAAOE,KAAKF,IAAIuC,UAAUL,SAxVlB,QA2VtBI,cAActC,WACNvB,WAAY,kBAAQyB,KAAKC,QAAQ1B,UAAW,CAACyB,KAAMF,IAAKE,KAAKe,WAC7DwD,WAAajH,cAAciB,UAAUiG,sBACpCnF,OAAOoF,aAAazE,KAAKe,SAAUjB,IAAKE,KAAK0E,iBAAiBH,aAGvEI,mBACQrG,OAAEA,QAAW0B,KAAKC,cAEF,iBAAX3B,OACFA,OAAOsG,MAAM,KAAKC,KAAIC,OAASC,OAAOC,SAASF,MAAO,MAGzC,mBAAXxG,OACF2G,YAAc3G,OAAO2G,WAAYjF,KAAKe,UAGxCzC,OAGT2F,yBAAyBiB,YAChB,kBAAQA,IAAK,CAAClF,KAAKe,WAG5B2D,iBAAiBH,kBACTY,sBAAwB,CAC5B5G,UAAWgG,WACXa,UAAW,CACT,CACEC,KAAM,OACNC,QAAS,CACPlH,mBAAoB4B,KAAKC,QAAQ7B,qBAGrC,CACEiH,KAAM,SACNC,QAAS,CACPhH,OAAQ0B,KAAK2E,eAGjB,CACEU,KAAM,kBACNC,QAAS,CACPtH,SAAUgC,KAAKC,QAAQjC,WAG3B,CACEqH,KAAM,QACNC,QAAS,CACPnG,mBAAaa,KAAKd,YAAYiB,iBAGlC,CACEkF,KAAM,kBACNE,SAAS,EACTC,MAAO,aACPC,GAAIC,YAGGxD,iBAAiBf,aAAa,wBAAyBuE,KAAKC,MAAMpH,qBAMxE,IACF4G,0BACA,kBAAQnF,KAAKC,QAAQzB,aAAc,CAAC2G,yBAI3CpF,sBACQ6F,SAAW5F,KAAKC,QAAQnB,QAAQ8F,MAAM,SAEvC,MAAM9F,WAAW8G,YACJ,UAAZ9G,8BACW6D,GAAG3C,KAAKe,SAAUf,KAAKd,YAAY0C,UAtZpC,SAsZ4D5B,KAAKC,QAAQtB,UAAUwF,QAC7EnE,KAAKkE,6BAA6BC,OAC1C5D,iBAEL,GAjaU,WAiaNzB,QAA4B,OAC/B+G,QAraQ,UAqaE/G,QACdkB,KAAKd,YAAY0C,UAzZF,cA0Zf5B,KAAKd,YAAY0C,UA5ZL,WA6ZRkE,SAxaQ,UAwaGhH,QACfkB,KAAKd,YAAY0C,UA3ZF,cA4Zf5B,KAAKd,YAAY0C,UA9ZJ,kCAgaFe,GAAG3C,KAAKe,SAAU8E,QAAS7F,KAAKC,QAAQtB,UAAUwF,cACvD4B,QAAU/F,KAAKkE,6BAA6BC,OAClD4B,QAAQrG,eAA8B,YAAfyE,MAAM6B,KA7ajB,QADA,UA8auE,EACnFD,QAAQpF,kCAEGgC,GAAG3C,KAAKe,SAAU+E,SAAU9F,KAAKC,QAAQtB,UAAUwF,cACxD4B,QAAU/F,KAAKkE,6BAA6BC,OAClD4B,QAAQrG,eAA8B,aAAfyE,MAAM6B,KAlbjB,QADA,SAobVD,QAAQhF,SAASiB,SAASmC,MAAM8B,eAElCF,QAAQrF,iBAKTO,kBAAoB,KACnBjB,KAAKe,eACFgC,8BAIIJ,GAAG3C,KAAKe,SAASC,QAAQ3D,gBAncjB,gBAmcoD2C,KAAKiB,mBAGhFf,kBACQrB,MAAQmB,KAAKe,SAASG,aAAa,SAEpCrC,QAIAmB,KAAKe,SAASG,aAAa,eAAkBlB,KAAKe,SAASmF,YAAYC,aACrEpF,SAASI,aAAa,aAActC,YAGtCkC,SAASI,aAAa,yBAA0BtC,YAChDkC,SAASmC,gBAAgB,UAGhCvC,SACMX,KAAKS,YAAcT,KAAKP,gBACrBA,YAAa,QAIfA,YAAa,OAEb2G,aAAY,KACXpG,KAAKP,iBACF4B,SAENrB,KAAKC,QAAQ9B,MAAMkD,OAGxBX,SACMV,KAAKiD,8BAIJxD,YAAa,OAEb2G,aAAY,KACVpG,KAAKP,iBACHsD,SAEN/C,KAAKC,QAAQ9B,MAAM4E,OAGxBqD,YAAYC,QAASC,SACnBzF,aAAab,KAAKR,eACbA,SAAW+G,WAAWF,QAASC,SAGtCrD,8BACSuD,OAAOC,OAAOzG,KAAKN,gBAAgBgH,UAAS,GAGrDC,WAAWvH,cACHwH,eAAiBC,qBAAYC,kBAAkB9G,KAAKe,cAErD,MAAMgG,iBAAiBP,OAAOQ,KAAKJ,gBAClCzJ,sBAAsB8J,IAAIF,uBACrBH,eAAeG,sBAI1B3H,OAAS,IACJwH,kBACmB,iBAAXxH,QAAuBA,OAASA,OAAS,IAEtDA,OAASY,KAAKkH,gBAAgB9H,QAC9BA,OAASY,KAAKmH,kBAAkB/H,aAC3BgI,iBAAiBhI,QACfA,OAGT+H,kBAAkB/H,eAChBA,OAAOnB,WAAiC,IAArBmB,OAAOnB,UAAsBsE,SAASE,MAAO,qBAAWrD,OAAOnB,WAEtD,iBAAjBmB,OAAOjB,QAChBiB,OAAOjB,MAAQ,CACbkD,KAAMjC,OAAOjB,MACb4E,KAAM3D,OAAOjB,QAIW,iBAAjBiB,OAAOP,QAChBO,OAAOP,MAAQO,OAAOP,MAAM+E,YAGA,iBAAnBxE,OAAOoE,UAChBpE,OAAOoE,QAAUpE,OAAOoE,QAAQI,YAG3BxE,OAGTkF,2BACQlF,OAAS,OAEV,MAAOiI,IAAKvC,SAAU0B,OAAOc,QAAQtH,KAAKC,SACzCD,KAAKd,YAAYtB,QAAQyJ,OAASvC,QACpC1F,OAAOiI,KAAOvC,cAIlB1F,OAAOT,UAAW,EAClBS,OAAON,QAAU,SAKVM,OAGTgC,iBACMpB,KAAKL,eACFA,QAAQ4H,eACR5H,QAAU,MAGbK,KAAKF,WACFA,IAAIkD,cACJlD,IAAM,6BAKQV,eACdY,KAAKwH,MAAK,iBACT9B,KAAO1G,QAAQoF,oBAAoBpE,KAAMZ,WAEzB,iBAAXA,gBAIiB,IAAjBsG,KAAKtG,cACR,IAAIE,qCAA8BF,aAG1CsG,KAAKtG,6CASQJ,sBAEJA"}