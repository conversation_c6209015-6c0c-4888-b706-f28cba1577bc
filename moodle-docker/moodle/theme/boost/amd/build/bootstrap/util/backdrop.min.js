define("theme_boost/bootstrap/util/backdrop",["exports","../dom/event-handler","./config","./index"],(function(_exports,_eventHandler,_config,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_eventHandler=_interopRequireDefault(_eventHandler),_config=_interopRequireDefault(_config);const EVENT_MOUSEDOWN="mousedown.bs.".concat("backdrop"),Default={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},DefaultType={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class Backdrop extends _config.default{constructor(config){super(),this._config=this._getConfig(config),this._isAppended=!1,this._element=null}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"backdrop"}show(callback){if(!this._config.isVisible)return void(0,_index.execute)(callback);this._append();const element=this._getElement();this._config.isAnimated&&(0,_index.reflow)(element),element.classList.add("show"),this._emulateAnimation((()=>{(0,_index.execute)(callback)}))}hide(callback){this._config.isVisible?(this._getElement().classList.remove("show"),this._emulateAnimation((()=>{this.dispose(),(0,_index.execute)(callback)}))):(0,_index.execute)(callback)}dispose(){this._isAppended&&(_eventHandler.default.off(this._element,EVENT_MOUSEDOWN),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const backdrop=document.createElement("div");backdrop.className=this._config.className,this._config.isAnimated&&backdrop.classList.add("fade"),this._element=backdrop}return this._element}_configAfterMerge(config){return config.rootElement=(0,_index.getElement)(config.rootElement),config}_append(){if(this._isAppended)return;const element=this._getElement();this._config.rootElement.append(element),_eventHandler.default.on(element,EVENT_MOUSEDOWN,(()=>{(0,_index.execute)(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(callback){(0,_index.executeAfterTransition)(callback,this._getElement(),this._config.isAnimated)}}var _default=Backdrop;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=backdrop.min.js.map