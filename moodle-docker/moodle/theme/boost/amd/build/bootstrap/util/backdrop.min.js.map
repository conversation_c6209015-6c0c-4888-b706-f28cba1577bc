{"version": 3, "file": "backdrop.min.js", "sources": ["../../../src/bootstrap/util/backdrop.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport Config from './config'\nimport {\n  execute, executeAfterTransition, getElement, reflow\n} from './index'\n\n/**\n * Constants\n */\n\nconst NAME = 'backdrop'\nconst CLASS_NAME_FADE = 'fade'\nconst CLASS_NAME_SHOW = 'show'\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME}`\n\nconst Default = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true, // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n}\n\nconst DefaultType = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n}\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isAppended = false\n    this._element = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._append()\n\n    const element = this._getElement()\n    if (this._config.isAnimated) {\n      reflow(element)\n    }\n\n    element.classList.add(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      execute(callback)\n    })\n  }\n\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback)\n      return\n    }\n\n    this._getElement().classList.remove(CLASS_NAME_SHOW)\n\n    this._emulateAnimation(() => {\n      this.dispose()\n      execute(callback)\n    })\n  }\n\n  dispose() {\n    if (!this._isAppended) {\n      return\n    }\n\n    EventHandler.off(this._element, EVENT_MOUSEDOWN)\n\n    this._element.remove()\n    this._isAppended = false\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div')\n      backdrop.className = this._config.className\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE)\n      }\n\n      this._element = backdrop\n    }\n\n    return this._element\n  }\n\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement)\n    return config\n  }\n\n  _append() {\n    if (this._isAppended) {\n      return\n    }\n\n    const element = this._getElement()\n    this._config.rootElement.append(element)\n\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback)\n    })\n\n    this._isAppended = true\n  }\n\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated)\n  }\n}\n\nexport default Backdrop\n"], "names": ["EVENT_MOUSEDOWN", "<PERSON><PERSON><PERSON>", "className", "clickCallback", "isAnimated", "isVisible", "rootElement", "DefaultType", "Backdrop", "Config", "constructor", "config", "_config", "this", "_getConfig", "_isAppended", "_element", "NAME", "show", "callback", "_append", "element", "_getElement", "classList", "add", "_emulateAnimation", "hide", "remove", "dispose", "off", "backdrop", "document", "createElement", "_configAfterMerge", "append", "on"], "mappings": "0ZAoBMA,uCAHO,YAKPC,QAAU,CACdC,UAAW,iBACXC,cAAe,KACfC,YAAY,EACZC,WAAW,EACXC,YAAa,QAGTC,YAAc,CAClBL,UAAW,SACXC,cAAe,kBACfC,WAAY,UACZC,UAAW,UACXC,YAAa,0BAOTE,iBAAiBC,gBACrBC,YAAYC,qBAELC,QAAUC,KAAKC,WAAWH,aAC1BI,aAAc,OACdC,SAAW,KAIPf,4BACFA,QAGEM,gCACFA,YAGEU,wBA1CA,WA+CXC,KAAKC,cACEN,KAAKD,QAAQP,wCACRc,eAILC,gBAECC,QAAUR,KAAKS,cACjBT,KAAKD,QAAQR,8BACRiB,SAGTA,QAAQE,UAAUC,IA1DE,aA4DfC,mBAAkB,wBACbN,aAIZO,KAAKP,UACEN,KAAKD,QAAQP,gBAKbiB,cAAcC,UAAUI,OAvET,aAyEfF,mBAAkB,UAChBG,6BACGT,iCARAA,UAYZS,UACOf,KAAKE,oCAIGc,IAAIhB,KAAKG,SAAUhB,sBAE3BgB,SAASW,cACTZ,aAAc,GAIrBO,kBACOT,KAAKG,SAAU,OACZc,SAAWC,SAASC,cAAc,OACxCF,SAAS5B,UAAYW,KAAKD,QAAQV,UAC9BW,KAAKD,QAAQR,YACf0B,SAASP,UAAUC,IAjGH,aAoGbR,SAAWc,gBAGXjB,KAAKG,SAGdiB,kBAAkBtB,eAEhBA,OAAOL,aAAc,qBAAWK,OAAOL,aAChCK,OAGTS,aACMP,KAAKE,yBAIHM,QAAUR,KAAKS,mBAChBV,QAAQN,YAAY4B,OAAOb,+BAEnBc,GAAGd,QAASrB,iBAAiB,wBAChCa,KAAKD,QAAQT,uBAGlBY,aAAc,EAGrBU,kBAAkBN,4CACOA,SAAUN,KAAKS,cAAeT,KAAKD,QAAQR,0BAIvDI"}