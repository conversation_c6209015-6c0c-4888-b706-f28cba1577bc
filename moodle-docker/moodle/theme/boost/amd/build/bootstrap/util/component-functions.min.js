define("theme_boost/bootstrap/util/component-functions",["exports","../dom/event-handler","../dom/selector-engine","./index"],(function(_exports,_eventHandler,_selectorEngine,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.enableDismissTrigger=void 0,_eventHandler=_interopRequireDefault(_eventHandler),_selectorEngine=_interopRequireDefault(_selectorEngine);_exports.enableDismissTrigger=function(component){let method=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"hide";const clickEvent="click.dismiss".concat(component.EVENT_KEY),name=component.NAME;_eventHandler.default.on(document,clickEvent,'[data-bs-dismiss="'.concat(name,'"]'),(function(event){if(["A","AREA"].includes(this.tagName)&&event.preventDefault(),(0,_index.isDisabled)(this))return;const target=_selectorEngine.default.getElementFromSelector(this)||this.closest(".".concat(name));component.getOrCreateInstance(target)[method]()}))}}));

//# sourceMappingURL=component-functions.min.js.map