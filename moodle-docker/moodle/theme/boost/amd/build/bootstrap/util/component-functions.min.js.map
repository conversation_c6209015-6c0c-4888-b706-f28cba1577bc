{"version": 3, "file": "component-functions.min.js", "sources": ["../../../src/bootstrap/util/component-functions.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport { isDisabled } from './index'\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`\n  const name = component.NAME\n\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault()\n    }\n\n    if (isDisabled(this)) {\n      return\n    }\n\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`)\n    const instance = component.getOrCreateInstance(target)\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]()\n  })\n}\n\nexport {\n  enableDismissTrigger\n}\n"], "names": ["component", "method", "clickEvent", "EVENT_KEY", "name", "NAME", "on", "document", "event", "includes", "this", "tagName", "preventDefault", "target", "SelectorEngine", "getElementFromSelector", "closest", "getOrCreateInstance"], "mappings": "gfAW6B,SAACA,eAAWC,8DAAS,aAC1CC,kCAA6BF,UAAUG,WACvCC,KAAOJ,UAAUK,2BAEVC,GAAGC,SAAUL,uCAAiCE,YAAU,SAAUI,UACzE,CAAC,IAAK,QAAQC,SAASC,KAAKC,UAC9BH,MAAMI,kBAGJ,qBAAWF,mBAITG,OAASC,wBAAeC,uBAAuBL,OAASA,KAAKM,mBAAYZ,OAC9DJ,UAAUiB,oBAAoBJ,QAGtCZ"}