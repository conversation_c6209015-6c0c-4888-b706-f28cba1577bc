define("theme_boost/bootstrap/util/config",["exports","../dom/manipulator","./index"],(function(_exports,_manipulator,_index){var obj;Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_manipulator=(obj=_manipulator)&&obj.__esModule?obj:{default:obj};var _default=class{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(config){return config=this._mergeConfigObj(config),config=this._configAfterMerge(config),this._typeCheckConfig(config),config}_configAfterMerge(config){return config}_mergeConfigObj(config,element){const jsonConfig=(0,_index.isElement)(element)?_manipulator.default.getDataAttribute(element,"config"):{};return{...this.constructor.Default,..."object"==typeof jsonConfig?jsonConfig:{},...(0,_index.isElement)(element)?_manipulator.default.getDataAttributes(element):{},..."object"==typeof config?config:{}}}_typeCheckConfig(config){let configTypes=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.constructor.DefaultType;for(const[property,expectedTypes]of Object.entries(configTypes)){const value=config[property],valueType=(0,_index.isElement)(value)?"element":(0,_index.toType)(value);if(!new RegExp(expectedTypes).test(valueType))throw new TypeError("".concat(this.constructor.NAME.toUpperCase(),': Option "').concat(property,'" provided type "').concat(valueType,'" but expected type "').concat(expectedTypes,'".'))}}};return _exports.default=_default,_exports.default}));

//# sourceMappingURL=config.min.js.map