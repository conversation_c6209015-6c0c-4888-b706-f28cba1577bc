{"version": 3, "file": "config.min.js", "sources": ["../../../src/bootstrap/util/config.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator'\nimport { isElement, toType } from './index'\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {}\n  }\n\n  static get DefaultType() {\n    return {}\n  }\n\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!')\n  }\n\n  _getConfig(config) {\n    config = this._mergeConfigObj(config)\n    config = this._configAfterMerge(config)\n    this._typeCheckConfig(config)\n    return config\n  }\n\n  _configAfterMerge(config) {\n    return config\n  }\n\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {} // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    }\n  }\n\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property]\n      const valueType = isElement(value) ? 'element' : toType(value)\n\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(\n          `${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`\n        )\n      }\n    }\n  }\n}\n\nexport default Config\n"], "names": ["<PERSON><PERSON><PERSON>", "DefaultType", "NAME", "Error", "_getConfig", "config", "this", "_mergeConfigObj", "_configAfterMerge", "_typeCheckConfig", "element", "jsonConfig", "Manipulator", "getDataAttribute", "constructor", "getDataAttributes", "configTypes", "property", "expectedTypes", "Object", "entries", "value", "valueType", "RegExp", "test", "TypeError", "toUpperCase"], "mappings": "2SAgBaA,2BACF,GAGEC,+BACF,GAGEC,wBACH,IAAIC,MAAM,uEAGlBC,WAAWC,eACTA,OAASC,KAAKC,gBAAgBF,QAC9BA,OAASC,KAAKE,kBAAkBH,aAC3BI,iBAAiBJ,QACfA,OAGTG,kBAAkBH,eACTA,OAGTE,gBAAgBF,OAAQK,eAChBC,YAAa,oBAAUD,SAAWE,qBAAYC,iBAAiBH,QAAS,UAAY,SAEnF,IACFJ,KAAKQ,YAAYd,WACM,iBAAfW,WAA0BA,WAAa,OAC9C,oBAAUD,SAAWE,qBAAYG,kBAAkBL,SAAW,MAC5C,iBAAXL,OAAsBA,OAAS,IAI9CI,iBAAiBJ,YAAQW,mEAAcV,KAAKQ,YAAYb,gBACjD,MAAOgB,SAAUC,iBAAkBC,OAAOC,QAAQJ,aAAc,OAC7DK,MAAQhB,OAAOY,UACfK,WAAY,oBAAUD,OAAS,WAAY,iBAAOA,WAEnD,IAAIE,OAAOL,eAAeM,KAAKF,iBAC5B,IAAIG,oBACLnB,KAAKQ,YAAYZ,KAAKwB,mCAA0BT,qCAA4BK,0CAAiCJ"}