define("theme_boost/bootstrap/util/focustrap",["exports","../dom/event-handler","../dom/selector-engine","./config"],(function(_exports,_eventHandler,_selectorEngine,_config){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_eventHandler=_interopRequireDefault(_eventHandler),_selectorEngine=_interopRequireDefault(_selectorEngine),_config=_interopRequireDefault(_config);const EVENT_KEY=".".concat("bs.focustrap"),EVENT_FOCUSIN="focusin".concat(EVENT_KEY),EVENT_KEYDOWN_TAB="keydown.tab".concat(EVENT_KEY),Default={autofocus:!0,trapElement:null},DefaultType={autofocus:"boolean",trapElement:"element"};class FocusTrap extends _config.default{constructor(config){super(),this._config=this._getConfig(config),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),_eventHandler.default.off(document,EVENT_KEY),_eventHandler.default.on(document,EVENT_FOCUSIN,(event=>this._handleFocusin(event))),_eventHandler.default.on(document,EVENT_KEYDOWN_TAB,(event=>this._handleKeydown(event))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,_eventHandler.default.off(document,EVENT_KEY))}_handleFocusin(event){const{trapElement:trapElement}=this._config;if(event.target===document||event.target===trapElement||trapElement.contains(event.target))return;const elements=_selectorEngine.default.focusableChildren(trapElement);0===elements.length?trapElement.focus():"backward"===this._lastTabNavDirection?elements[elements.length-1].focus():elements[0].focus()}_handleKeydown(event){"Tab"===event.key&&(this._lastTabNavDirection=event.shiftKey?"backward":"forward")}}var _default=FocusTrap;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=focustrap.min.js.map