{"version": 3, "file": "focustrap.min.js", "sources": ["../../../src/bootstrap/util/focustrap.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\n\n/**\n * Constants\n */\n\nconst NAME = 'focustrap'\nconst DATA_KEY = 'bs.focustrap'\nconst EVENT_KEY = `.${DATA_KEY}`\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY}`\n\nconst TAB_KEY = 'Tab'\nconst TAB_NAV_FORWARD = 'forward'\nconst TAB_NAV_BACKWARD = 'backward'\n\nconst Default = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n}\n\nconst DefaultType = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n}\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n    this._isActive = false\n    this._lastTabNavDirection = null\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return\n    }\n\n    if (this._config.autofocus) {\n      this._config.trapElement.focus()\n    }\n\n    EventHandler.off(document, EVENT_KEY) // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN, event => this._handleFocusin(event))\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event))\n\n    this._isActive = true\n  }\n\n  deactivate() {\n    if (!this._isActive) {\n      return\n    }\n\n    this._isActive = false\n    EventHandler.off(document, EVENT_KEY)\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const { trapElement } = this._config\n\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return\n    }\n\n    const elements = SelectorEngine.focusableChildren(trapElement)\n\n    if (elements.length === 0) {\n      trapElement.focus()\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus()\n    } else {\n      elements[0].focus()\n    }\n  }\n\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return\n    }\n\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD\n  }\n}\n\nexport default FocusTrap\n"], "names": ["EVENT_KEY", "EVENT_FOCUSIN", "EVENT_KEYDOWN_TAB", "<PERSON><PERSON><PERSON>", "autofocus", "trapElement", "DefaultType", "FocusTrap", "Config", "constructor", "config", "_config", "this", "_getConfig", "_isActive", "_lastTabNavDirection", "NAME", "activate", "focus", "off", "document", "on", "event", "_handleFocusin", "_handleKeydown", "deactivate", "target", "contains", "elements", "SelectorEngine", "focusableC<PERSON><PERSON>n", "length", "key", "shift<PERSON>ey"], "mappings": "2eAiBMA,qBADW,gBAEXC,+BAA0BD,WAC1BE,uCAAkCF,WAMlCG,QAAU,CACdC,WAAW,EACXC,YAAa,MAGTC,YAAc,CAClBF,UAAW,UACXC,YAAa,iBAOTE,kBAAkBC,gBACtBC,YAAYC,qBAELC,QAAUC,KAAKC,WAAWH,aAC1BI,WAAY,OACZC,qBAAuB,KAInBZ,4BACFA,QAGEG,gCACFA,YAGEU,wBAzCA,YA8CXC,WACML,KAAKE,YAILF,KAAKD,QAAQP,gBACVO,QAAQN,YAAYa,8BAGdC,IAAIC,SAAUpB,iCACdqB,GAAGD,SAAUnB,eAAeqB,OAASV,KAAKW,eAAeD,+BACzDD,GAAGD,SAAUlB,mBAAmBoB,OAASV,KAAKY,eAAeF,cAErER,WAAY,GAGnBW,aACOb,KAAKE,iBAILA,WAAY,wBACJK,IAAIC,SAAUpB,YAI7BuB,eAAeD,aACPjB,YAAEA,aAAgBO,KAAKD,WAEzBW,MAAMI,SAAWN,UAAYE,MAAMI,SAAWrB,aAAeA,YAAYsB,SAASL,MAAMI,qBAItFE,SAAWC,wBAAeC,kBAAkBzB,aAE1B,IAApBuB,SAASG,OACX1B,YAAYa,QA1EO,aA2EVN,KAAKG,qBACda,SAASA,SAASG,OAAS,GAAGb,QAE9BU,SAAS,GAAGV,QAIhBM,eAAeF,OApFD,QAqFRA,MAAMU,WAILjB,qBAAuBO,MAAMW,SAvFb,WADD,yBA4FT1B"}