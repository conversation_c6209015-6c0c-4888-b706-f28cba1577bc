define("theme_boost/bootstrap/util/index",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.triggerTransitionEnd=_exports.toType=_exports.reflow=_exports.parseSelector=_exports.onDOMContentLoaded=_exports.noop=_exports.isVisible=_exports.isRTL=_exports.isElement=_exports.isDisabled=_exports.getjQuery=_exports.getUID=_exports.getTransitionDurationFromElement=_exports.getNextActiveElement=_exports.getElement=_exports.findShadowRoot=_exports.executeAfterTransition=_exports.execute=_exports.defineJQueryPlugin=void 0;const parseSelector=selector=>(selector&&window.CSS&&window.CSS.escape&&(selector=selector.replace(/#([^\s"#']+)/g,((match,id)=>"#".concat(CSS.escape(id))))),selector);_exports.parseSelector=parseSelector;_exports.toType=object=>null==object?"".concat(object):Object.prototype.toString.call(object).match(/\s([a-z]+)/i)[1].toLowerCase();_exports.getUID=prefix=>{do{prefix+=Math.floor(1e6*Math.random())}while(document.getElementById(prefix));return prefix};const getTransitionDurationFromElement=element=>{if(!element)return 0;let{transitionDuration:transitionDuration,transitionDelay:transitionDelay}=window.getComputedStyle(element);const floatTransitionDuration=Number.parseFloat(transitionDuration),floatTransitionDelay=Number.parseFloat(transitionDelay);return floatTransitionDuration||floatTransitionDelay?(transitionDuration=transitionDuration.split(",")[0],transitionDelay=transitionDelay.split(",")[0],1e3*(Number.parseFloat(transitionDuration)+Number.parseFloat(transitionDelay))):0};_exports.getTransitionDurationFromElement=getTransitionDurationFromElement;const triggerTransitionEnd=element=>{element.dispatchEvent(new Event("transitionend"))};_exports.triggerTransitionEnd=triggerTransitionEnd;const isElement=object=>!(!object||"object"!=typeof object)&&(void 0!==object.jquery&&(object=object[0]),void 0!==object.nodeType);_exports.isElement=isElement;_exports.getElement=object=>isElement(object)?object.jquery?object[0]:object:"string"==typeof object&&object.length>0?document.querySelector(parseSelector(object)):null;_exports.isVisible=element=>{if(!isElement(element)||0===element.getClientRects().length)return!1;const elementIsVisible="visible"===getComputedStyle(element).getPropertyValue("visibility"),closedDetails=element.closest("details:not([open])");if(!closedDetails)return elementIsVisible;if(closedDetails!==element){const summary=element.closest("summary");if(summary&&summary.parentNode!==closedDetails)return!1;if(null===summary)return!1}return elementIsVisible};_exports.isDisabled=element=>!element||element.nodeType!==Node.ELEMENT_NODE||(!!element.classList.contains("disabled")||(void 0!==element.disabled?element.disabled:element.hasAttribute("disabled")&&"false"!==element.getAttribute("disabled")));const findShadowRoot=element=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof element.getRootNode){const root=element.getRootNode();return root instanceof ShadowRoot?root:null}return element instanceof ShadowRoot?element:element.parentNode?findShadowRoot(element.parentNode):null};_exports.findShadowRoot=findShadowRoot;_exports.noop=()=>{};_exports.reflow=element=>{element.offsetHeight};const getjQuery=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null;_exports.getjQuery=getjQuery;const DOMContentLoadedCallbacks=[],onDOMContentLoaded=callback=>{"loading"===document.readyState?(DOMContentLoadedCallbacks.length||document.addEventListener("DOMContentLoaded",(()=>{for(const callback of DOMContentLoadedCallbacks)callback()})),DOMContentLoadedCallbacks.push(callback)):callback()};_exports.onDOMContentLoaded=onDOMContentLoaded;_exports.isRTL=()=>"rtl"===document.documentElement.dir;_exports.defineJQueryPlugin=plugin=>{onDOMContentLoaded((()=>{const $=getjQuery();if($){const name=plugin.NAME,JQUERY_NO_CONFLICT=$.fn[name];$.fn[name]=plugin.jQueryInterface,$.fn[name].Constructor=plugin,$.fn[name].noConflict=()=>($.fn[name]=JQUERY_NO_CONFLICT,plugin.jQueryInterface)}}))};const execute=function(possibleCallback){let args=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],defaultValue=arguments.length>2&&void 0!==arguments[2]?arguments[2]:possibleCallback;return"function"==typeof possibleCallback?possibleCallback(...args):defaultValue};_exports.execute=execute;_exports.executeAfterTransition=function(callback,transitionElement){let waitForTransition=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!waitForTransition)return void execute(callback);const durationPadding=5,emulatedDuration=getTransitionDurationFromElement(transitionElement)+durationPadding;let called=!1;const handler=_ref=>{let{target:target}=_ref;target===transitionElement&&(called=!0,transitionElement.removeEventListener("transitionend",handler),execute(callback))};transitionElement.addEventListener("transitionend",handler),setTimeout((()=>{called||triggerTransitionEnd(transitionElement)}),emulatedDuration)};_exports.getNextActiveElement=(list,activeElement,shouldGetNext,isCycleAllowed)=>{const listLength=list.length;let index=list.indexOf(activeElement);return-1===index?!shouldGetNext&&isCycleAllowed?list[listLength-1]:list[0]:(index+=shouldGetNext?1:-1,isCycleAllowed&&(index=(index+listLength)%listLength),list[Math.max(0,Math.min(index,listLength-1))])}}));

//# sourceMappingURL=index.min.js.map