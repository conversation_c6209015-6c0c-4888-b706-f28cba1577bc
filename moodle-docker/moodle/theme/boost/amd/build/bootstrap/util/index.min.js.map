{"version": 3, "file": "index.min.js", "sources": ["../../../src/bootstrap/util/index.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1_000_000\nconst MILLISECONDS_MULTIPLIER = 1000\nconst TRANSITION_END = 'transitionend'\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`)\n  }\n\n  return selector\n}\n\n// Shout-out Angus Croll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`\n  }\n\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase()\n}\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID)\n  } while (document.getElementById(prefix))\n\n  return prefix\n}\n\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0\n  }\n\n  // Get transition-duration of the element\n  let { transitionDuration, transitionDelay } = window.getComputedStyle(element)\n\n  const floatTransitionDuration = Number.parseFloat(transitionDuration)\n  const floatTransitionDelay = Number.parseFloat(transitionDelay)\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0]\n  transitionDelay = transitionDelay.split(',')[0]\n\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER\n}\n\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END))\n}\n\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false\n  }\n\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0]\n  }\n\n  return typeof object.nodeType !== 'undefined'\n}\n\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object\n  }\n\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object))\n  }\n\n  return null\n}\n\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false\n  }\n\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible'\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])')\n\n  if (!closedDetails) {\n    return elementIsVisible\n  }\n\n  if (closedDetails !== element) {\n    const summary = element.closest('summary')\n    if (summary && summary.parentNode !== closedDetails) {\n      return false\n    }\n\n    if (summary === null) {\n      return false\n    }\n  }\n\n  return elementIsVisible\n}\n\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true\n  }\n\n  if (element.classList.contains('disabled')) {\n    return true\n  }\n\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled\n  }\n\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false'\n}\n\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode()\n    return root instanceof ShadowRoot ? root : null\n  }\n\n  if (element instanceof ShadowRoot) {\n    return element\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null\n  }\n\n  return findShadowRoot(element.parentNode)\n}\n\nconst noop = () => {}\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight // eslint-disable-line no-unused-expressions\n}\n\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery\n  }\n\n  return null\n}\n\nconst DOMContentLoadedCallbacks = []\n\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback()\n        }\n      })\n    }\n\n    DOMContentLoadedCallbacks.push(callback)\n  } else {\n    callback()\n  }\n}\n\nconst isRTL = () => document.documentElement.dir === 'rtl'\n\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery()\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME\n      const JQUERY_NO_CONFLICT = $.fn[name]\n      $.fn[name] = plugin.jQueryInterface\n      $.fn[name].Constructor = plugin\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT\n        return plugin.jQueryInterface\n      }\n    }\n  })\n}\n\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback(...args) : defaultValue\n}\n\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback)\n    return\n  }\n\n  const durationPadding = 5\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding\n\n  let called = false\n\n  const handler = ({ target }) => {\n    if (target !== transitionElement) {\n      return\n    }\n\n    called = true\n    transitionElement.removeEventListener(TRANSITION_END, handler)\n    execute(callback)\n  }\n\n  transitionElement.addEventListener(TRANSITION_END, handler)\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement)\n    }\n  }, emulatedDuration)\n}\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length\n  let index = list.indexOf(activeElement)\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0]\n  }\n\n  index += shouldGetNext ? 1 : -1\n\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength\n  }\n\n  return list[Math.max(0, Math.min(index, listLength - 1))]\n}\n\nexport {\n  defineJQueryPlugin,\n  execute,\n  executeAfterTransition,\n  findShadowRoot,\n  getElement,\n  getjQuery,\n  getNextActiveElement,\n  getTransitionDurationFromElement,\n  getUID,\n  isDisabled,\n  isElement,\n  isRTL,\n  isVisible,\n  noop,\n  onDOMContentLoaded,\n  parseSelector,\n  reflow,\n  triggerTransitionEnd,\n  toType\n}\n"], "names": ["parseSelector", "selector", "window", "CSS", "escape", "replace", "match", "id", "object", "Object", "prototype", "toString", "call", "toLowerCase", "prefix", "Math", "floor", "random", "document", "getElementById", "getTransitionDurationFromElement", "element", "transitionDuration", "transitionDelay", "getComputedStyle", "floatTransitionDuration", "Number", "parseFloat", "floatTransitionDelay", "split", "triggerTransitionEnd", "dispatchEvent", "Event", "isElement", "j<PERSON>y", "nodeType", "length", "querySelector", "getClientRects", "elementIsVisible", "getPropertyValue", "closedDetails", "closest", "summary", "parentNode", "Node", "ELEMENT_NODE", "classList", "contains", "disabled", "hasAttribute", "getAttribute", "findShadowRoot", "documentElement", "attachShadow", "getRootNode", "root", "ShadowRoot", "offsetHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "j<PERSON><PERSON><PERSON>", "body", "DOMContentLoadedCallbacks", "onDOMContentLoaded", "callback", "readyState", "addEventListener", "push", "dir", "plugin", "$", "name", "NAME", "JQUERY_NO_CONFLICT", "fn", "jQueryInterface", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "execute", "<PERSON><PERSON><PERSON><PERSON>", "args", "defaultValue", "transitionElement", "waitForTransition", "durationPadding", "emulatedDuration", "called", "handler", "_ref", "target", "removeEventListener", "setTimeout", "list", "activeElement", "shouldGetNext", "isCycleAllowed", "listLength", "index", "indexOf", "max", "min"], "mappings": "2jBAgBMA,cAAgBC,WAChBA,UAAYC,OAAOC,KAAOD,OAAOC,IAAIC,SAEvCH,SAAWA,SAASI,QAAQ,iBAAiB,CAACC,MAAOC,gBAAWJ,IAAIC,OAAOG,QAGtEN,+DAIMO,QACTA,MAAAA,iBACQA,QAGLC,OAAOC,UAAUC,SAASC,KAAKJ,QAAQF,MAAM,eAAe,GAAGO,8BAOzDC,YAEXA,QAAUC,KAAKC,MAjCH,IAiCSD,KAAKE,gBACnBC,SAASC,eAAeL,gBAE1BA,cAGHM,iCAAmCC,cAClCA,eACI,MAILC,mBAAEA,mBAAFC,gBAAsBA,iBAAoBrB,OAAOsB,iBAAiBH,eAEhEI,wBAA0BC,OAAOC,WAAWL,oBAC5CM,qBAAuBF,OAAOC,WAAWJ,wBAG1CE,yBAA4BG,sBAKjCN,mBAAqBA,mBAAmBO,MAAM,KAAK,GACnDN,gBAAkBA,gBAAgBM,MAAM,KAAK,GAxDf,KA0DtBH,OAAOC,WAAWL,oBAAsBI,OAAOC,WAAWJ,mBAPzD,oFAULO,qBAAuBT,UAC3BA,QAAQU,cAAc,IAAIC,MA7DL,4EAgEjBC,UAAYzB,WACXA,QAA4B,iBAAXA,eAIO,IAAlBA,OAAO0B,SAChB1B,OAASA,OAAO,SAGgB,IAApBA,OAAO2B,2DAGJ3B,QAEbyB,UAAUzB,QACLA,OAAO0B,OAAS1B,OAAO,GAAKA,OAGf,iBAAXA,QAAuBA,OAAO4B,OAAS,EACzClB,SAASmB,cAAcrC,cAAcQ,SAGvC,wBAGSa,cACXY,UAAUZ,UAAgD,IAApCA,QAAQiB,iBAAiBF,cAC3C,QAGHG,iBAAgF,YAA7Df,iBAAiBH,SAASmB,iBAAiB,cAE9DC,cAAgBpB,QAAQqB,QAAQ,2BAEjCD,qBACIF,oBAGLE,gBAAkBpB,QAAS,OACvBsB,QAAUtB,QAAQqB,QAAQ,cAC5BC,SAAWA,QAAQC,aAAeH,qBAC7B,KAGO,OAAZE,eACK,SAIJJ,sCAGUlB,UACZA,SAAWA,QAAQc,WAAaU,KAAKC,iBAItCzB,QAAQ0B,UAAUC,SAAS,mBAIC,IAArB3B,QAAQ4B,SACV5B,QAAQ4B,SAGV5B,QAAQ6B,aAAa,aAAoD,UAArC7B,QAAQ8B,aAAa,oBAG5DC,eAAiB/B,cAChBH,SAASmC,gBAAgBC,oBACrB,QAI0B,mBAAxBjC,QAAQkC,YAA4B,OACvCC,KAAOnC,QAAQkC,qBACdC,gBAAgBC,WAAaD,KAAO,YAGzCnC,mBAAmBoC,WACdpC,QAIJA,QAAQuB,WAINQ,eAAe/B,QAAQuB,YAHrB,2DAME,uBAUEvB,UACbA,QAAQqC,oBAGJC,UAAY,IACZzD,OAAO0D,SAAW1C,SAAS2C,KAAKX,aAAa,qBACxChD,OAAO0D,OAGT,wCAGHE,0BAA4B,GAE5BC,mBAAqBC,WACG,YAAxB9C,SAAS+C,YAENH,0BAA0B1B,QAC7BlB,SAASgD,iBAAiB,oBAAoB,SACvC,MAAMF,YAAYF,0BACrBE,cAKNF,0BAA0BK,KAAKH,WAE/BA,0EAIU,IAAuC,QAAjC9C,SAASmC,gBAAgBe,gCAElBC,SACzBN,oBAAmB,WACXO,EAAIX,eAENW,EAAG,OACCC,KAAOF,OAAOG,KACdC,mBAAqBH,EAAEI,GAAGH,MAChCD,EAAEI,GAAGH,MAAQF,OAAOM,gBACpBL,EAAEI,GAAGH,MAAMK,YAAcP,OACzBC,EAAEI,GAAGH,MAAMM,WAAa,KACtBP,EAAEI,GAAGH,MAAQE,mBACNJ,OAAOM,4BAMhBG,QAAU,SAACC,sBAAkBC,4DAAO,GAAIC,oEAAeF,uBACxB,mBAArBA,iBAAkCA,oBAAoBC,MAAQC,uEAG/C,SAACjB,SAAUkB,uBAAmBC,iFACtDA,8BACHL,QAAQd,gBAIJoB,gBAAkB,EAClBC,iBAAmBjE,iCAAiC8D,mBAAqBE,oBAE3EE,QAAS,QAEPC,QAAUC,WAACC,OAAEA,aACbA,SAAWP,oBAIfI,QAAS,EACTJ,kBAAkBQ,oBA5OC,gBA4OmCH,SACtDT,QAAQd,YAGVkB,kBAAkBhB,iBAhPG,gBAgP8BqB,SACnDI,YAAW,KACJL,QACHxD,qBAAqBoD,qBAEtBG,iDAYwB,CAACO,KAAMC,cAAeC,cAAeC,wBAC1DC,WAAaJ,KAAKxD,WACpB6D,MAAQL,KAAKM,QAAQL,sBAIV,IAAXI,OACMH,eAAiBC,eAAiBH,KAAKI,WAAa,GAAKJ,KAAK,IAGxEK,OAASH,cAAgB,GAAK,EAE1BC,iBACFE,OAASA,MAAQD,YAAcA,YAG1BJ,KAAK7E,KAAKoF,IAAI,EAAGpF,KAAKqF,IAAIH,MAAOD,WAAa"}