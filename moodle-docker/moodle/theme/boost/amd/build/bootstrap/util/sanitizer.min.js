define("theme_boost/bootstrap/util/sanitizer",["exports"],(function(_exports){Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.DefaultAllowlist=void 0,_exports.sanitizeHtml=function(unsafeHtml,allowList,sanitizeFunction){if(!unsafeHtml.length)return unsafeHtml;if(sanitizeFunction&&"function"==typeof sanitizeFunction)return sanitizeFunction(unsafeHtml);const createdDocument=(new window.DOMParser).parseFromString(unsafeHtml,"text/html"),elements=[].concat(...createdDocument.body.querySelectorAll("*"));for(const element of elements){const elementName=element.nodeName.toLowerCase();if(!Object.keys(allowList).includes(elementName)){element.remove();continue}const attributeList=[].concat(...element.attributes),allowedAttributes=[].concat(allowList["*"]||[],allowList[elementName]||[]);for(const attribute of attributeList)allowedAttribute(attribute,allowedAttributes)||element.removeAttribute(attribute.nodeName)}return createdDocument.body.innerHTML};const DefaultAllowlist={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]};_exports.DefaultAllowlist=DefaultAllowlist;const uriAttributes=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),SAFE_URL_PATTERN=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,allowedAttribute=(attribute,allowedAttributeList)=>{const attributeName=attribute.nodeName.toLowerCase();return allowedAttributeList.includes(attributeName)?!uriAttributes.has(attributeName)||Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue)):allowedAttributeList.filter((attributeRegex=>attributeRegex instanceof RegExp)).some((regex=>regex.test(attributeName)))}}));

//# sourceMappingURL=sanitizer.min.js.map