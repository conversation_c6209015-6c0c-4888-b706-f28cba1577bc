{"version": 3, "file": "sanitizer.min.js", "sources": ["../../../src/bootstrap/util/sanitizer.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i\n\nexport const DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  dd: [],\n  div: [],\n  dl: [],\n  dt: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n}\n// js-docs-end allow-list\n\nconst uriAttributes = new Set([\n  'background',\n  'cite',\n  'href',\n  'itemtype',\n  'longdesc',\n  'poster',\n  'src',\n  'xlink:href'\n])\n\n/**\n * A pattern that recognizes URLs that are safe wrt. XSS in URL navigation\n * contexts.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/15.2.8/packages/core/src/sanitization/url_sanitizer.ts#L38\n */\n// eslint-disable-next-line unicorn/better-regex\nconst SAFE_URL_PATTERN = /^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i\n\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase()\n\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue))\n    }\n\n    return true\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp)\n    .some(regex => regex.test(attributeName))\n}\n\nexport function sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml\n  }\n\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml)\n  }\n\n  const domParser = new window.DOMParser()\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html')\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'))\n\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase()\n\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove()\n      continue\n    }\n\n    const attributeList = [].concat(...element.attributes)\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || [])\n\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName)\n      }\n    }\n  }\n\n  return createdDocument.body.innerHTML\n}\n"], "names": ["unsafeHtml", "allowList", "sanitizeFunction", "length", "createdDocument", "window", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseFromString", "elements", "concat", "body", "querySelectorAll", "element", "elementName", "nodeName", "toLowerCase", "Object", "keys", "includes", "remove", "attributeList", "attributes", "allowedAttributes", "attribute", "allowedAttribute", "removeAttribute", "innerHTML", "DefaultAllowlist", "a", "area", "b", "br", "col", "code", "dd", "div", "dl", "dt", "em", "hr", "h1", "h2", "h3", "h4", "h5", "h6", "i", "img", "li", "ol", "p", "pre", "s", "small", "span", "sub", "sup", "strong", "u", "ul", "uriAttributes", "Set", "SAFE_URL_PATTERN", "allowedAttributeList", "attributeName", "has", "Boolean", "test", "nodeValue", "filter", "attributeRegex", "RegExp", "some", "regex"], "mappings": "sMAoF6BA,WAAYC,UAAWC,sBAC7CF,WAAWG,cACPH,cAGLE,kBAAgD,mBAArBA,wBACtBA,iBAAiBF,kBAIpBI,iBADY,IAAIC,OAAOC,WACKC,gBAAgBP,WAAY,aACxDQ,SAAW,GAAGC,UAAUL,gBAAgBM,KAAKC,iBAAiB,UAE/D,MAAMC,WAAWJ,SAAU,OACxBK,YAAcD,QAAQE,SAASC,kBAEhCC,OAAOC,KAAKhB,WAAWiB,SAASL,aAAc,CACjDD,QAAQO,wBAIJC,cAAgB,GAAGX,UAAUG,QAAQS,YACrCC,kBAAoB,GAAGb,OAAOR,UAAU,MAAQ,GAAIA,UAAUY,cAAgB,QAE/E,MAAMU,aAAaH,cACjBI,iBAAiBD,UAAWD,oBAC/BV,QAAQa,gBAAgBF,UAAUT,iBAKjCV,gBAAgBM,KAAKgB,iBAzGjBC,iBAAmB,KAEzB,CAAC,QAAS,MAAO,KAAM,OAAQ,OAJP,kBAK7BC,EAAG,CAAC,SAAU,OAAQ,QAAS,OAC/BC,KAAM,GACNC,EAAG,GACHC,GAAI,GACJC,IAAK,GACLC,KAAM,GACNC,GAAI,GACJC,IAAK,GACLC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,CAAC,MAAO,SAAU,MAAO,QAAS,QAAS,UAChDC,GAAI,GACJC,GAAI,GACJC,EAAG,GACHC,IAAK,GACLC,EAAG,GACHC,MAAO,GACPC,KAAM,GACNC,IAAK,GACLC,IAAK,GACLC,OAAQ,GACRC,EAAG,GACHC,GAAI,qDAIAC,cAAgB,IAAIC,IAAI,CAC5B,aACA,OACA,OACA,WACA,WACA,SACA,MACA,eAUIC,iBAAmB,0DAEnBtC,iBAAmB,CAACD,UAAWwC,8BAC7BC,cAAgBzC,UAAUT,SAASC,qBAErCgD,qBAAqB7C,SAAS8C,gBAC5BJ,cAAcK,IAAID,gBACbE,QAAQJ,iBAAiBK,KAAK5C,UAAU6C,YAO5CL,qBAAqBM,QAAOC,gBAAkBA,0BAA0BC,SAC5EC,MAAKC,OAASA,MAAMN,KAAKH"}