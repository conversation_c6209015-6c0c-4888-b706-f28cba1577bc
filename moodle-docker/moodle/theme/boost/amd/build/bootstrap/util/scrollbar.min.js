define("theme_boost/bootstrap/util/scrollbar",["exports","../dom/manipulator","../dom/selector-engine","./index"],(function(_exports,_manipulator,_selectorEngine,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_manipulator=_interopRequireDefault(_manipulator),_selectorEngine=_interopRequireDefault(_selectorEngine);var _default=class{constructor(){this._element=document.body}getWidth(){const documentWidth=document.documentElement.clientWidth;return Math.abs(window.innerWidth-documentWidth)}hide(){const width=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,"padding-right",(calculatedValue=>calculatedValue+width)),this._setElementAttributes(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top","padding-right",(calculatedValue=>calculatedValue+width)),this._setElementAttributes(".sticky-top","margin-right",(calculatedValue=>calculatedValue-width))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,"padding-right"),this._resetElementAttributes(".fixed-top, .fixed-bottom, .is-fixed, .sticky-top","padding-right"),this._resetElementAttributes(".sticky-top","margin-right")}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(selector,styleProperty,callback){const scrollbarWidth=this.getWidth();this._applyManipulationCallback(selector,(element=>{if(element!==this._element&&window.innerWidth>element.clientWidth+scrollbarWidth)return;this._saveInitialAttribute(element,styleProperty);const calculatedValue=window.getComputedStyle(element).getPropertyValue(styleProperty);element.style.setProperty(styleProperty,"".concat(callback(Number.parseFloat(calculatedValue)),"px"))}))}_saveInitialAttribute(element,styleProperty){const actualValue=element.style.getPropertyValue(styleProperty);actualValue&&_manipulator.default.setDataAttribute(element,styleProperty,actualValue)}_resetElementAttributes(selector,styleProperty){this._applyManipulationCallback(selector,(element=>{const value=_manipulator.default.getDataAttribute(element,styleProperty);null!==value?(_manipulator.default.removeDataAttribute(element,styleProperty),element.style.setProperty(styleProperty,value)):element.style.removeProperty(styleProperty)}))}_applyManipulationCallback(selector,callBack){if((0,_index.isElement)(selector))callBack(selector);else for(const sel of _selectorEngine.default.find(selector,this._element))callBack(sel)}};return _exports.default=_default,_exports.default}));

//# sourceMappingURL=scrollbar.min.js.map