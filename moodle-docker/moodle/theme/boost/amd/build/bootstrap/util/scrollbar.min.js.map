{"version": 3, "file": "scrollbar.min.js", "sources": ["../../../src/bootstrap/util/scrollbar.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport Manipulator from '../dom/manipulator'\nimport SelectorEngine from '../dom/selector-engine'\nimport { isElement } from './index'\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top'\nconst SELECTOR_STICKY_CONTENT = '.sticky-top'\nconst PROPERTY_PADDING = 'padding-right'\nconst PROPERTY_MARGIN = 'margin-right'\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth\n    return Math.abs(window.innerWidth - documentWidth)\n  }\n\n  hide() {\n    const width = this.getWidth()\n    this._disableOverFlow()\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width)\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width)\n  }\n\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow')\n    this._resetElementAttributes(this._element, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING)\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN)\n  }\n\n  isOverflowing() {\n    return this.getWidth() > 0\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow')\n    this._element.style.overflow = 'hidden'\n  }\n\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth()\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return\n      }\n\n      this._saveInitialAttribute(element, styleProperty)\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty)\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty)\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue)\n    }\n  }\n\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty)\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty)\n        return\n      }\n\n      Manipulator.removeDataAttribute(element, styleProperty)\n      element.style.setProperty(styleProperty, value)\n    }\n\n    this._applyManipulationCallback(selector, manipulationCallBack)\n  }\n\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector)\n      return\n    }\n\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel)\n    }\n  }\n}\n\nexport default ScrollBarHelper\n"], "names": ["constructor", "_element", "document", "body", "getWidth", "documentWidth", "documentElement", "clientWidth", "Math", "abs", "window", "innerWidth", "hide", "width", "this", "_disableOver<PERSON>low", "_setElementAttributes", "calculatedValue", "reset", "_resetElementAttributes", "isOverflowing", "_saveInitialAttribute", "style", "overflow", "selector", "styleProperty", "callback", "scrollbarWidth", "_applyManipulationCallback", "element", "getComputedStyle", "getPropertyValue", "setProperty", "Number", "parseFloat", "actualValue", "setDataAttribute", "value", "Manipulator", "getDataAttribute", "removeDataAttribute", "removeProperty", "callBack", "sel", "SelectorEngine", "find"], "mappings": "ycAyBEA,mBACOC,SAAWC,SAASC,KAI3BC,iBAEQC,cAAgBH,SAASI,gBAAgBC,mBACxCC,KAAKC,IAAIC,OAAOC,WAAaN,eAGtCO,aACQC,MAAQC,KAAKV,gBACdW,wBAEAC,sBAAsBF,KAAKb,SAvBX,iBAuBuCgB,iBAAmBA,gBAAkBJ,aAE5FG,sBA3BsB,oDAEN,iBAyBgDC,iBAAmBA,gBAAkBJ,aACrGG,sBA3BuB,cAER,gBAyBiDC,iBAAmBA,gBAAkBJ,QAG5GK,aACOC,wBAAwBL,KAAKb,SAAU,iBACvCkB,wBAAwBL,KAAKb,SA/Bb,sBAgChBkB,wBAlCsB,oDAEN,sBAiChBA,wBAlCuB,cAER,gBAmCtBC,uBACSN,KAAKV,WAAa,EAI3BW,wBACOM,sBAAsBP,KAAKb,SAAU,iBACrCA,SAASqB,MAAMC,SAAW,SAGjCP,sBAAsBQ,SAAUC,cAAeC,gBACvCC,eAAiBb,KAAKV,gBAWvBwB,2BAA2BJ,UAVHK,aACvBA,UAAYf,KAAKb,UAAYS,OAAOC,WAAakB,QAAQtB,YAAcoB,2BAItEN,sBAAsBQ,QAASJ,qBAC9BR,gBAAkBP,OAAOoB,iBAAiBD,SAASE,iBAAiBN,eAC1EI,QAAQP,MAAMU,YAAYP,wBAAkBC,SAASO,OAAOC,WAAWjB,4BAM3EI,sBAAsBQ,QAASJ,qBACvBU,YAAcN,QAAQP,MAAMS,iBAAiBN,eAC/CU,kCACUC,iBAAiBP,QAASJ,cAAeU,aAIzDhB,wBAAwBK,SAAUC,oBAa3BG,2BAA2BJ,UAZHK,gBACrBQ,MAAQC,qBAAYC,iBAAiBV,QAASJ,eAEtC,OAAVY,4BAKQG,oBAAoBX,QAASJ,eACzCI,QAAQP,MAAMU,YAAYP,cAAeY,QALvCR,QAAQP,MAAMmB,eAAehB,kBAWnCG,2BAA2BJ,SAAUkB,cAC/B,oBAAUlB,UACZkB,SAASlB,mBAIN,MAAMmB,OAAOC,wBAAeC,KAAKrB,SAAUV,KAAKb,UACnDyC,SAASC"}