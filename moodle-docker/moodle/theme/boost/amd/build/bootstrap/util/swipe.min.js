define("theme_boost/bootstrap/util/swipe",["exports","../dom/event-handler","./config","./index"],(function(_exports,_eventHandler,_config,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_eventHandler=_interopRequireDefault(_eventHandler),_config=_interopRequireDefault(_config);const EVENT_TOUCHSTART="touchstart".concat(".bs.swipe"),EVENT_TOUCHMOVE="touchmove".concat(".bs.swipe"),EVENT_TOUCHEND="touchend".concat(".bs.swipe"),EVENT_POINTERDOWN="pointerdown".concat(".bs.swipe"),EVENT_POINTERUP="pointerup".concat(".bs.swipe"),Default={endCallback:null,leftCallback:null,rightCallback:null},DefaultType={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Swipe extends _config.default{constructor(element,config){super(),this._element=element,element&&Swipe.isSupported()&&(this._config=this._getConfig(config),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"swipe"}dispose(){_eventHandler.default.off(this._element,".bs.swipe")}_start(event){this._supportPointerEvents?this._eventIsPointerPenTouch(event)&&(this._deltaX=event.clientX):this._deltaX=event.touches[0].clientX}_end(event){this._eventIsPointerPenTouch(event)&&(this._deltaX=event.clientX-this._deltaX),this._handleSwipe(),(0,_index.execute)(this._config.endCallback)}_move(event){this._deltaX=event.touches&&event.touches.length>1?0:event.touches[0].clientX-this._deltaX}_handleSwipe(){const absDeltaX=Math.abs(this._deltaX);if(absDeltaX<=40)return;const direction=absDeltaX/this._deltaX;this._deltaX=0,direction&&(0,_index.execute)(direction>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(_eventHandler.default.on(this._element,EVENT_POINTERDOWN,(event=>this._start(event))),_eventHandler.default.on(this._element,EVENT_POINTERUP,(event=>this._end(event))),this._element.classList.add("pointer-event")):(_eventHandler.default.on(this._element,EVENT_TOUCHSTART,(event=>this._start(event))),_eventHandler.default.on(this._element,EVENT_TOUCHMOVE,(event=>this._move(event))),_eventHandler.default.on(this._element,EVENT_TOUCHEND,(event=>this._end(event))))}_eventIsPointerPenTouch(event){return this._supportPointerEvents&&("pen"===event.pointerType||"touch"===event.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}var _default=Swipe;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=swipe.min.js.map