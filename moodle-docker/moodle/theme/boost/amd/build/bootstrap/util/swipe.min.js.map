{"version": 3, "file": "swipe.min.js", "sources": ["../../../src/bootstrap/util/swipe.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport EventHandler from '../dom/event-handler'\nimport Config from './config'\nimport { execute } from './index'\n\n/**\n * Constants\n */\n\nconst NAME = 'swipe'\nconst EVENT_KEY = '.bs.swipe'\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY}`\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY}`\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY}`\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY}`\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY}`\nconst POINTER_TYPE_TOUCH = 'touch'\nconst POINTER_TYPE_PEN = 'pen'\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event'\nconst SWIPE_THRESHOLD = 40\n\nconst Default = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n}\n\nconst DefaultType = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n}\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super()\n    this._element = element\n\n    if (!element || !Swipe.isSupported()) {\n      return\n    }\n\n    this._config = this._getConfig(config)\n    this._deltaX = 0\n    this._supportPointerEvents = Boolean(window.PointerEvent)\n    this._initEvents()\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY)\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX\n\n      return\n    }\n\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX\n    }\n  }\n\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX\n    }\n\n    this._handleSwipe()\n    execute(this._config.endCallback)\n  }\n\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ?\n      0 :\n      event.touches[0].clientX - this._deltaX\n  }\n\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX)\n\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return\n    }\n\n    const direction = absDeltaX / this._deltaX\n\n    this._deltaX = 0\n\n    if (!direction) {\n      return\n    }\n\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback)\n  }\n\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event))\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event))\n\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT)\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event))\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event))\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event))\n    }\n  }\n\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH)\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0\n  }\n}\n\nexport default Swipe\n"], "names": ["EVENT_TOUCHSTART", "EVENT_TOUCHMOVE", "EVENT_TOUCHEND", "EVENT_POINTERDOWN", "EVENT_POINTERUP", "<PERSON><PERSON><PERSON>", "endCallback", "leftCallback", "<PERSON><PERSON><PERSON><PERSON>", "DefaultType", "Swipe", "Config", "constructor", "element", "config", "_element", "isSupported", "_config", "this", "_getConfig", "_deltaX", "_supportPointerEvents", "Boolean", "window", "PointerEvent", "_initEvents", "NAME", "dispose", "off", "_start", "event", "_eventIsPointerPenTouch", "clientX", "touches", "_end", "_handleSwipe", "_move", "length", "absDeltaX", "Math", "abs", "direction", "on", "classList", "add", "pointerType", "document", "documentElement", "navigator", "maxTouchPoints"], "mappings": "uZAiBMA,qCADY,aAEZC,mCAFY,aAGZC,iCAHY,aAIZC,uCAJY,aAKZC,mCALY,aAWZC,QAAU,CACdC,YAAa,KACbC,aAAc,KACdC,cAAe,MAGXC,YAAc,CAClBH,YAAa,kBACbC,aAAc,kBACdC,cAAe,yBAOXE,cAAcC,gBAClBC,YAAYC,QAASC,qBAEdC,SAAWF,QAEXA,SAAYH,MAAMM,qBAIlBC,QAAUC,KAAKC,WAAWL,aAC1BM,QAAU,OACVC,sBAAwBC,QAAQC,OAAOC,mBACvCC,eAIIpB,4BACFA,QAGEI,gCACFA,YAGEiB,wBApDA,QAyDXC,gCACeC,IAAIV,KAAKH,SAzDR,aA6DhBc,OAAOC,OACAZ,KAAKG,sBAMNH,KAAKa,wBAAwBD,cAC1BV,QAAUU,MAAME,cANhBZ,QAAUU,MAAMG,QAAQ,GAAGD,QAUpCE,KAAKJ,OACCZ,KAAKa,wBAAwBD,cAC1BV,QAAUU,MAAME,QAAUd,KAAKE,cAGjCe,kCACGjB,KAAKD,QAAQX,aAGvB8B,MAAMN,YACCV,QAAUU,MAAMG,SAAWH,MAAMG,QAAQI,OAAS,EACrD,EACAP,MAAMG,QAAQ,GAAGD,QAAUd,KAAKE,QAGpCe,qBACQG,UAAYC,KAAKC,IAAItB,KAAKE,YAE5BkB,WAlFgB,gBAsFdG,UAAYH,UAAYpB,KAAKE,aAE9BA,QAAU,EAEVqB,8BAIGA,UAAY,EAAIvB,KAAKD,QAAQT,cAAgBU,KAAKD,QAAQV,cAGpEkB,cACMP,KAAKG,6CACMqB,GAAGxB,KAAKH,SAAUZ,mBAAmB2B,OAASZ,KAAKW,OAAOC,+BAC1DY,GAAGxB,KAAKH,SAAUX,iBAAiB0B,OAASZ,KAAKgB,KAAKJ,cAE9Df,SAAS4B,UAAUC,IAvGG,yCAyGdF,GAAGxB,KAAKH,SAAUf,kBAAkB8B,OAASZ,KAAKW,OAAOC,+BACzDY,GAAGxB,KAAKH,SAAUd,iBAAiB6B,OAASZ,KAAKkB,MAAMN,+BACvDY,GAAGxB,KAAKH,SAAUb,gBAAgB4B,OAASZ,KAAKgB,KAAKJ,UAItEC,wBAAwBD,cACfZ,KAAKG,wBAjHS,QAiHiBS,MAAMe,aAlHrB,UAkHyDf,MAAMe,wCAK/E,iBAAkBC,SAASC,iBAAmBC,UAAUC,eAAiB,gBAIrEvC"}