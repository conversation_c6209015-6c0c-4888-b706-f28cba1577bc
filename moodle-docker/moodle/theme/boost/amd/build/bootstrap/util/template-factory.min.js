define("theme_boost/bootstrap/util/template-factory",["exports","../dom/selector-engine","./config","./sanitizer","./index"],(function(_exports,_selectorEngine,_config,_sanitizer,_index){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_selectorEngine=_interopRequireDefault(_selectorEngine),_config=_interopRequireDefault(_config);const Default={allowList:_sanitizer.DefaultAllowlist,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},DefaultType={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},DefaultContentType={entry:"(string|element|function|null)",selector:"(string|element)"};class TemplateFactory extends _config.default{constructor(config){super(),this._config=this._getConfig(config)}static get Default(){return Default}static get DefaultType(){return DefaultType}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((config=>this._resolvePossibleFunction(config))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(content){return this._checkContent(content),this._config.content={...this._config.content,...content},this}toHtml(){const templateWrapper=document.createElement("div");templateWrapper.innerHTML=this._maybeSanitize(this._config.template);for(const[selector,text]of Object.entries(this._config.content))this._setContent(templateWrapper,text,selector);const template=templateWrapper.children[0],extraClass=this._resolvePossibleFunction(this._config.extraClass);return extraClass&&template.classList.add(...extraClass.split(" ")),template}_typeCheckConfig(config){super._typeCheckConfig(config),this._checkContent(config.content)}_checkContent(arg){for(const[selector,content]of Object.entries(arg))super._typeCheckConfig({selector:selector,entry:content},DefaultContentType)}_setContent(template,content,selector){const templateElement=_selectorEngine.default.findOne(selector,template);templateElement&&((content=this._resolvePossibleFunction(content))?(0,_index.isElement)(content)?this._putElementInTemplate((0,_index.getElement)(content),templateElement):this._config.html?templateElement.innerHTML=this._maybeSanitize(content):templateElement.textContent=content:templateElement.remove())}_maybeSanitize(arg){return this._config.sanitize?(0,_sanitizer.sanitizeHtml)(arg,this._config.allowList,this._config.sanitizeFn):arg}_resolvePossibleFunction(arg){return(0,_index.execute)(arg,[this])}_putElementInTemplate(element,templateElement){if(this._config.html)return templateElement.innerHTML="",void templateElement.append(element);templateElement.textContent=element.textContent}}var _default=TemplateFactory;return _exports.default=_default,_exports.default}));

//# sourceMappingURL=template-factory.min.js.map