{"version": 3, "file": "template-factory.min.js", "sources": ["../../../src/bootstrap/util/template-factory.js"], "sourcesContent": ["/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nimport SelectorEngine from '../dom/selector-engine'\nimport Config from './config'\nimport { DefaultAllowlist, sanitizeHtml } from './sanitizer'\nimport { execute, getElement, isElement } from './index'\n\n/**\n * Constants\n */\n\nconst NAME = 'TemplateFactory'\n\nconst Default = {\n  allowList: DefaultAllowlist,\n  content: {}, // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n}\n\nconst DefaultType = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n}\n\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n}\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super()\n    this._config = this._getConfig(config)\n  }\n\n  // Getters\n  static get Default() {\n    return Default\n  }\n\n  static get DefaultType() {\n    return DefaultType\n  }\n\n  static get NAME() {\n    return NAME\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content)\n      .map(config => this._resolvePossibleFunction(config))\n      .filter(Boolean)\n  }\n\n  hasContent() {\n    return this.getContent().length > 0\n  }\n\n  changeContent(content) {\n    this._checkContent(content)\n    this._config.content = { ...this._config.content, ...content }\n    return this\n  }\n\n  toHtml() {\n    const templateWrapper = document.createElement('div')\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template)\n\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector)\n    }\n\n    const template = templateWrapper.children[0]\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass)\n\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '))\n    }\n\n    return template\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config)\n    this._checkContent(config.content)\n  }\n\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({ selector, entry: content }, DefaultContentType)\n    }\n  }\n\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template)\n\n    if (!templateElement) {\n      return\n    }\n\n    content = this._resolvePossibleFunction(content)\n\n    if (!content) {\n      templateElement.remove()\n      return\n    }\n\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement)\n      return\n    }\n\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content)\n      return\n    }\n\n    templateElement.textContent = content\n  }\n\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg\n  }\n\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this])\n  }\n\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = ''\n      templateElement.append(element)\n      return\n    }\n\n    templateElement.textContent = element.textContent\n  }\n}\n\nexport default TemplateFactory\n"], "names": ["<PERSON><PERSON><PERSON>", "allowList", "DefaultAllowlist", "content", "extraClass", "html", "sanitize", "sanitizeFn", "template", "DefaultType", "DefaultContentType", "entry", "selector", "TemplateFactory", "Config", "constructor", "config", "_config", "this", "_getConfig", "NAME", "get<PERSON>ontent", "Object", "values", "map", "_resolvePossibleFunction", "filter", "Boolean", "<PERSON><PERSON><PERSON><PERSON>", "length", "changeContent", "_checkContent", "toHtml", "templateWrapper", "document", "createElement", "innerHTML", "_maybeSanitize", "text", "entries", "_setContent", "children", "classList", "add", "split", "_typeCheckConfig", "arg", "templateElement", "SelectorEngine", "findOne", "_putElementInTemplate", "textContent", "remove", "element", "append"], "mappings": "mcAkBMA,QAAU,CACdC,UAAWC,4BACXC,QAAS,GACTC,WAAY,GACZC,MAAM,EACNC,UAAU,EACVC,WAAY,KACZC,SAAU,eAGNC,YAAc,CAClBR,UAAW,SACXE,QAAS,SACTC,WAAY,oBACZC,KAAM,UACNC,SAAU,UACVC,WAAY,kBACZC,SAAU,UAGNE,mBAAqB,CACzBC,MAAO,iCACPC,SAAU,0BAONC,wBAAwBC,gBAC5BC,YAAYC,qBAELC,QAAUC,KAAKC,WAAWH,QAItBhB,4BACFA,QAGES,gCACFA,YAGEW,wBA9CA,kBAmDXC,oBACSC,OAAOC,OAAOL,KAAKD,QAAQd,SAC/BqB,KAAIR,QAAUE,KAAKO,yBAAyBT,UAC5CU,OAAOC,SAGZC,oBACSV,KAAKG,aAAaQ,OAAS,EAGpCC,cAAc3B,qBACP4B,cAAc5B,cACdc,QAAQd,QAAU,IAAKe,KAAKD,QAAQd,WAAYA,SAC9Ce,KAGTc,eACQC,gBAAkBC,SAASC,cAAc,OAC/CF,gBAAgBG,UAAYlB,KAAKmB,eAAenB,KAAKD,QAAQT,cAExD,MAAOI,SAAU0B,QAAShB,OAAOiB,QAAQrB,KAAKD,QAAQd,cACpDqC,YAAYP,gBAAiBK,KAAM1B,gBAGpCJ,SAAWyB,gBAAgBQ,SAAS,GACpCrC,WAAac,KAAKO,yBAAyBP,KAAKD,QAAQb,mBAE1DA,YACFI,SAASkC,UAAUC,OAAOvC,WAAWwC,MAAM,MAGtCpC,SAITqC,iBAAiB7B,cACT6B,iBAAiB7B,aAClBe,cAAcf,OAAOb,SAG5B4B,cAAce,SACP,MAAOlC,SAAUT,WAAYmB,OAAOiB,QAAQO,WACzCD,iBAAiB,CAAEjC,SAAAA,SAAUD,MAAOR,SAAWO,oBAIzD8B,YAAYhC,SAAUL,QAASS,gBACvBmC,gBAAkBC,wBAAeC,QAAQrC,SAAUJ,UAEpDuC,mBAIL5C,QAAUe,KAAKO,yBAAyBtB,WAOpC,oBAAUA,cACP+C,uBAAsB,qBAAW/C,SAAU4C,iBAI9C7B,KAAKD,QAAQZ,KACf0C,gBAAgBX,UAAYlB,KAAKmB,eAAelC,SAIlD4C,gBAAgBI,YAAchD,QAd5B4C,gBAAgBK,UAiBpBf,eAAeS,YACN5B,KAAKD,QAAQX,UAAW,2BAAawC,IAAK5B,KAAKD,QAAQhB,UAAWiB,KAAKD,QAAQV,YAAcuC,IAGtGrB,yBAAyBqB,YAChB,kBAAQA,IAAK,CAAC5B,OAGvBgC,sBAAsBG,QAASN,oBACzB7B,KAAKD,QAAQZ,YACf0C,gBAAgBX,UAAY,QAC5BW,gBAAgBO,OAAOD,SAIzBN,gBAAgBI,YAAcE,QAAQF,0BAI3BtC"}