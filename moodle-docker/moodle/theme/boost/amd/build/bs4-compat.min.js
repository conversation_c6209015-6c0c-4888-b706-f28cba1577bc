define("theme_boost/bs4-compat",["exports","./bootstrap/util/sanitizer","theme_boost/bootstrap/popover","theme_boost/bootstrap/tooltip","core/log"],(function(_exports,_sanitizer,_popover,_tooltip,_log){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Backward compatibility for Bootstrap 5.
   *
   * This module silently adapts the current page to Bootstrap 5.
   * When the Boostrap 4 backward compatibility period ends in MDL-84465,
   * this module will be removed.
   *
   * @module     theme_boost/bs4-compat
   * @copyright  2025 Mike<PERSON> <<EMAIL>>
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   * @deprecated since Moodle 5.0
   * @todo       Final deprecation in Moodle 6.0. See MDL-84465.
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_popover=_interopRequireDefault(_popover),_tooltip=_interopRequireDefault(_tooltip),_log=_interopRequireDefault(_log);const bootstrapElements=[{selector:".alert button.close",replacements:[{bs4:"data-dismiss",bs5:"data-bs-dismiss"}]},{selector:'[data-toggle="modal"]',replacements:[{bs4:"data-toggle",bs5:"data-bs-toggle"},{bs4:"data-target",bs5:"data-bs-target"}]},{selector:".modal .modal-header button.close",replacements:[{bs4:"data-dismiss",bs5:"data-bs-dismiss"}]},{selector:'[data-toggle="dropdown"]',replacements:[{bs4:"data-toggle",bs5:"data-bs-toggle"}]},{selector:'[data-toggle="collapse"]',replacements:[{bs4:"data-toggle",bs5:"data-bs-toggle"},{bs4:"data-target",bs5:"data-bs-target"},{bs4:"data-parent",bs5:"data-bs-parent"}]},{selector:".carousel [data-slide]",replacements:[{bs4:"data-slide",bs5:"data-bs-slide"},{bs4:"data-target",bs5:"data-bs-target"}]},{selector:'[data-toggle="tooltip"]',replacements:[{bs4:"data-toggle",bs5:"data-bs-toggle"},{bs4:"data-placement",bs5:"data-bs-placement"},{bs4:"data-animation",bs5:"data-bs-animation"},{bs4:"data-delay",bs5:"data-bs-delay"},{bs4:"data-title",bs5:"data-bs-title"},{bs4:"data-html",bs5:"data-bs-html"},{bs4:"data-trigger",bs5:"data-bs-trigger"},{bs4:"data-selector",bs5:"data-bs-selector"},{bs4:"data-container",bs5:"data-bs-container"}]},{selector:'[data-toggle="popover"]',replacements:[{bs4:"data-toggle",bs5:"data-bs-toggle"},{bs4:"data-content",bs5:"data-bs-content"},{bs4:"data-placement",bs5:"data-bs-placement"},{bs4:"data-animation",bs5:"data-bs-animation"},{bs4:"data-delay",bs5:"data-bs-delay"},{bs4:"data-title",bs5:"data-bs-title"},{bs4:"data-html",bs5:"data-bs-html"},{bs4:"data-trigger",bs5:"data-bs-trigger"},{bs4:"data-selector",bs5:"data-bs-selector"},{bs4:"data-container",bs5:"data-bs-container"}]},{selector:'[data-toggle="tab"]',replacements:[{bs4:"data-toggle",bs5:"data-bs-toggle"},{bs4:"data-target",bs5:"data-bs-target"}]}];_exports.init=element=>{element||(element=document),(container=>{for(const bootstrapElement of bootstrapElements){const elements=container.querySelectorAll(bootstrapElement.selector);for(const element of elements)for(const replacement of bootstrapElement.replacements)element.hasAttribute(replacement.bs4)&&(element.setAttribute(replacement.bs5,element.getAttribute(replacement.bs4)),element.removeAttribute(replacement.bs4),_log.default.debug("Silent Bootstrap 4 to 5 compatibility: ".concat(replacement.bs4," replaced by ").concat(replacement.bs5)),_log.default.debug(element))}})(element),(container=>{const popoverConfig={container:"body",trigger:"focus",allowList:Object.assign(_sanitizer.DefaultAllowlist,{table:[],thead:[],tbody:[],tr:[],th:[],td:[]})};container.querySelectorAll('[data-bs-toggle="popover"]').forEach((tooltipTriggerEl=>{_popover.default.getInstance(tooltipTriggerEl)||new _popover.default(tooltipTriggerEl,popoverConfig)})),container.querySelectorAll('[data-bs-toggle="tooltip"]').forEach((tooltipTriggerEl=>{_tooltip.default.getInstance(tooltipTriggerEl)||new _tooltip.default(tooltipTriggerEl)}))})(element)}}));

//# sourceMappingURL=bs4-compat.min.js.map