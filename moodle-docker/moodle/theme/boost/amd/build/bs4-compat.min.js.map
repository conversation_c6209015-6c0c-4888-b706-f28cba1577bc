{"version": 3, "file": "bs4-compat.min.js", "sources": ["../src/bs4-compat.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON>le is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n\n/**\n * Backward compatibility for Bootstrap 5.\n *\n * This module silently adapts the current page to Bootstrap 5.\n * When the Boostrap 4 backward compatibility period ends in MDL-84465,\n * this module will be removed.\n *\n * @module     theme_boost/bs4-compat\n * @copyright  2025 Mikel Martín <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @deprecated since Moodle 5.0\n * @todo       Final deprecation in Moodle 6.0. See MDL-84465.\n */\n\nimport {DefaultAllowlist} from './bootstrap/util/sanitizer';\nimport Popover from 'theme_boost/bootstrap/popover';\nimport Tooltip from 'theme_boost/bootstrap/tooltip';\nimport log from 'core/log';\n\n/**\n * List of Bootstrap 4 elements to replace with Bootstrap 5 elements.\n * This list is based on the Bootstrap 4 to 5 migration guide:\n * https://getbootstrap.com/docs/5.0/migration/\n *\n * The list is not exhaustive and it will be updated as needed.\n */\nconst bootstrapElements = [\n    {\n        selector: '.alert button.close',\n        replacements: [\n            {bs4: 'data-dismiss', bs5: 'data-bs-dismiss'},\n        ],\n    },\n    {\n        selector: '[data-toggle=\"modal\"]',\n        replacements: [\n            {bs4: 'data-toggle', bs5: 'data-bs-toggle'},\n            {bs4: 'data-target', bs5: 'data-bs-target'},\n        ],\n    },\n    {\n        selector: '.modal .modal-header button.close',\n        replacements: [\n            {bs4: 'data-dismiss', bs5: 'data-bs-dismiss'},\n        ],\n    },\n    {\n        selector: '[data-toggle=\"dropdown\"]',\n        replacements: [\n            {bs4: 'data-toggle', bs5: 'data-bs-toggle'},\n        ],\n    },\n    {\n        selector: '[data-toggle=\"collapse\"]',\n        replacements: [\n            {bs4: 'data-toggle', bs5: 'data-bs-toggle'},\n            {bs4: 'data-target', bs5: 'data-bs-target'},\n            {bs4: 'data-parent', bs5: 'data-bs-parent'},\n        ],\n    },\n    {\n        selector: '.carousel [data-slide]',\n        replacements: [\n            {bs4: 'data-slide', bs5: 'data-bs-slide'},\n            {bs4: 'data-target', bs5: 'data-bs-target'},\n        ],\n    },\n    {\n        selector: '[data-toggle=\"tooltip\"]',\n        replacements: [\n            {bs4: 'data-toggle', bs5: 'data-bs-toggle'},\n            {bs4: 'data-placement', bs5: 'data-bs-placement'},\n            {bs4: 'data-animation', bs5: 'data-bs-animation'},\n            {bs4: 'data-delay', bs5: 'data-bs-delay'},\n            {bs4: 'data-title', bs5: 'data-bs-title'},\n            {bs4: 'data-html', bs5: 'data-bs-html'},\n            {bs4: 'data-trigger', bs5: 'data-bs-trigger'},\n            {bs4: 'data-selector', bs5: 'data-bs-selector'},\n            {bs4: 'data-container', bs5: 'data-bs-container'},\n        ],\n    },\n    {\n        selector: '[data-toggle=\"popover\"]',\n        replacements: [\n            {bs4: 'data-toggle', bs5: 'data-bs-toggle'},\n            {bs4: 'data-content', bs5: 'data-bs-content'},\n            {bs4: 'data-placement', bs5: 'data-bs-placement'},\n            {bs4: 'data-animation', bs5: 'data-bs-animation'},\n            {bs4: 'data-delay', bs5: 'data-bs-delay'},\n            {bs4: 'data-title', bs5: 'data-bs-title'},\n            {bs4: 'data-html', bs5: 'data-bs-html'},\n            {bs4: 'data-trigger', bs5: 'data-bs-trigger'},\n            {bs4: 'data-selector', bs5: 'data-bs-selector'},\n            {bs4: 'data-container', bs5: 'data-bs-container'},\n        ],\n    },\n    {\n        selector: '[data-toggle=\"tab\"]',\n        replacements: [\n            {bs4: 'data-toggle', bs5: 'data-bs-toggle'},\n            {bs4: 'data-target', bs5: 'data-bs-target'},\n        ],\n    },\n];\n\n/**\n * Replace Bootstrap 4 attributes with Bootstrap 5 attributes.\n *\n * @param {HTMLElement} container The element to search for Bootstrap 4 elements.\n */\nconst replaceBootstrap4Attributes = (container) => {\n    for (const bootstrapElement of bootstrapElements) {\n        const elements = container.querySelectorAll(bootstrapElement.selector);\n        for (const element of elements) {\n            for (const replacement of bootstrapElement.replacements) {\n                if (element.hasAttribute(replacement.bs4)) {\n                    element.setAttribute(replacement.bs5, element.getAttribute(replacement.bs4));\n                    element.removeAttribute(replacement.bs4);\n                    log.debug(`Silent Bootstrap 4 to 5 compatibility: ${replacement.bs4} replaced by ${replacement.bs5}`);\n                    log.debug(element);\n                }\n            }\n        }\n    }\n};\n\n/**\n * Ensure Bootstrap 4 components are initialized.\n *\n * Some elements (tooltip and popovers) needs to be initialized manually after adding the data attributes.\n *\n * @param {HTMLElement} container The element to search for Bootstrap 4 elements.\n */\nconst initializeBootsrap4Components = (container) => {\n    const popoverConfig = {\n        container: 'body',\n        trigger: 'focus',\n        allowList: Object.assign(DefaultAllowlist, {table: [], thead: [], tbody: [], tr: [], th: [], td: []}),\n    };\n    container.querySelectorAll('[data-bs-toggle=\"popover\"]').forEach((tooltipTriggerEl) => {\n        const popOverInstance = Popover.getInstance(tooltipTriggerEl);\n        if (!popOverInstance) {\n            new Popover(tooltipTriggerEl, popoverConfig);\n        }\n    });\n\n    container.querySelectorAll('[data-bs-toggle=\"tooltip\"]').forEach((tooltipTriggerEl) => {\n        const tooltipInstance = Tooltip.getInstance(tooltipTriggerEl);\n        if (!tooltipInstance) {\n            new Tooltip(tooltipTriggerEl);\n        }\n    });\n};\n\n/**\n * Init Bootstrap 4 compatibility.\n *\n * @deprecated since Moodle 5.0\n * @param {HTMLElement} element The element to search for Bootstrap 4 elements.\n */\nexport const init = (element) => {\n    if (!element) {\n        element = document;\n    }\n    replaceBootstrap4Attributes(element);\n    initializeBootsrap4Components(element);\n};\n"], "names": ["bootstrapElements", "selector", "replacements", "bs4", "bs5", "element", "document", "container", "bootstrapElement", "elements", "querySelectorAll", "replacement", "hasAttribute", "setAttribute", "getAttribute", "removeAttribute", "debug", "replaceBootstrap4Attributes", "popoverConfig", "trigger", "allowList", "Object", "assign", "DefaultAllowlist", "table", "thead", "tbody", "tr", "th", "td", "for<PERSON>ach", "tooltipTriggerEl", "Popover", "getInstance", "<PERSON><PERSON><PERSON>", "initializeBootsrap4Components"], "mappings": ";;;;;;;;;;;;;8MA0CMA,kBAAoB,CACtB,CACIC,SAAU,sBACVC,aAAc,CACV,CAACC,IAAK,eAAgBC,IAAK,qBAGnC,CACIH,SAAU,wBACVC,aAAc,CACV,CAACC,IAAK,cAAeC,IAAK,kBAC1B,CAACD,IAAK,cAAeC,IAAK,oBAGlC,CACIH,SAAU,oCACVC,aAAc,CACV,CAACC,IAAK,eAAgBC,IAAK,qBAGnC,CACIH,SAAU,2BACVC,aAAc,CACV,CAACC,IAAK,cAAeC,IAAK,oBAGlC,CACIH,SAAU,2BACVC,aAAc,CACV,CAACC,IAAK,cAAeC,IAAK,kBAC1B,CAACD,IAAK,cAAeC,IAAK,kBAC1B,CAACD,IAAK,cAAeC,IAAK,oBAGlC,CACIH,SAAU,yBACVC,aAAc,CACV,CAACC,IAAK,aAAcC,IAAK,iBACzB,CAACD,IAAK,cAAeC,IAAK,oBAGlC,CACIH,SAAU,0BACVC,aAAc,CACV,CAACC,IAAK,cAAeC,IAAK,kBAC1B,CAACD,IAAK,iBAAkBC,IAAK,qBAC7B,CAACD,IAAK,iBAAkBC,IAAK,qBAC7B,CAACD,IAAK,aAAcC,IAAK,iBACzB,CAACD,IAAK,aAAcC,IAAK,iBACzB,CAACD,IAAK,YAAaC,IAAK,gBACxB,CAACD,IAAK,eAAgBC,IAAK,mBAC3B,CAACD,IAAK,gBAAiBC,IAAK,oBAC5B,CAACD,IAAK,iBAAkBC,IAAK,uBAGrC,CACIH,SAAU,0BACVC,aAAc,CACV,CAACC,IAAK,cAAeC,IAAK,kBAC1B,CAACD,IAAK,eAAgBC,IAAK,mBAC3B,CAACD,IAAK,iBAAkBC,IAAK,qBAC7B,CAACD,IAAK,iBAAkBC,IAAK,qBAC7B,CAACD,IAAK,aAAcC,IAAK,iBACzB,CAACD,IAAK,aAAcC,IAAK,iBACzB,CAACD,IAAK,YAAaC,IAAK,gBACxB,CAACD,IAAK,eAAgBC,IAAK,mBAC3B,CAACD,IAAK,gBAAiBC,IAAK,oBAC5B,CAACD,IAAK,iBAAkBC,IAAK,uBAGrC,CACIH,SAAU,sBACVC,aAAc,CACV,CAACC,IAAK,cAAeC,IAAK,kBAC1B,CAACD,IAAK,cAAeC,IAAK,mCA4DjBC,UACZA,UACDA,QAAUC,UApDmBC,CAAAA,gBAC5B,MAAMC,oBAAoBR,kBAAmB,OACxCS,SAAWF,UAAUG,iBAAiBF,iBAAiBP,cACxD,MAAMI,WAAWI,aACb,MAAME,eAAeH,iBAAiBN,aACnCG,QAAQO,aAAaD,YAAYR,OACjCE,QAAQQ,aAAaF,YAAYP,IAAKC,QAAQS,aAAaH,YAAYR,MACvEE,QAAQU,gBAAgBJ,YAAYR,kBAChCa,uDAAgDL,YAAYR,4BAAmBQ,YAAYP,mBAC3FY,MAAMX,YA6C1BY,CAA4BZ,SA/BOE,CAAAA,kBAC7BW,cAAgB,CAClBX,UAAW,OACXY,QAAS,QACTC,UAAWC,OAAOC,OAAOC,4BAAkB,CAACC,MAAO,GAAIC,MAAO,GAAIC,MAAO,GAAIC,GAAI,GAAIC,GAAI,GAAIC,GAAI,MAErGtB,UAAUG,iBAAiB,8BAA8BoB,SAASC,mBACtCC,iBAAQC,YAAYF,uBAEpCC,iBAAQD,iBAAkBb,kBAItCX,UAAUG,iBAAiB,8BAA8BoB,SAASC,mBACtCG,iBAAQD,YAAYF,uBAEpCG,iBAAQH,sBAgBpBI,CAA8B9B"}