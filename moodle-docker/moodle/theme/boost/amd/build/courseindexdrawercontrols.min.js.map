{"version": 3, "file": "courseindexdrawercontrols.min.js", "sources": ["../src/courseindexdrawercontrols.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Controls for the course index drawer, such as expand-all/collapse-all sections.\n *\n * @module     theme_boost/courseindexdrawercontrols\n * @copyright  2023 Stefan <PERSON>tedt\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nimport {BaseComponent} from 'core/reactive';\nimport {getCurrentCourseEditor} from 'core_courseformat/courseeditor';\n\nexport default class Component extends BaseComponent {\n\n  create() {\n    this.name = 'courseindexdrawercontrols';\n    this.selectors = {\n      COLLAPSEALL: `[data-action=\"collapseallcourseindexsections\"]`,\n      EXPANDALL: `[data-action=\"expandallcourseindexsections\"]`,\n    };\n  }\n\n  /**\n   * @param {element|string} target the DOM main element or its ID\n   * @param {object} selectors optional css selector overrides\n   * @return {Component}\n   */\n  static init(target, selectors) {\n    return new Component({\n      element: document.getElementById(target),\n      reactive: getCurrentCourseEditor(),\n      selectors,\n    });\n  }\n\n  /**\n   * Initial state ready method.\n   */\n  stateReady() {\n    // Attach the on-click event handlers to the expand-all and collapse-all buttons, if present.\n    const expandAllBtn = this.getElement(this.selectors.EXPANDALL);\n    if (expandAllBtn) {\n      this.addEventListener(expandAllBtn, 'click', this._expandAllSections);\n\n    }\n    const collapseAllBtn = this.getElement(this.selectors.COLLAPSEALL);\n    if (collapseAllBtn) {\n      this.addEventListener(collapseAllBtn, 'click', this._collapseAllSections);\n    }\n  }\n\n  /**\n   * On-click event handler for the collapse-all button.\n   * @private\n   */\n  _collapseAllSections() {\n    this._toggleAllSections(true);\n  }\n\n  /**\n   * On-click event handler for the expand-all button.\n   * @private\n   */\n  _expandAllSections() {\n    this._toggleAllSections(false);\n  }\n\n  /**\n   * Collapses or expands all sections in the course index.\n   * @param {boolean} expandOrCollapse set to TRUE to collapse all, and FALSE to expand all.\n   * @private\n   */\n  _toggleAllSections(expandOrCollapse) {\n    this.reactive.dispatch('allSectionsIndexCollapsed', expandOrCollapse);\n  }\n}\n"], "names": ["Component", "BaseComponent", "create", "name", "selectors", "COLLAPSEALL", "EXPANDALL", "target", "element", "document", "getElementById", "reactive", "stateReady", "expandAllBtn", "this", "getElement", "addEventListener", "_expandAllSections", "collapseAllBtn", "_collapseAllSections", "_toggleAllSections", "expandOrCollapse", "dispatch"], "mappings": ";;;;;;;;MAyBqBA,kBAAkBC,wBAErCC,cACOC,KAAO,iCACPC,UAAY,CACfC,6DACAC,sEASQC,OAAQH,kBACX,IAAIJ,UAAU,CACnBQ,QAASC,SAASC,eAAeH,QACjCI,UAAU,0CACVP,UAAAA,YAOJQ,mBAEQC,aAAeC,KAAKC,WAAWD,KAAKV,UAAUE,WAChDO,mBACGG,iBAAiBH,aAAc,QAASC,KAAKG,0BAG9CC,eAAiBJ,KAAKC,WAAWD,KAAKV,UAAUC,aAClDa,qBACGF,iBAAiBE,eAAgB,QAASJ,KAAKK,sBAQxDA,4BACOC,oBAAmB,GAO1BH,0BACOG,oBAAmB,GAQ1BA,mBAAmBC,uBACZV,SAASW,SAAS,4BAA6BD"}