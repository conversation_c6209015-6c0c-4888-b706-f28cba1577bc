/**
 * Contain the logic for a drawer.
 *
 * @module theme_boost/drawer
 * @copyright  2016 Damyon Wiese
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
define("theme_boost/drawer",["jquery","core/custom_interaction_events","core/log","core/pubsub","core/aria","core_user/repository"],(function($,CustomEvents,Log,PubSub,Aria,UserRepository){var SELECTORS_TOGGLE_REGION='[data-region="drawer-toggle"]',SELECTORS_TOGGLE_ACTION='[data-action="toggle-drawer"]',SELECTORS_BODY="body",SELECTORS_SECTION='.list-group-item[href*="#section-"]',SELECTORS_DRAWER="#nav-drawer",small=$(document).width()<768,Drawer=function(){$(SELECTORS_TOGGLE_REGION).length||Log.debug("Page is missing a drawer region"),$(SELECTORS_TOGGLE_ACTION).length||Log.debug("Page is missing a drawer toggle link"),$(SELECTORS_TOGGLE_REGION).each(function(index,ele){var trigger=$(ele).find(SELECTORS_TOGGLE_ACTION),drawerid=trigger.attr("aria-controls"),drawer=$(document.getElementById(drawerid)),hidden="false"==trigger.attr("aria-expanded"),side=trigger.attr("data-side"),body=$(SELECTORS_BODY),preference=trigger.attr("data-preference");small&&UserRepository.setUserPreference(preference,!1),drawer.on("mousewheel DOMMouseScroll",this.preventPageScroll),hidden?trigger.attr("aria-expanded","false"):(body.addClass("drawer-open-"+side),trigger.attr("aria-expanded","true"))}.bind(this)),this.registerEventListeners(),small&&this.closeAll()};return Drawer.prototype.closeAll=function(){$(SELECTORS_TOGGLE_REGION).each((function(index,ele){var trigger=$(ele).find(SELECTORS_TOGGLE_ACTION),side=trigger.attr("data-side"),body=$(SELECTORS_BODY),drawerid=trigger.attr("aria-controls"),drawer=$(document.getElementById(drawerid)),preference=trigger.attr("data-preference");trigger.attr("aria-expanded","false"),body.removeClass("drawer-open-"+side),Aria.hide(drawer.get()),drawer.addClass("closed"),small||UserRepository.setUserPreference(preference,!1)}))},Drawer.prototype.toggleDrawer=function(e){var trigger=$(e.target).closest("[data-action=toggle-drawer]"),drawerid=trigger.attr("aria-controls"),drawer=$(document.getElementById(drawerid)),body=$(SELECTORS_BODY),side=trigger.attr("data-side"),preference=trigger.attr("data-preference");small&&UserRepository.setUserPreference(preference,!1),body.addClass("drawer-ease");var open="true"==trigger.attr("aria-expanded");open?(body.removeClass("drawer-open-"+side),trigger.attr("aria-expanded","false"),drawer.addClass("closed").delay(500).queue((function(){$(this).hasClass("closed")&&Aria.hide(this),$(this).dequeue()})),small||UserRepository.setUserPreference(preference,!1)):(trigger.attr("aria-expanded","true"),Aria.unhide(drawer.get()),drawer.focus(),body.addClass("drawer-open-"+side),drawer.removeClass("closed"),small||UserRepository.setUserPreference(preference,!0)),PubSub.publish("nav-drawer-toggle-start",open)},Drawer.prototype.preventPageScroll=function(e){var delta=e.wheelDelta||e.originalEvent&&e.originalEvent.wheelDelta||-e.originalEvent.detail,bottomOverflow=this.scrollTop+$(this).outerHeight()-this.scrollHeight>=0,topOverflow=this.scrollTop<=0;(delta<0&&bottomOverflow||delta>0&&topOverflow)&&e.preventDefault()},Drawer.prototype.registerEventListeners=function(){$(SELECTORS_TOGGLE_ACTION).each(function(index,element){CustomEvents.define($(element),[CustomEvents.events.activate]),$(element).on(CustomEvents.events.activate,function(e,data){this.toggleDrawer(data.originalEvent),data.originalEvent.preventDefault()}.bind(this))}.bind(this)),$(SELECTORS_SECTION).click(function(){small&&this.closeAll()}.bind(this)),$(SELECTORS_DRAWER).on("webkitTransitionEnd msTransitionEnd transitionend",(function(e){var open=!!$(e.target).closest(SELECTORS_DRAWER).attr("aria-hidden");PubSub.publish("nav-drawer-toggle-end",open)}))},{init:function(){return new Drawer}}}));

//# sourceMappingURL=drawer.min.js.map