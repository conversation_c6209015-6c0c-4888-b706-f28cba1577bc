define("theme_boost/footer-popover",["exports","./bootstrap/popover"],(function(_exports,_popover){var obj;
/**
   * Shows the footer content in a popover.
   *
   * @module     theme_boost/footer-popover
   * @copyright  2021 Bas Brands
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),Object.defineProperty(_exports,"Popover",{enumerable:!0,get:function(){return _popover.default}}),_exports.init=void 0,_popover=(obj=_popover)&&obj.__esModule?obj:{default:obj};const SELECTORS_FOOTERCONTAINER='[data-region="footer-container-popover"]',SELECTORS_FOOTERCONTENT='[data-region="footer-content-popover"]',SELECTORS_FOOTERBUTTON='[data-action="footer-popover"]',SELECTORS_FOOTERARROW='[data-region="footer-container-popover"] .popover-arrow';let footerIsShown=!1;_exports.init=()=>{const container=document.querySelector(SELECTORS_FOOTERCONTAINER),footerButton=document.querySelector(SELECTORS_FOOTERBUTTON),footerArrow=document.querySelector(SELECTORS_FOOTERARROW);new _popover.default(footerButton,{content:getFooterContent,container:container,html:!0,placement:"top",customClass:"footer",trigger:"click",boundary:"viewport",modifiers:[{name:"preventOverflow",options:{boundariesElement:"viewport",padding:48}},{name:"arrow",options:{element:footerArrow}}]}),document.addEventListener("click",(e=>{footerIsShown&&!e.target.closest(SELECTORS_FOOTERCONTAINER)&&_popover.default.getInstance(footerButton).hide()}),!0),document.addEventListener("keydown",(e=>{footerIsShown&&"Escape"===e.key&&(_popover.default.getInstance(footerButton).hide(),footerButton.focus())})),document.addEventListener("focus",(e=>{footerIsShown&&!e.target.closest(SELECTORS_FOOTERCONTAINER)&&_popover.default.getInstance(footerButton).hide()}),!0),footerButton.addEventListener("show.bs.popover",(()=>{footerIsShown=!0})),footerButton.addEventListener("hide.bs.popover",(()=>{footerIsShown=!1}))};const getFooterContent=()=>document.querySelector(SELECTORS_FOOTERCONTENT).innerHTML}));

//# sourceMappingURL=footer-popover.min.js.map