{"version": 3, "file": "footer-popover.min.js", "sources": ["../src/footer-popover.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Mo<PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Shows the footer content in a popover.\n *\n * @module     theme_boost/footer-popover\n * @copyright  2021 Bas Brands\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Popover from './bootstrap/popover';\n\nconst SELECTORS = {\n    FOOTERCONTAINER: '[data-region=\"footer-container-popover\"]',\n    FOOTERCONTENT: '[data-region=\"footer-content-popover\"]',\n    FOOTERBUTTON: '[data-action=\"footer-popover\"]',\n    FOOTERARROW: '[data-region=\"footer-container-popover\"] .popover-arrow',\n};\n\nlet footerIsShown = false;\n\nexport const init = () => {\n    const container = document.querySelector(SELECTORS.FOOTERCONTAINER);\n    const footerButton = document.querySelector(SELECTORS.FOOTERBUTTON);\n    const footerArrow = document.querySelector(SELECTORS.FOOTERARROW);\n\n    new Popover(footerButton, {\n        content: getFooterContent,\n        container: container,\n        html: true,\n        placement: 'top',\n        customClass: 'footer',\n        trigger: 'click',\n        boundary: 'viewport',\n        modifiers: [\n            {\n                name: 'preventOverflow',\n                options: {\n                    boundariesElement: 'viewport',\n                    padding: 48,\n                },\n            },\n            {\n                name: 'arrow',\n                options: {\n                    element: footerArrow,\n                },\n            },\n        ]\n    });\n\n    document.addEventListener('click', e => {\n        if (footerIsShown && !e.target.closest(SELECTORS.FOOTERCONTAINER)) {\n            Popover.getInstance(footerButton).hide();\n        }\n    },\n    true);\n\n    document.addEventListener('keydown', e => {\n        if (footerIsShown && e.key === 'Escape') {\n            Popover.getInstance(footerButton).hide();\n            footerButton.focus();\n        }\n    });\n\n    document.addEventListener('focus', e => {\n        if (footerIsShown && !e.target.closest(SELECTORS.FOOTERCONTAINER)) {\n            Popover.getInstance(footerButton).hide();\n        }\n    },\n    true);\n\n    footerButton.addEventListener('show.bs.popover', () => {\n        footerIsShown = true;\n    });\n\n    footerButton.addEventListener('hide.bs.popover', () => {\n        footerIsShown = false;\n    });\n};\n\n/**\n * Get the footer content for popover.\n *\n * @returns {String} HTML string\n * @private\n */\nconst getFooterContent = () => {\n    return document.querySelector(SELECTORS.FOOTERCONTENT).innerHTML;\n};\n\nexport {\n    Popover\n};\n"], "names": ["SELECTORS", "footerIsShown", "container", "document", "querySelector", "footer<PERSON><PERSON><PERSON>", "footer<PERSON>rrow", "Popover", "content", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "html", "placement", "customClass", "trigger", "boundary", "modifiers", "name", "options", "boundariesElement", "padding", "element", "addEventListener", "e", "target", "closest", "getInstance", "hide", "key", "focus", "innerHTML"], "mappings": ";;;;;;;oPAyBMA,0BACe,2CADfA,wBAEa,yCAFbA,uBAGY,iCAHZA,sBAIW,8DAGbC,eAAgB,gBAEA,WACVC,UAAYC,SAASC,cAAcJ,2BACnCK,aAAeF,SAASC,cAAcJ,wBACtCM,YAAcH,SAASC,cAAcJ,2BAEvCO,iBAAQF,aAAc,CACtBG,QAASC,iBACTP,UAAWA,UACXQ,MAAM,EACNC,UAAW,MACXC,YAAa,SACbC,QAAS,QACTC,SAAU,WACVC,UAAW,CACP,CACIC,KAAM,kBACNC,QAAS,CACLC,kBAAmB,WACnBC,QAAS,KAGjB,CACIH,KAAM,QACNC,QAAS,CACLG,QAASd,iBAMzBH,SAASkB,iBAAiB,SAASC,IAC3BrB,gBAAkBqB,EAAEC,OAAOC,QAAQxB,6CAC3ByB,YAAYpB,cAAcqB,UAG1C,GAEAvB,SAASkB,iBAAiB,WAAWC,IAC7BrB,eAA2B,WAAVqB,EAAEK,uBACXF,YAAYpB,cAAcqB,OAClCrB,aAAauB,YAIrBzB,SAASkB,iBAAiB,SAASC,IAC3BrB,gBAAkBqB,EAAEC,OAAOC,QAAQxB,6CAC3ByB,YAAYpB,cAAcqB,UAG1C,GAEArB,aAAagB,iBAAiB,mBAAmB,KAC7CpB,eAAgB,KAGpBI,aAAagB,iBAAiB,mBAAmB,KAC7CpB,eAAgB,YAUlBQ,iBAAmB,IACdN,SAASC,cAAcJ,yBAAyB6B"}