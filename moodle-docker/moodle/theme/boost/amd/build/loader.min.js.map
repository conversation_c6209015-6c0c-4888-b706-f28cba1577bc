{"version": 3, "file": "loader.min.js", "sources": ["../src/loader.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Template renderer for Moodle. Load and render Moodle templates with Mustache.\n *\n * @module     theme_boost/loader\n * @copyright  2015 Damyon Wiese <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n * @since      2.9\n */\n\nimport * as Aria from './aria';\nimport * as Bootstrap from './index';\nimport Pending from 'core/pending';\nimport {DefaultAllowlist} from './bootstrap/util/sanitizer';\nimport setupBootstrapPendingChecks from './pending';\nimport EventHandler from './bootstrap/dom/event-handler';\n\n/**\n * Rember the last visited tabs.\n */\nconst rememberTabs = () => {\n    const tabTriggerList = document.querySelectorAll('a[data-bs-toggle=\"tab\"]');\n    [...tabTriggerList].map(tabTriggerEl => tabTriggerEl.addEventListener('shown.bs.tab', (e) => {\n        var hash = e.target.getAttribute('href');\n        if (history.replaceState) {\n            history.replaceState(null, null, hash);\n        } else {\n            location.hash = hash;\n        }\n    }));\n    const hash = window.location.hash;\n    if (hash) {\n        const tab = document.querySelector('[role=\"tablist\"] [href=\"' + hash + '\"]');\n        if (tab) {\n            tab.click();\n        }\n    }\n};\n\n/**\n * Enable all popovers\n *\n */\nconst enablePopovers = () => {\n    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle=\"popover\"]');\n    const popoverConfig = {\n        container: 'body',\n        trigger: 'focus',\n        allowList: Object.assign(DefaultAllowlist, {table: [], thead: [], tbody: [], tr: [], th: [], td: []}),\n    };\n    [...popoverTriggerList].map(popoverTriggerEl => new Bootstrap.Popover(popoverTriggerEl, popoverConfig));\n\n    // Enable dynamically created popovers inside modals.\n    document.addEventListener('core/modal:bodyRendered', (e) => {\n        const modal = e.target;\n        const popoverTriggerList = modal.querySelectorAll('[data-bs-toggle=\"popover\"]');\n        [...popoverTriggerList].map(popoverTriggerEl => new Bootstrap.Popover(popoverTriggerEl, popoverConfig));\n    });\n\n    document.addEventListener('keydown', e => {\n        const popoverTrigger = e.target.closest('[data-bs-toggle=\"popover\"]');\n        if (e.key === 'Escape' && popoverTrigger) {\n            Bootstrap.Popover.getOrCreateInstance(popoverTrigger).hide();\n        }\n        if (e.key === 'Enter' && popoverTrigger) {\n            Bootstrap.Popover.getOrCreateInstance(popoverTrigger).show();\n        }\n    });\n    document.addEventListener('click', e => {\n        const popoverTrigger = e.target.closest('[data-bs-toggle=\"popover\"]');\n        if (!popoverTrigger) {\n            return;\n        }\n        const popover = Bootstrap.Popover.getOrCreateInstance(popoverTrigger);\n        if (!popover._isShown()) {\n            popover.show();\n        }\n    });\n};\n\n/**\n * Enable tooltips\n *\n */\nconst enableTooltips = () => {\n    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle=\"tooltip\"]');\n    [...tooltipTriggerList].map(tooltipTriggerEl => new Bootstrap.Tooltip(tooltipTriggerEl));\n};\n\n/**\n * Realocate Bootstrap events to the body element.\n *\n * Bootstrap 5 has a unique event handling mechanism that attaches all event handlers at the document level\n * during the capture phase, rather than the usual bubbling phase. As a result, original Bootstrap events\n * cannot be stopped or prevented, since the document is the first node executed in the capture phase.\n * For certain advanced UI elements, such as form autocomplete, it is important to capture key-down events before\n * Bootstrap's handlers to prevent unintended closures of elements. Therefore, we need to change the Bootstrap handler\n * so that it operates one level lower, specifically at the body level.\n */\nconst realocateBootstrapEvents = () => {\n    EventHandler.off(document, 'keydown.bs.dropdown.data-api', '.dropdown-menu', Bootstrap.Dropdown.dataApiKeydownHandler);\n    EventHandler.on(document.body, 'keydown.bs.dropdown.data-api', '.dropdown-menu', Bootstrap.Dropdown.dataApiKeydownHandler);\n};\n\nconst pendingPromise = new Pending('theme_boost/loader:init');\n\n// Add pending promise event listeners to relevant Bootstrap custom events.\nsetupBootstrapPendingChecks();\n\n// Setup Aria helpers for Bootstrap features.\nAria.init();\n\n// Remember the last visited tabs.\nrememberTabs();\n\n// Enable all popovers.\nenablePopovers();\n\n// Enable all tooltips.\nenableTooltips();\n\n// Realocate Bootstrap events to the body element.\nrealocateBootstrapEvents();\n\npendingPromise.resolve();\n\nexport {\n    Bootstrap,\n};\n"], "names": ["pendingPromise", "Pending", "Aria", "init", "document", "querySelectorAll", "map", "tabTriggerEl", "addEventListener", "e", "hash", "target", "getAttribute", "history", "replaceState", "location", "window", "tab", "querySelector", "click", "rememberTabs", "popoverTriggerList", "popoverConfig", "container", "trigger", "allowList", "Object", "assign", "DefaultAllowlist", "table", "thead", "tbody", "tr", "th", "td", "popoverTriggerEl", "Bootstrap", "Popover", "popoverTrigger", "closest", "key", "getOrCreateInstance", "hide", "show", "popover", "_isShown", "enablePopovers", "tooltipTriggerEl", "<PERSON><PERSON><PERSON>", "off", "Dropdown", "dataApiKeydownHandler", "on", "body", "resolve"], "mappings": ";;;;;;;;oVAsHMA,eAAiB,IAAIC,iBAAQ,mDAMnCC,KAAKC,OA1FgB,UACMC,SAASC,iBAAiB,4BAC7BC,KAAIC,cAAgBA,aAAaC,iBAAiB,gBAAiBC,QAC/EC,KAAOD,EAAEE,OAAOC,aAAa,QAC7BC,QAAQC,aACRD,QAAQC,aAAa,KAAM,KAAMJ,MAEjCK,SAASL,KAAOA,gBAGlBA,KAAOM,OAAOD,SAASL,QACzBA,KAAM,OACAO,IAAMb,SAASc,cAAc,2BAA6BR,KAAO,MACnEO,KACAA,IAAIE,UA+EhBC,GAtEuB,YACbC,mBAAqBjB,SAASC,iBAAiB,8BAC/CiB,cAAgB,CAClBC,UAAW,OACXC,QAAS,QACTC,UAAWC,OAAOC,OAAOC,4BAAkB,CAACC,MAAO,GAAIC,MAAO,GAAIC,MAAO,GAAIC,GAAI,GAAIC,GAAI,GAAIC,GAAI,UAEjGb,oBAAoBf,KAAI6B,kBAAoB,IAAIC,UAAUC,QAAQF,iBAAkBb,iBAGxFlB,SAASI,iBAAiB,2BAA4BC,QACpCA,EAAEE,OACiBN,iBAAiB,+BAC1BC,KAAI6B,kBAAoB,IAAIC,UAAUC,QAAQF,iBAAkBb,oBAG5FlB,SAASI,iBAAiB,WAAWC,UAC3B6B,eAAiB7B,EAAEE,OAAO4B,QAAQ,8BAC1B,WAAV9B,EAAE+B,KAAoBF,gBACtBF,UAAUC,QAAQI,oBAAoBH,gBAAgBI,OAE5C,UAAVjC,EAAE+B,KAAmBF,gBACrBF,UAAUC,QAAQI,oBAAoBH,gBAAgBK,UAG9DvC,SAASI,iBAAiB,SAASC,UACzB6B,eAAiB7B,EAAEE,OAAO4B,QAAQ,kCACnCD,4BAGCM,QAAUR,UAAUC,QAAQI,oBAAoBH,gBACjDM,QAAQC,YACTD,QAAQD,WAyCpBG,OA/B+B1C,SAASC,iBAAiB,+BAC7BC,KAAIyC,kBAAoB,IAAIX,UAAUY,QAAQD,0CAczDE,IAAI7C,SAAU,+BAAgC,iBAAkBgC,UAAUc,SAASC,6CACnFC,GAAGhD,SAASiD,KAAM,+BAAgC,iBAAkBjB,UAAUc,SAASC,uBAuBxGnD,eAAesD"}