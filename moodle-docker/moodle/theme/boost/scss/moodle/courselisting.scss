/*
 * Course listings and course summary styles.
 */

#page-course-pending .singlebutton,
#page-course-index .singlebutton,
#page-course-index-category .singlebutton,
#page-course-editsection .singlebutton {
    text-align: center;
}

#page-admin-course-manage #movecourses td img {
    margin: 0 .22em;
    vertical-align: text-bottom;
}

#page-course-pending .pendingcourserequests {
    margin-bottom: 1em;
}

#page-course-pending .pendingcourserequests .singlebutton {
    display: inline;
}

#page-course-pending .pendingcourserequests .cell {
    padding: 0 5px;
}

#page-course-pending .pendingcourserequests .cell.c6 {
    white-space: nowrap;
}

.coursebox {
    display: flex;
    flex-direction: column;
    .info {
        display: flex;
        align-items: center;
    }
}

#frontpage-available-course-list,
#frontpage-course-list,
.course-search-result {
    margin-top: $spacer * 0.5;
    .coursebox {
        padding: $spacer * 0.5;
        border: $border-width solid $border-color;
        margin-bottom: $spacer * 0.5;

        @include border-radius();
    }
}

.coursebox > .info > .coursename {
    font-size: $font-size-base;
    font-weight: normal;
    margin: 5px;
    padding: 0;
}

.coursebox .content .teachers li {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.coursebox .customfieldname,
.coursebox .customfieldseparator {
    font-weight: $font-weight-bold;
}

.coursebox .content .coursefile {
    max-width: 100px;
}

.coursebox .content .courseimage img {
    max-width: 100px;
    max-height: 100px;
}

.coursebox .content .coursecat,
.coursebox .content .summary,
.coursebox .content .courseimage,
.coursebox .content .coursefile,
.coursebox .content .teachers,
.coursebox.remotecoursebox .remotecourseinfo,
.coursebox .content .customfields-container {
    margin: 15px 5px 5px;
    padding: 0;
}

.category-browse {
    .coursebox .content .coursecat,
    .coursebox .content .summary,
    .coursebox .content .courseimage,
    .coursebox .content .coursefile,
    .coursebox .content .teachers,
    .coursebox.remotecoursebox .remotecourseinfo,
    .coursebox .content .customfields-container {
        margin-top: 0;
    }
}

.coursebox.collapsed > .content {
    display: none;
}

.courses > .paging.paging-morelink {
    text-align: center;
    padding: $spacer;
}

.course_category_tree .category .numberofcourse {
    font-size: $font-size-sm;
}

.course_category_tree .category > .info > .categoryname {
    margin: 5px;
    font-size: $font-size-base;
    font-weight: normal;
    padding: 2px 18px;
}

.course_category_tree .category.with_children > .info > .categoryname {
    background-image: url([[pix:moodle|t/expanded]]);
    background-size: $icon-width $icon-height;
    background-repeat: no-repeat;
    background-position: center left;
}

.course_category_tree .category.with_children.collapsed > .info > .categoryname {
    background-image: url([[pix:moodle|t/collapsed]]);
}
/* rtl:raw:
.course_category_tree .category.with_children.collapsed > .info > .categoryname {
    background-image:url([[pix:moodle|t/collapsed_rtl]]);
}
*/
.course_category_tree .category.collapsed > .content {
    display: none;
}

.course_category_tree .category > .content {
    padding-left: 16px;
}

#page-course-index-category .categorypicker {
    margin: 10px 0 20px;
}
