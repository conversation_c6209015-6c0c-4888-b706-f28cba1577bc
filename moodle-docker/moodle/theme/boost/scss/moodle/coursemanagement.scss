/*
 * Course management page styles.
 */

#course-category-listings {
    margin-bottom: 0;

    /** Two column layout */
    &.columns-2 {
        > #course-listing > div {
            position: relative;
            left: -1px;
        }
    }
    /** Three column layout */
    &.columns-3 > #course-listing > div {
        height: 100%;
    }

    > div > div {
        min-height: 300px;

        > ul.ml > li:first-child > div {
            border-top: 0;
        }
    }

    h3 {
        margin: 0;
        padding: 0.4rem 0.6rem 0.3rem;
    }

    h4 {
        margin: 1rem 0 0;
        padding: 0.6rem 1rem 0.5rem;
    }

    .moodle-actionmenu {
        white-space: nowrap;
    }

    .listing-actions {
        text-align: center;

        > .moodle-actionmenu {
            display: inline-block;
        }
    }

    ul.ml {
        list-style: none;
        margin: 1rem 0;

        ul.ml {
            margin: 0;
        }
    }

    .listitem {

        &[data-selected='1'] {
            border-left: calc(#{$list-group-border-width} + 5px) solid map-get($theme-colors, 'primary');
            padding-left: calc(#{$list-group-item-padding-x} - 5px);
        }
        &:hover {
            z-index: 2;
        }
    }

    .item-actions {
        margin-right: 1em;
        display: inline-block;

        &.show .menu {

            img {
                width: 12px;
                max-width: none;
            }
        }

        .menu-action-text {
            vertical-align: inherit;
        }
    }

    .listitem {
        > div {
            > .float-start {
                float: left;
            }

            > .float-end {
                float: right;
                text-align: right;
            }

            .item-actions {
                .action-show {
                    display: none;
                }

                .action-hide {
                    display: inline;
                }
            }

            .without-actions {
                color: $course-cat-without-actions-color;
            }

            .idnumber {
                margin-right: 2em;
            }
        }
        // The category or course is hidden.
        &[data-visible="0"] {
            color: $text-muted;

            > div {
                > a {
                    color: $text-muted;
                }

                .item-actions {
                    .action-show {
                        display: inline;
                    }

                    .action-hide {
                        display: none;
                    }
                }
            }
        }

        &.highlight {
            background-color: $body-bg;

            > div,
            > div:hover,
            &[data-selected='1'] > div {
                background-color: $table-hover-bg;
            }
        }
    }

    #course-listing {
        .listitem {
            .categoryname {
                display: inline-block;
                margin-left: 1em;
                color: $course-listing-color;
            }

            .coursename {
                display: inline-block;
                flex-basis: 10rem;
            }
        }

        > .firstpage .listitem:first-child > div .item-actions .action-moveup,
        > .lastpage .listitem:last-child > div .item-actions .action-movedown {
            display: none;
        }
    }

    #category-listing {
        .listitem.collapsed > ul.ml {
            display: none;
        }

        .listitem {
            &:first-child > div .item-actions .action-moveup,
            &:last-child > div .item-actions .action-movedown {
                display: none;
            }
        }

        .course-count {
            color: $course-listing-color;
            margin-right: 2rem;
            min-width: 3.5em;
            display: inline-block;
        }

        .category-listing > ul > .listitem:first-child {
            position: relative;
        }

        .category-bulk-actions {
            margin: 0 0.5em 0.5em;
            position: relative;
        }
    }

    .detail-pair {

        > * {
            display: inline-block;
        }

        .pair-key {
            font-weight: bold;
            vertical-align: top;

            span {
                margin-right: 1rem;
                display: block;
            }
        }

        .pair-value select {
            max-width: 100%;
        }
    }

    .listing-pagination {
        text-align: center;

        .yui3-button {
            @include button-variant($info, $info);
            border: 0;
            margin: 0.4rem 0.2rem 0.45rem;
            font-size: 10.4px;

            &.active-page {
                @include button-variant($primary, $primary);
            }
        }
    }

    .listing-pagination-totals {
        text-align: center;

        &.dimmed {
            color: $text-muted;
            margin: 0.4rem 1rem 0.45rem;
        }
    }

    .select-a-category .notifymessage,
    .select-a-category .alert {
        margin: 1em;
    }
}

#course-category-listings #course-listing .listitem .drag-handle {
    display: none;
}

.jsenabled #course-category-listings #course-listing .listitem .drag-handle {
    display: inline-block;
    margin: 0 6px 0 0;
    cursor: pointer;
}

/** Management header styling **/
.course-being-dragged-proxy {
    border: 0;
    color: $link-color;
    vertical-align: middle;
    padding: 0 0 0 4em;
}

.course-being-dragged {
    opacity: 0.5;
}

/**
 * Display sizes:
 * Large displays                   1200        +
 * Default displays                  980     1199
 * Tablets                           768      979
 * Small tablets and large phones    481      767
 * Phones                              0      480
 */

@media (min-width: 1200px) and (max-width: 1600px) {
    #course-category-listings.columns-3 {
        background-color: $body-bg;
        border: 0;

        #category-listing,
        #course-listing {
            width: 50%;
        }

        #category-listing > div,
        #course-listing > div,
        #course-detail > div {
            background-color: $body-bg;
        }

        #course-detail {
            width: 100%;
            margin-top: 1em;
        }
    }
}

@media (max-width: 1199px) {
    #course-category-listings.columns-2,
    #course-category-listings.columns-3 {
        border: 0;

        #category-listing,
        #course-listing,
        #course-detail {
            width: 100%;
            margin: 0 0 1em;
        }
    }
}
