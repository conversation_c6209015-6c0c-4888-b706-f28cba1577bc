.primary-navigation {
    .navigation {
        height: $navbar-height;
        .nav-link {
            height: $navbar-height;
            color: $gray-900;
            border-top: 3px solid transparent;
        }
    }
}

@include media-breakpoint-down(md) {
    .primary-navigation {
        display: none;
    }
    .editmode-switch-form label {
        @include visually-hidden();
    }
}

.drawer-primary .drawercontent {
    padding: 0;

    .list-group {
        border-radius: 0;
        margin-top: -1px;
        .list-group-item {
            border-left: 0;
            border-right: 0;
        }
    }
}
