.generaltable {
    width: 100%;
    margin-bottom: $spacer;
    color: $table-color;
    background-color: $table-bg; // Reset for nesting within parents with `background-color`.

    $table-cell-padding: .75rem !default;
    $table-cell-padding-sm: .3rem !default;

    th,
    td {
        padding: $table-cell-padding;
        vertical-align: top;
        border-top: $table-border-width solid $table-border-color;
    }

    thead th,
    thead td {
        vertical-align: bottom;
        border-bottom: calc(2 * #{$table-border-width}) solid $table-border-color;
    }

    tbody + tbody {
        border-top: calc(2 * #{$table-border-width}) solid $table-border-color;
    }

    &.table-sm {
        th,
        td {
            padding: $table-cell-padding-sm;
        }
    }
    tbody tr {
        &:hover {
            &.dimmed_text {
                a:not(.menu-action) {
                    color: $table-hover-color;
                }
            }
            td.sticky-column {
                background-color: $table-hover-bg;
            }
        }
    }
    // Allow table cells to inherit text color and background color from table rows.
    tbody {
        td,
        th {
            color: inherit;
            background-color: inherit;
        }
    }
}

table {
    caption {
        font-size: 24px;
        font-weight: bold;
        line-height: 42px;
        text-align: left;
        caption-side: top;
    }
    .sticky-column {
        position: sticky;
        left: 0;
        background-color: inherit;
    }
}

.table-dynamic > .loading-icon {
    position: absolute;
    left: calc(50% - 1.5rem);
    top: 200px;
    .icon {
        max-height: 3rem;
        max-width: 3rem;
        font-size: 3rem;
    }
}

.table-responsive {
    // Fix dropdown menu position inside responsive tables.
    .dropdown {
        position: static !important; /* stylelint-disable-line declaration-no-important */
    }
    // Fix visually hidden text in responsive tables.
    .table {
        margin-bottom: 0;
        .visually-hidden,
        .accesshide {
            position: relative !important; /* stylelint-disable-line declaration-no-important */
            display: block;
        }
    }
}
