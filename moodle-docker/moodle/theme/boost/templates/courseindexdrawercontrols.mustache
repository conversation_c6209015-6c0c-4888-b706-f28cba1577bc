{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_boost/courseindexdrawercontrols

    This template will render "expand-all" and "collapse-all" buttons for the course index drawer.

    Example context (json):
    {
    }
}}
<div id="courseindexdrawercontrols" class="dropdown">
    <button class="btn btn-icon mx-2"
            type="button"
            data-bs-toggle="dropdown"
            aria-haspopup="true"
            aria-expanded="false"
            title="{{#str}}courseindexoptions, courseformat{{/str}}"
    >
        <i class="icon fa fa-ellipsis-v fa-fw m-0" aria-hidden="true"></i>
    </button>
    <div class="dropdown-menu dropdown-menu-end">
        <a class="dropdown-item"
           href="#"
           data-action="expandallcourseindexsections"
        >
            {{#pix}} t/angles-down, core {{/pix}}
            {{#str}}expandall{{/str}}
        </a>
        <a class="dropdown-item"
           href="#"
           data-action="collapseallcourseindexsections"
        >
            <span class="dir-rtl-hide">{{#pix}} t/angles-right, core {{/pix}}</span>
            <span class="dir-ltr-hide">{{#pix}} t/angles-left, core {{/pix}}</span>
            {{#str}}collapseall{{/str}}
        </a>
    </div>
</div>
{{#js}}
    require(['theme_boost/courseindexdrawercontrols'], function(component) {
    component.init('courseindexdrawercontrols');
    });
{{/js}}
