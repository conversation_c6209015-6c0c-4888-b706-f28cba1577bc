{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_boost/drawer

    Example context (json):
    {
        "drawerclasses": "drawer drawer-right",
        "drawertrigger": "toggleblocks",
        "tooltipplacement": "right",
        "drawerconent": "Content for the blocks region",
        "closebuttontext": "Close drawer"
    }
}}
<div {{!
    }} class="{{$drawerclasses}}{{/drawerclasses}} d-print-none not-initialized"{{!
    }} data-region="fixed-drawer"{{!
    }} id="{{$id}}{{/id}}"{{!
    }} data-preference="{{$drawerpreferencename}}{{/drawerpreferencename}}"{{!
    }} data-state="{{$drawerstate}}{{/drawerstate}}"{{!
    }} data-forceopen="{{$forceopen}}0{{/forceopen}}"{{!
    }} data-close-on-resize="{{$drawercloseonresize}}0{{/drawercloseonresize}}"{{!
    }}>
    <div class="drawerheader">
        <button
            class="btn btn-icon drawertoggle hidden"
            data-toggler="drawers"
            data-action="closedrawer"
            data-target="{{$id}}{{/id}}"
            data-bs-toggle="tooltip"
            data-bs-placement="{{$tooltipplacement}}right{{/tooltipplacement}}"
            title="{{$closebuttontext}}{{#str}}closedrawer, core{{/str}}{{/closebuttontext}}"
        >
            {{#pix}} e/cancel, core {{/pix}}
        </button>
        <a
            href="{{{ config.homeurl }}}"
            title="{{{ sitename }}}"
            data-region="site-home-link"
            class="aabtn text-reset d-flex align-items-center py-1 h-100 d-md-none"
        >
            {{$drawerheading}}{{/drawerheading}}
        </a>
        <div class="drawerheadercontent hidden">
            {{$drawerheadercontent}}{{/drawerheadercontent}}
        </div>
    </div>
    <div class="drawercontent drag-container" data-usertour="scroller">
        {{$drawercontent}}{{/drawercontent}}
    </div>
</div>
{{#js}}
M.util.js_pending('theme_boost/drawers:load');
require(['theme_boost/drawers'], function() {
    M.util.js_complete('theme_boost/drawers:load');
});
{{/js}}
