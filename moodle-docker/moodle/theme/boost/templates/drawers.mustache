{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_boost/drawers

    Boost drawer template.

    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page
    * bodyattributes - attributes for the body tag as a string of html attributes
    * sidepreblocks - HTML for the blocks
    * hasblocks - true if there are blocks on this page
    * courseindexopen - true if the nav drawer should be open on page load
    * regionmainsettingsmenu - HTML for the region main settings menu
    * hasregionmainsettingsmenu - There is a region main settings menu on this page.

    Example context (json):
    {
        "sitename": "<PERSON>od<PERSON>",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         },
        "bodyattributes":"",
        "sidepreblocks": "<h2>Blocks html goes here</h2>",
        "hasblocks":true,
        "courseindexopen": true,
        "navdraweropen": false,
        "blockdraweropen": true,
        "regionmainsettingsmenu": "",
        "hasregionmainsettingsmenu": false,
        "addblockbutton": ""
    }
}}
{{> theme_boost/head }}

<body {{{ bodyattributes }}}>
{{> core/local/toast/wrapper}}
<div id="page-wrapper" class="d-print-block">

    {{{ output.standard_top_of_body_html }}}

    {{> theme_boost/navbar }}
    {{#courseindex}}
        {{< theme_boost/drawer }}
            {{$id}}theme_boost-drawers-courseindex{{/id}}
            {{$drawerheadercontent}}
                {{> theme_boost/courseindexdrawercontrols}}
            {{/drawerheadercontent}}
            {{$drawerclasses}}drawer drawer-left {{#courseindexopen}}show{{/courseindexopen}}{{/drawerclasses}}
            {{$drawercontent}}
                {{{courseindex}}}
            {{/drawercontent}}
            {{$drawerpreferencename}}drawer-open-index{{/drawerpreferencename}}
            {{$drawerstate}}show-drawer-left{{/drawerstate}}
            {{$tooltipplacement}}right{{/tooltipplacement}}
            {{$closebuttontext}}{{#str}}closecourseindex, core{{/str}}{{/closebuttontext}}
        {{/ theme_boost/drawer}}
    {{/courseindex}}
    {{#hasblocks}}
        {{< theme_boost/drawer }}
            {{$id}}theme_boost-drawers-blocks{{/id}}
            {{$drawerclasses}}drawer drawer-right{{#blockdraweropen}} show{{/blockdraweropen}}{{/drawerclasses}}
            {{$drawercontent}}
                <div class="d-print-none">
                    {{{ addblockbutton }}}
                    {{{ sidepreblocks }}}
                </div>
            {{/drawercontent}}
            {{$drawerpreferencename}}drawer-open-block{{/drawerpreferencename}}
            {{$forceopen}}{{#forceblockdraweropen}}1{{/forceblockdraweropen}}{{/forceopen}}
            {{$drawerstate}}show-drawer-right{{/drawerstate}}
            {{$tooltipplacement}}left{{/tooltipplacement}}
            {{$drawercloseonresize}}1{{/drawercloseonresize}}
            {{$closebuttontext}}{{#str}}closeblockdrawer, core{{/str}}{{/closebuttontext}}
        {{/ theme_boost/drawer}}
    {{/hasblocks}}
    <div id="page" data-region="mainpage" data-usertour="scroller" class="drawers {{#courseindexopen}}show-drawer-left{{/courseindexopen}} {{#blockdraweropen}}show-drawer-right{{/blockdraweropen}} drag-container">
        <div id="topofscroll" class="main-inner">
            <div class="drawer-toggles d-flex">
                {{#courseindex}}
                    <div class="drawer-toggler drawer-left-toggle open-nav d-print-none">
                        <button
                            class="btn icon-no-margin"
                            data-toggler="drawers"
                            data-action="toggle"
                            data-target="theme_boost-drawers-courseindex"
                            data-bs-toggle="tooltip"
                            data-bs-placement="right"
                            title="{{#str}}opendrawerindex, core{{/str}}"
                        >
                            <span class="visually-hidden">{{#str}}opendrawerindex, core{{/str}}</span>
                            {{#pix}} t/index_drawer, moodle {{/pix}}
                        </button>
                    </div>
                {{/courseindex}}
                {{#hasblocks}}
                    <div class="drawer-toggler drawer-right-toggle ms-auto d-print-none">
                        <button
                            class="btn icon-no-margin"
                            data-toggler="drawers"
                            data-action="toggle"
                            data-target="theme_boost-drawers-blocks"
                            data-bs-toggle="tooltip"
                            data-bs-placement="right"
                            title="{{#str}}opendrawerblocks, core{{/str}}"
                        >
                            <span class="visually-hidden">{{#str}}opendrawerblocks, core{{/str}}</span>
                            <span class="dir-rtl-hide">{{#pix}}t/blocks_drawer, core{{/pix}}</span>
                            <span class="dir-ltr-hide">{{#pix}}t/blocks_drawer_rtl, core{{/pix}}</span>
                        </button>
                    </div>
                {{/hasblocks}}
            </div>
            {{{ output.full_header }}}
            {{#secondarymoremenu}}
                <div class="secondary-navigation d-print-none">
                    {{> core/moremenu}}
                </div>
            {{/secondarymoremenu}}
            <div id="page-content" class="pb-3 d-print-block">
                <div id="region-main-box">
                    {{#hasregionmainsettingsmenu}}
                    <div id="region-main-settings-menu" class="d-print-none">
                        <div> {{{ regionmainsettingsmenu }}} </div>
                    </div>
                    {{/hasregionmainsettingsmenu}}
                    <div id="region-main">

                        {{#hasregionmainsettingsmenu}}
                            <div class="region_main_settings_menu_proxy"></div>
                        {{/hasregionmainsettingsmenu}}
                        {{{ output.course_content_header }}}
                        {{#headercontent}}
                            {{> core/activity_header }}
                        {{/headercontent}}
                        {{#overflow}}
                            <div class="container-fluid tertiary-navigation">
                                <div class="navitem">
                                    {{> core/url_select}}
                                </div>
                            </div>
                        {{/overflow}}
                        {{{ output.main_content }}}
                        {{{ output.activity_navigation }}}
                        {{{ output.course_content_footer }}}

                    </div>
                </div>
            </div>
        </div>
        {{> theme_boost/footer }}
    </div>
    {{{ output.standard_after_main_region_html }}}
</div>

</body>
</html>
{{#js}}
M.util.js_pending('theme_boost/loader');
require(['theme_boost/loader', 'theme_boost/drawer'], function(Loader, Drawer) {
    Drawer.init();
    M.util.js_complete('theme_boost/loader');
});
{{/js}}
