{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_boost/embedded

    Boost maintenance layout template.

    Context variables required for this template:
    * output - The core renderer for the page
    * hasfakeblocks - true if there are fake blocks on this page
    * fakeblocks - HTML for the fake blocks

    Example context (json):
    {
        "output": {
            "doctype": "<!DOCTYPE html>",
            "htmlattributes": "The attributes that should be added to the <html> tag",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "standard_head_html": "The standard tags that should be included in the <head> tag",
            "body_attributes": "The attributes to use within the body tag",
            "standard_top_of_body_html": "The standard tags that should be output just inside the start of the <body> tag",
            "main_content": "<h1>Headings make html validators happier</h1>",
            "standard_end_of_body_html": "The standard tags that should be output after everything else"
         },
         "hasfakeblocks": true,
         "fakeblocks": "<h2>Fake blocks html goes here</h2>"
    }
}}
{{{ output.doctype }}}
<html {{{ output.htmlattributes }}}>
<head>
    <title>{{{ output.page_title }}}</title>
    <link rel="shortcut icon" href="{{{ output.favicon }}}" />
    {{{ output.standard_head_html }}}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>

<body {{{ output.body_attributes }}}>
{{> core/local/toast/wrapper}}

{{{ output.standard_top_of_body_html }}}
<div id="page" {{#hasfakeblocks}}class="has-fake-blocks"{{/hasfakeblocks}}>
    {{#hasfakeblocks}}
        <div class="embedded-blocks">
            {{{ fakeblocks }}}
        </div>
    {{/hasfakeblocks}}
    <section class="embedded-main">
        {{#headercontent}}
            {{> core/activity_header }}
        {{/headercontent}}
        {{{ output.main_content }}}
    </section>
</div>
{{{ output.standard_end_of_body_html }}}
</body>
</html>
{{#js}}
M.util.js_pending('theme_boost/loader');
require(['theme_boost/loader'], function() {
  M.util.js_complete('theme_boost/loader');
});
{{/js}}
