{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @deprecated since Moodle 4.0
    @template theme_boost/flat_navigation

    Display the flat navigation for the boost theme

    Classes required for JS:
    * none

    Data attributes required for JS:
    * none

    Context variables required for this template:
    * flatnavigation - array of flat_navigation_nodes
      * showdivider - boolean
      * action - string
      * isactive - boolean
      * get_indent - integer
      * is_section - boolean
      * text - HTML

    Example context (json):
    {
        "flatnavigation" : [
            {
                "showdivider": false,
                "action": "#",
                "isactive": true,
                "get_indent": 1,
                "is_section": false,
                "text": "First"
            },{
                "showdivider": true,
                "action": "#",
                "isactive": false,
                "get_indent": 0,
                "is_section": true,
                "text": "Last &amp; Second"
            }
        ]
    }
}}
<nav class="list-group" aria-label="{{firstcollectionlabel}}">
    <ul>
    {{# flatnavigation }}
        {{#showdivider}}
            </ul>
            </nav>
            <nav class="list-group mt-1" aria-label="{{get_collectionlabel}}">
            <ul>
        {{/showdivider}}
        {{#action}}
            <li>
                <a class="list-group-item list-group-item-action {{#isactive}}active{{/isactive}} {{#classes}}{{.}} {{/classes}}" href="{{{action}}}" data-key="{{key}}" data-isexpandable="{{isexpandable}}" data-indent="{{get_indent}}" data-showdivider="{{showdivider}}" data-type="{{type}}" data-nodetype="{{nodetype}}" data-collapse="{{collapse}}" data-forceopen="{{forceopen}}" data-isactive="{{isactive}}" data-hidden="{{hidden}}" data-preceedwithhr="{{preceedwithhr}}" {{#parent.key}}data-parent-key="{{.}}"{{/parent.key}}>
                    <div class="ms-{{get_indent}}">
                        <div class="d-flex">
                            {{#icon.pix}}
                                <span class="flex-shrink-0">
                                    {{#pix}}{{{icon.pix}}}, {{{icon.component}}}, {{{icon.alt}}}{{/pix}}
                                </span>
                            {{/icon.pix}}
                            <span class="flex-grow-1 ms-3 {{#isactive}}fw-bold{{/isactive}}">{{{text}}}</span>
                        </div>
                    </div>
                </a>
            </li>
        {{/action}}
        {{^action}}
            <li>
                <div class="list-group-item {{#classes}}{{.}} {{/classes}}" data-key="{{key}}" data-isexpandable="{{isexpandable}}" data-indent="{{get_indent}}" data-showdivider="{{showdivider}}" data-type="{{type}}" data-nodetype="{{nodetype}}" data-collapse="{{collapse}}" data-forceopen="{{forceopen}}" data-isactive="{{isactive}}" data-hidden="{{hidden}}" data-preceedwithhr="{{preceedwithhr}}" {{#parent.key}}data-parent-key="{{.}}"{{/parent.key}}>
                    <div class="ms-{{get_indent}}">
                        <div class="d-flex">
                            {{#icon.pix}}
                                <span class="flex-shrink-0">
                                    {{#pix}}{{{icon.pix}}}, {{{icon.component}}}, {{{icon.alt}}}{{/pix}}
                                </span>
                            {{/icon.pix}}
                            <span class="flex-grow-1 ms-3">{{{text}}}</span>
                        </div>
                    </div>
                </div>
            </li>
        {{/action}}
    {{/ flatnavigation }}
    </ul>
</nav>
