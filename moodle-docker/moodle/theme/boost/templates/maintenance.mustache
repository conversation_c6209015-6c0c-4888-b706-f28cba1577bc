{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON>ABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_boost/maintenance

    Boost maintenance layout template.

    Context variables required for this template:
    * output - The core renderer for the page

    Example context (json):
    {
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         }
    }
}}
{{{ output.doctype }}}
<html {{{ output.htmlattributes }}}>
<head>
    <title>{{{ output.page_title }}}</title>
    <link rel="shortcut icon" href="{{{ output.favicon }}}" />
    {{{ output.standard_head_html }}}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>

<body {{{ output.body_attributes }}}>
{{> core/local/toast/wrapper}}

<div id="page-wrapper">

    {{{ output.standard_top_of_body_html }}}

    <div id="page" class="container pb-3">

        <div class="row">
            <div class="col-12 py-3 page-header-headings">
                {{{ output.page_heading }}}
            </div>
        </div>

        <div id="page-content" class="row">
            <div id="region-main" class="col-12">
                {{{ output.main_content }}}
            </div>
        </div>

    </div>
    {{> theme_boost/footer }}
</div>

</body>
</html>
{{#js}}
M.util.js_pending('theme_boost/loader');
require(['theme_boost/loader'], function() {
  M.util.js_complete('theme_boost/loader');
});
{{/js}}
