{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_boost/secure

    Boost secure layout template.

    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page

      Example context (json):
    {
        "sitename": "Moodle",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         }
    }
}}
{{> theme_boost/head }}

<body {{{ bodyattributes }}}>
{{> core/local/toast/wrapper}}

<div id="page-wrapper">

    {{{ output.standard_top_of_body_html }}}

    {{>theme_boost/navbar-secure}}

    <div id="page" class="container-fluid">
        {{! Secured full header }}

        <header id="page-header" class="row">
            <div class="col-12 py-3">
                <div class="page-context-header">
                    <div class="page-header-headings">
                        {{{ output.page_heading }}}
                    </div>
                </div>
            </div>
        </header>

        <div id="page-content" class="row">
            <div id="region-main-box" class="col-12">
                <div id="region-main" {{#hasblocks}}class="has-blocks"{{/hasblocks}}>

                    {{{ output.course_content_header }}}
                    {{#headercontent}}
                        {{> core/activity_header }}
                    {{/headercontent}}
                    {{{ output.main_content }}}
                    {{{ output.course_content_footer }}}

                </div>
                {{#hasblocks}}
                <div data-region="blocks-column">
                    {{{ sidepreblocks }}}
                </div>
                {{/hasblocks}}
            </div>
        </div>
    </div>
    <footer id="page-footer" class="py-3">
        <div class="container">
            <div id="course-footer">{{{ output.course_footer }}}</div>
            {{{ output.standard_end_of_body_html }}}
        </div>
    </footer>
</div>


</body>
</html>
{{#js}}
M.util.js_pending('theme_boost/loader');
require(['theme_boost/loader'], function() {
    M.util.js_complete('theme_boost/loader');
});
{{/js}}
