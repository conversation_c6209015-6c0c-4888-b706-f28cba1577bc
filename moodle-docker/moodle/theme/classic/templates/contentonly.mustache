{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_classic/contentonly

    Classic 1 column layout template.

    Context variables required for this template:
    * sitename - The name of the site
    * output - The core renderer for the page
    * bodyattributes - attributes for the body tag as a string of html attributes

    Example context (json):
    {
        "sitename": "Moodle",
        "output": {
            "doctype": "<!DOCTYPE html>",
            "page_title": "Test page",
            "favicon": "favicon.ico",
            "main_content": "<h1>Headings make html validators happier</h1>"
         },
         "bodyattributes":""
    }
}}
{{{ output.doctype }}}
<html {{{ output.htmlattributes }}}>
<head>
    <title>{{{ output.page_title }}}</title>
    <link rel="shortcut icon" href="{{{ output.favicon }}}" />
    {{{ output.standard_head_html }}}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>

<body {{{ bodyattributes }}}>

<div id="page-wrapper">

    {{{ output.standard_top_of_body_html }}}

    <div id="page" class="container-fluid">
        <div id="page-content" class="row">
            <div id="region-main-box" class="col-12">
                <div id="region-main">
                    {{{ output.course_content_header }}}
                    {{#headercontent}}
                        {{> core/activity_header }}
                    {{/headercontent}}
                    {{{ output.main_content }}}
                    {{{ output.activity_navigation }}}
                    {{{ output.course_content_footer }}}
                </div>
            </div>
        </div>
    </div>
</div>
{{{ output.standard_end_of_body_html }}}
</body>
</html>
{{#js}}
M.util.js_pending('theme_boost/loader');
require(['theme_boost/loader'], function() {
    M.util.js_complete('theme_boost/loader');
});
{{/js}}
