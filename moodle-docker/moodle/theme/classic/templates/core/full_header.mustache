{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core/full_header

    This template renders the header.

    Example context (json):
    {
        "contextheader": "context_header_html",
        "hasnavbar": false,
        "navbar": "navbar_if_available",
        "courseheader": "course_header_html",
        "welcomemessage": "welcomemessage"
    }
}}
<header id="page-header" class="row">
    <div class="col-12 pt-3 pb-3">
        <div class="card {{^contextheader}}border-0 bg-transparent{{/contextheader}}">
            <div class="card-body {{^contextheader}}p-2{{/contextheader}}">
                <div class="d-flex align-items-center">
                    <div class="me-auto">
                    {{{contextheader}}}
                    </div>
                    <div class="header-actions-container flex-shrink-0" data-region="header-actions-container">
                        {{#headeractions}}
                            <div class="header-action ms-2">{{{.}}}</div>
                        {{/headeractions}}
                    </div>
                </div>
                <div class="d-flex flex-wrap">
                    {{#hasnavbar}}
                    <div id="page-navbar">
                        {{{navbar}}}
                    </div>
                    {{/hasnavbar}}
                    <div class="ms-auto d-flex">
                        {{{pageheadingbutton}}}
                    </div>
                    <div id="course-header">
                        {{{courseheader}}}
                    </div>
                </div>
            </div>
        </div>
        {{#welcomemessage}}
            <div class="card border-0 mt-3">
                <div class="card-body py-0">
                    {{> core/welcome }}
                </div>
            </div>
        {{/welcomemessage}}
    </div>
</header>
