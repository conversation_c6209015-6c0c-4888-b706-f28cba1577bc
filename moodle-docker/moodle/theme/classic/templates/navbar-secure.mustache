{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_classic/navbar-secure

    This template renders the top navbar.

    Example context (json):
    {
        "output": {
            "should_display_navbar_logo": "true",
            "get_compact_logo_url": "http://example.com/image.png"
        },
        "sitename": "Moodle Site"
    }
}}
<nav class="fixed-top navbar navbar-bootswatch navbar-expand moodle-has-zindex" aria-label="{{#str}}sitemenubar, admin{{/str}}">
    <div class="container-fluid">
        {{# output.should_display_navbar_logo }}
            <span class="logo d-none d-sm-inline">
                <img src="{{output.get_compact_logo_url}}" alt="{{sitename}}">
            </span>
        {{/ output.should_display_navbar_logo }}
        <span class="site-name d-none d-md-inline">{{{ sitename }}}</span>

        {{# output.secure_layout_language_menu }}
        <ul class="navbar-nav d-none d-md-flex">
            <!-- language_menu -->
            {{{ . }}}
        </ul>
        {{/ output.secure_layout_language_menu }}
        {{# output.secure_layout_login_info }}
        <div class="ms-auto">
            {{{ . }}}
        </div>
        {{/ output.secure_layout_login_info }}
    </div>
</nav>
