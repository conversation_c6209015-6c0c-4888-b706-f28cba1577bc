{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template theme_classic/navbar

    This template renders the top navbar.

    Example context (json):
    {
        "output": {
            "should_display_navbar_logo": true,
            "get_compact_logo_url": "http://example.com/image.png",
            "custom_menu": "<li>..</li>",
            "page_heading_menu": "<li>..</li>",
            "search_box": "<div><input type='text'></div>",
            "navbar_plugin_output": "<div class='dropdown'>Messaging</div>",
            "user_menu": "<div class='dropdown'><PERSON>e</div>"
        },
        "config": {
            "wwwroot": "#",
            "homeurl": "/my/"
        },
        "sitename": "Moodle Site"
    }
}}
<nav class="fixed-top navbar navbar-bootswatch navbar-expand moodle-has-zindex">
    <div class="container-fluid">
        <a href="{{{ config.homeurl }}}" class="navbar-brand d-flex align-items-center m-1 p-0 aabtn">
            {{# output.should_display_navbar_logo }}
                <img src="{{output.get_compact_logo_url}}" class="logo me-1" alt="{{sitename}}">
            {{/ output.should_display_navbar_logo }}
            {{^ output.should_display_navbar_logo }}
                <span class="sitename">{{{ sitename }}}</span>
            {{/ output.should_display_navbar_logo }}
        </a>

        <ul class="navbar-nav d-none d-md-flex">
            <!-- custom_menu -->
            {{{ output.custom_menu }}}
            <!-- page_heading_menu -->
            {{{ output.page_heading_menu }}}
        </ul>
        <div id="usernavigation" class="navbar-nav my-1 ms-auto">
            <div class="divider border-start h-50 align-self-center mx-1"></div>
            {{{ output.search_box }}}
            {{{ output.navbar_plugin_output }}}
            <div class="d-flex align-items-stretch usermenu-container" data-region="usermenu">
                {{{ output.user_menu }}}
            </div>
        </div>
    </div>
</nav>
