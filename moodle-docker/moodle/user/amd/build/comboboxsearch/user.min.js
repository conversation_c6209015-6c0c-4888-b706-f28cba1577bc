define("core_user/comboboxsearch/user",["exports","core/comboboxsearch/search_combobox","core/str","core/templates","jquery"],(function(_exports,_search_combobox,_str,_templates,_jquery){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _defineProperty(obj,key,value){return key in obj?Object.defineProperty(obj,key,{value:value,enumerable:!0,configurable:!0,writable:!0}):obj[key]=value,obj}Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.default=void 0,_search_combobox=_interopRequireDefault(_search_combobox),_jquery=_interopRequireDefault(_jquery);class UserSearch extends _search_combobox.default{constructor(){var _document$querySelect,_document$querySelect2;super(),_defineProperty(this,"courseID",void 0),_defineProperty(this,"groupID",void 0),_defineProperty(this,"profilestringmap",null),["click","focus"].forEach((eventType=>{document.addEventListener(eventType,(e=>{this.searchDropdown.classList.contains("show")&&!this.combobox.contains(e.target)&&this.toggleDropdown()}),!0)})),this.component.addEventListener("keydown",this.keyHandler.bind(this)),this.selectors={...this.selectors,courseid:'[data-region="courseid"]',groupid:'[data-region="groupid"]',resetPageButton:'[data-action="resetpage"]'},this.courseID=this.component.querySelector(this.selectors.courseid).dataset.courseid,this.groupID=null===(_document$querySelect=document.querySelector(this.selectors.groupid))||void 0===_document$querySelect||null===(_document$querySelect2=_document$querySelect.dataset)||void 0===_document$querySelect2?void 0:_document$querySelect2.groupid,this.instance=this.component.querySelector(this.selectors.instance).dataset.instance,this.renderDefault()}static init(){return new UserSearch}componentSelector(){return".user-search"}dropdownSelector(){return".usersearchdropdown"}async renderDropdown(){const{html:html,js:js}=await(0,_templates.renderForPromise)("core_user/comboboxsearch/resultset",{users:this.getMatchedResults().slice(0,5),hasresults:this.getMatchedResults().length>0,instance:this.instance,matches:this.getMatchedResults().length,searchterm:this.getSearchTerm(),selectall:this.selectAllResultsLink()});(0,_templates.replaceNodeContents)(this.getHTMLElements().searchDropdown,html,js),this.searchInput.removeAttribute("aria-activedescendant")}async renderDefault(){this.setMatchedResults(await this.filterDataset(await this.getDataset())),this.filterMatchDataset(),await this.renderDropdown()}fetchDataset(){throw new Error("fetchDataset() must be implemented in ".concat(this.constructor.name))}async filterDataset(filterableData){if(this.getPreppedSearchTerm()){const stringMap=await this.getStringMap();return filterableData.filter((user=>Object.keys(user).some((key=>!(""===user[key]||null===user[key]||!stringMap.get(key))&&user[key].toString().toLowerCase().includes(this.getPreppedSearchTerm())))))}return[]}async filterMatchDataset(){const stringMap=await this.getStringMap();this.setMatchedResults(this.getMatchedResults().map((user=>{for(const[key,value]of Object.entries(user)){if(null===value)continue;const valueString=value.toString().toLowerCase(),preppedSearchTerm=this.getPreppedSearchTerm(),searchTerm=this.getSearchTerm(),matchingFieldName=stringMap.get(key);if(matchingFieldName&&valueString.includes(preppedSearchTerm)){user.matchingFieldName=matchingFieldName;const escapedMatchingField=valueString.replace(/</g,"&lt;").replace(preppedSearchTerm.replace(/</g,"&lt;"),'<span class="fw-bold">'.concat(searchTerm.replace(/</g,"&lt;"),"</span>"));user.email?user.matchingField="".concat(escapedMatchingField," (").concat(user.email,")"):user.matchingField=escapedMatchingField;break}}return user})))}changeHandler(e){this.toggleDropdown(),"0"===e.target.value?window.location=this.selectAllResultsLink():window.location=this.selectOneLink(e.target.value)}keyHandler(e){switch(e.key){case"ArrowUp":case"ArrowDown":""!==this.getSearchTerm()&&!this.searchDropdown.classList.contains("show")&&e.target.contains(this.combobox)&&this.renderAndShow();break;case"Enter":case" ":if(e.target.closest(this.selectors.resetPageButton)){e.stopPropagation(),window.location=e.target.closest(this.selectors.resetPageButton).href;break}break;case"Escape":this.toggleDropdown(),this.searchInput.focus({preventScroll:!0})}}toggleDropdown(){arguments.length>0&&void 0!==arguments[0]&&arguments[0]?(this.searchDropdown.classList.add("show"),(0,_jquery.default)(this.searchDropdown).show(),this.getHTMLElements().searchInput.setAttribute("aria-expanded","true"),this.searchInput.focus({preventScroll:!0})):(this.searchDropdown.classList.remove("show"),(0,_jquery.default)(this.searchDropdown).hide(),this.getHTMLElements().searchInput.setAttribute("aria-expanded","false"),this.searchInput.removeAttribute("aria-activedescendant"),this.searchDropdown.querySelectorAll('.active[role="option"]').forEach((option=>{option.classList.remove("active")})))}selectAllResultsLink(){throw new Error("selectAllResultsLink() must be implemented in ".concat(this.constructor.name))}selectOneLink(userID){throw new Error("selectOneLink(".concat(userID,") must be implemented in ").concat(this.constructor.name))}getStringMap(){if(!this.profilestringmap){const requiredStrings=["username","fullname","firstname","lastname","email","city","country","department","institution","idnumber","phone1","phone2"];this.profilestringmap=(0,_str.getStrings)(requiredStrings.map((key=>({key:key})))).then((stringArray=>new Map(requiredStrings.map(((key,index)=>[key,stringArray[index]])))))}return this.profilestringmap}}return _exports.default=UserSearch,_exports.default}));

//# sourceMappingURL=user.min.js.map