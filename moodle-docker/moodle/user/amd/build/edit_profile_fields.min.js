define("core_user/edit_profile_fields",["exports","core_form/modalform","core/str"],(function(_exports,_modalform,_str){var obj;
/**
   * User profile fields editor
   *
   * @module     core_user/edit_profile_fields
   * @copyright  2021 Marina Glancy
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,_modalform=(obj=_modalform)&&obj.__esModule?obj:{default:obj};const Selectors_actions={editCategory:'[data-action="editcategory"]',editField:'[data-action="editfield"]',createField:'[data-action="createfield"]'};_exports.init=()=>{document.addEventListener("click",(function(e){let element=e.target.closest(Selectors_actions.editCategory);if(element){e.preventDefault();const title=element.getAttribute("data-id")?(0,_str.getString)("profileeditcategory","admin",element.getAttribute("data-name")):(0,_str.getString)("profilecreatenewcategory","admin"),form=new _modalform.default({formClass:"core_user\\form\\profile_category_form",args:{id:element.getAttribute("data-id")},modalConfig:{title:title},returnFocus:element});form.addEventListener(form.events.FORM_SUBMITTED,(()=>window.location.reload())),form.show()}if(element=e.target.closest(Selectors_actions.editField),element){e.preventDefault();const form=new _modalform.default({formClass:"core_user\\form\\profile_field_form",args:{id:element.getAttribute("data-id")},modalConfig:{title:(0,_str.getString)("profileeditfield","admin",element.getAttribute("data-name"))},returnFocus:element});form.addEventListener(form.events.FORM_SUBMITTED,(()=>window.location.reload())),form.show()}if(element=e.target.closest(Selectors_actions.createField),element){e.preventDefault();const form=new _modalform.default({formClass:"core_user\\form\\profile_field_form",args:{datatype:element.getAttribute("data-datatype"),categoryid:element.getAttribute("data-categoryid")},modalConfig:{title:(0,_str.getString)("profilecreatenewfield","admin",element.getAttribute("data-datatypename"))},returnFocus:element});form.addEventListener(form.events.FORM_SUBMITTED,(()=>window.location.reload())),form.show()}}))}}));

//# sourceMappingURL=edit_profile_fields.min.js.map