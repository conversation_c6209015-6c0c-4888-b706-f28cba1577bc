{"version": 3, "file": "edit_profile_fields.min.js", "sources": ["../src/edit_profile_fields.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\nimport ModalForm from 'core_form/modalform';\nimport {getString} from 'core/str';\n\n/**\n * User profile fields editor\n *\n * @module     core_user/edit_profile_fields\n * @copyright  2021 Marina Glancy\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nconst Selectors = {\n    actions: {\n        editCategory: '[data-action=\"editcategory\"]',\n        editField: '[data-action=\"editfield\"]',\n        createField: '[data-action=\"createfield\"]',\n    },\n};\n\nexport const init = () => {\n    document.addEventListener('click', function(e) {\n        let element = e.target.closest(Selectors.actions.editCategory);\n        if (element) {\n            e.preventDefault();\n            const title = element.getAttribute('data-id') ?\n                getString('profileeditcategory', 'admin', element.getAttribute('data-name')) :\n                getString('profilecreatenewcategory', 'admin');\n            const form = new ModalForm({\n                formClass: 'core_user\\\\form\\\\profile_category_form',\n                args: {id: element.getAttribute('data-id')},\n                modalConfig: {title},\n                returnFocus: element,\n            });\n            form.addEventListener(form.events.FORM_SUBMITTED, () => window.location.reload());\n            form.show();\n        }\n\n        element = e.target.closest(Selectors.actions.editField);\n        if (element) {\n            e.preventDefault();\n            const form = new ModalForm({\n                formClass: 'core_user\\\\form\\\\profile_field_form',\n                args: {id: element.getAttribute('data-id')},\n                modalConfig: {title: getString('profileeditfield', 'admin', element.getAttribute('data-name'))},\n                returnFocus: element,\n            });\n            form.addEventListener(form.events.FORM_SUBMITTED, () => window.location.reload());\n            form.show();\n        }\n\n        element = e.target.closest(Selectors.actions.createField);\n        if (element) {\n            e.preventDefault();\n            const form = new ModalForm({\n                formClass: 'core_user\\\\form\\\\profile_field_form',\n                args: {datatype: element.getAttribute('data-datatype'), categoryid: element.getAttribute('data-categoryid')},\n                modalConfig: {title: getString('profilecreatenewfield', 'admin', element.getAttribute('data-datatypename'))},\n                returnFocus: element,\n            });\n            form.addEventListener(form.events.FORM_SUBMITTED, () => window.location.reload());\n            form.show();\n        }\n    });\n};\n"], "names": ["Selectors", "editCategory", "edit<PERSON>ield", "createField", "document", "addEventListener", "e", "element", "target", "closest", "preventDefault", "title", "getAttribute", "form", "ModalForm", "formClass", "args", "id", "modalConfig", "returnFocus", "events", "FORM_SUBMITTED", "window", "location", "reload", "show", "datatype", "categoryid"], "mappings": ";;;;;;;sJA0BMA,kBACO,CACLC,aAAc,+BACdC,UAAW,4BACXC,YAAa,6CAID,KAChBC,SAASC,iBAAiB,SAAS,SAASC,OACpCC,QAAUD,EAAEE,OAAOC,QAAQT,kBAAkBC,iBAC7CM,QAAS,CACTD,EAAEI,uBACIC,MAAQJ,QAAQK,aAAa,YAC/B,kBAAU,sBAAuB,QAASL,QAAQK,aAAa,eAC/D,kBAAU,2BAA4B,SACpCC,KAAO,IAAIC,mBAAU,CACvBC,UAAW,yCACXC,KAAM,CAACC,GAAIV,QAAQK,aAAa,YAChCM,YAAa,CAACP,MAAAA,OACdQ,YAAaZ,UAEjBM,KAAKR,iBAAiBQ,KAAKO,OAAOC,gBAAgB,IAAMC,OAAOC,SAASC,WACxEX,KAAKY,UAGTlB,QAAUD,EAAEE,OAAOC,QAAQT,kBAAkBE,WACzCK,QAAS,CACTD,EAAEI,uBACIG,KAAO,IAAIC,mBAAU,CACvBC,UAAW,sCACXC,KAAM,CAACC,GAAIV,QAAQK,aAAa,YAChCM,YAAa,CAACP,OAAO,kBAAU,mBAAoB,QAASJ,QAAQK,aAAa,eACjFO,YAAaZ,UAEjBM,KAAKR,iBAAiBQ,KAAKO,OAAOC,gBAAgB,IAAMC,OAAOC,SAASC,WACxEX,KAAKY,UAGTlB,QAAUD,EAAEE,OAAOC,QAAQT,kBAAkBG,aACzCI,QAAS,CACTD,EAAEI,uBACIG,KAAO,IAAIC,mBAAU,CACvBC,UAAW,sCACXC,KAAM,CAACU,SAAUnB,QAAQK,aAAa,iBAAkBe,WAAYpB,QAAQK,aAAa,oBACzFM,YAAa,CAACP,OAAO,kBAAU,wBAAyB,QAASJ,QAAQK,aAAa,uBACtFO,YAAaZ,UAEjBM,KAAKR,iBAAiBQ,KAAKO,OAAOC,gBAAgB,IAAMC,OAAOC,SAASC,WACxEX,KAAKY"}