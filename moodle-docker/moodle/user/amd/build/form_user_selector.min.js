define("core_user/form_user_selector",["exports","core/ajax","core/templates","core/str"],(function(_exports,_ajax,_templates,_str){var obj;
/**
   * Provides the required functionality for an autocomplete element to select a user.
   *
   * @module      core_user/form_user_selector
   * @copyright   2020 <PERSON> <<EMAIL>>
   * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.processResults=function(selector,results){return Array.isArray(results)?results.map((result=>({value:result.id,label:result.label}))):results},_exports.transport=async function(selector,query,callback,failure){const request={methodname:"core_user_search_identity",args:{query:query}};try{const response=await _ajax.default.call([request])[0];if(response.overflow){const msg=await(0,_str.getString)("toomanyuserstoshow","core",">"+response.maxusersperpage);callback(msg)}else{let labels=[];response.list.forEach((user=>{labels.push((0,_templates.render)("core_user/form_user_selector_suggestion",user))})),labels=await Promise.all(labels),response.list.forEach(((user,index)=>{user.label=labels[index]})),callback(response.list)}}catch(e){failure(e)}},_ajax=(obj=_ajax)&&obj.__esModule?obj:{default:obj}}));

//# sourceMappingURL=form_user_selector.min.js.map