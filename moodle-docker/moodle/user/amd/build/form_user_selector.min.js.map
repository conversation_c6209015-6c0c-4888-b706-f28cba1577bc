{"version": 3, "file": "form_user_selector.min.js", "sources": ["../src/form_user_selector.js"], "sourcesContent": ["// This file is part of Moodle - https://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Provides the required functionality for an autocomplete element to select a user.\n *\n * @module      core_user/form_user_selector\n * @copyright   2020 <PERSON> <<EMAIL>>\n * @license     http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Ajax from 'core/ajax';\nimport {render as renderTemplate} from 'core/templates';\nimport {getString} from 'core/str';\n\n/**\n * Load the list of users matching the query and render the selector labels for them.\n *\n * @param {String} selector The selector of the auto complete element.\n * @param {String} query The query string.\n * @param {Function} callback A callback function receiving an array of results.\n * @param {Function} failure A function to call in case of failure, receiving the error message.\n */\nexport async function transport(selector, query, callback, failure) {\n\n    const request = {\n        methodname: 'core_user_search_identity',\n        args: {\n            query: query\n        }\n    };\n\n    try {\n        const response = await Ajax.call([request])[0];\n\n        if (response.overflow) {\n            const msg = await getString('toomanyuserstoshow', 'core', '>' + response.maxusersperpage);\n            callback(msg);\n\n        } else {\n            let labels = [];\n            response.list.forEach(user => {\n                labels.push(renderTemplate('core_user/form_user_selector_suggestion', user));\n            });\n            labels = await Promise.all(labels);\n\n            response.list.forEach((user, index) => {\n                user.label = labels[index];\n            });\n\n            callback(response.list);\n        }\n\n    } catch (e) {\n        failure(e);\n    }\n}\n\n/**\n * Process the results for auto complete elements.\n *\n * @param {String} selector The selector of the auto complete element.\n * @param {Array} results An array or results returned by {@see transport()}.\n * @return {Array} New array of the selector options.\n */\nexport function processResults(selector, results) {\n\n    if (!Array.isArray(results)) {\n        return results;\n\n    } else {\n        return results.map(result => ({value: result.id, label: result.label}));\n    }\n}\n"], "names": ["selector", "results", "Array", "isArray", "map", "result", "value", "id", "label", "query", "callback", "failure", "request", "methodname", "args", "response", "Ajax", "call", "overflow", "msg", "maxusersperpage", "labels", "list", "for<PERSON>ach", "user", "push", "Promise", "all", "index", "e"], "mappings": ";;;;;;;8FA6E+BA,SAAUC,gBAEhCC,MAAMC,QAAQF,SAIRA,QAAQG,KAAIC,UAAYC,MAAOD,OAAOE,GAAIC,MAAOH,OAAOG,UAHxDP,2CA7CiBD,SAAUS,MAAOC,SAAUC,eAEjDC,QAAU,CACZC,WAAY,4BACZC,KAAM,CACFL,MAAOA,kBAKLM,eAAiBC,cAAKC,KAAK,CAACL,UAAU,MAExCG,SAASG,SAAU,OACbC,UAAY,kBAAU,qBAAsB,OAAQ,IAAMJ,SAASK,iBACzEV,SAASS,SAEN,KACCE,OAAS,GACbN,SAASO,KAAKC,SAAQC,OAClBH,OAAOI,MAAK,qBAAe,0CAA2CD,UAE1EH,aAAeK,QAAQC,IAAIN,QAE3BN,SAASO,KAAKC,SAAQ,CAACC,KAAMI,SACzBJ,KAAKhB,MAAQa,OAAOO,UAGxBlB,SAASK,SAASO,OAGxB,MAAOO,GACLlB,QAAQkB"}