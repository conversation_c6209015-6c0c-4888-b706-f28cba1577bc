{"version": 3, "file": "bulkactions.min.js", "sources": ["../../../src/local/participants/bulkactions.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Bulk actions for lists of participants.\n *\n * @module     core_user/local/participants/bulkactions\n * @copyright  2020 Andrew Nicols <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport * as Repository from 'core_user/repository';\nimport * as Str from 'core/str';\nimport ModalEvents from 'core/modal_events';\nimport SaveCancelModal from 'core/modal_save_cancel';\nimport Notification from 'core/notification';\nimport Templates from 'core/templates';\nimport {add as notifyUser} from 'core/toast';\n\n/**\n * Show the add note popup\n *\n * @param {Number} courseid\n * @param {Number[]} users\n * @param {String[]} noteStateNames\n * @param {HTMLElement} stateHelpIcon\n * @return {Promise}\n */\nexport const showAddNote = (courseid, users, noteStateNames, stateHelpIcon) => {\n    if (!users.length) {\n        // No users were selected.\n        return Promise.resolve();\n    }\n\n    const states = [];\n    for (let key in noteStateNames) {\n        switch (key) {\n            case 'draft':\n                states.push({value: 'personal', label: noteStateNames[key]});\n                break;\n            case 'public':\n                states.push({value: 'course', label: noteStateNames[key], selected: 1});\n                break;\n            case 'site':\n                states.push({value: key, label: noteStateNames[key]});\n                break;\n        }\n    }\n\n    const context = {\n        stateNames: states,\n        stateHelpIcon: stateHelpIcon.innerHTML,\n    };\n\n    let titlePromise = null;\n    if (users.length === 1) {\n        titlePromise = Str.get_string('addbulknotesingle', 'core_notes');\n    } else {\n        titlePromise = Str.get_string('addbulknote', 'core_notes', users.length);\n    }\n\n    return SaveCancelModal.create({\n        body: Templates.render('core_user/add_bulk_note', context),\n        title: titlePromise,\n        buttons: {\n            save: titlePromise,\n        },\n        removeOnClose: true,\n        show: true,\n    })\n    .then(modal => {\n        modal.getRoot().on(ModalEvents.save, () => submitAddNote(courseid, users, modal));\n        return modal;\n    });\n};\n\n/**\n * Add a note to this list of users.\n *\n * @param {Number} courseid\n * @param {Number[]} users\n * @param {Modal} modal\n * @return {Promise}\n */\nconst submitAddNote = (courseid, users, modal) => {\n    const text = modal.getRoot().find('form textarea').val();\n    const publishstate = modal.getRoot().find('form select').val();\n\n    const notes = users.map(userid => {\n        return {\n            userid,\n            text,\n            courseid,\n            publishstate,\n        };\n    });\n\n    return Repository.createNotesForUsers(notes)\n    .then(noteIds => {\n        if (noteIds.length === 1) {\n            return Str.get_string('addbulknotedonesingle', 'core_notes');\n        } else {\n            return Str.get_string('addbulknotedone', 'core_notes', noteIds.length);\n        }\n    })\n    .then(msg => notifyUser(msg))\n    .catch(Notification.exception);\n};\n\n/**\n * Show the send message popup.\n *\n * @param {Number[]} users\n * @return {Promise}\n */\nexport const showSendMessage = users => {\n    if (!users.length) {\n        // Nothing to do.\n        return Promise.resolve();\n    }\n\n    let titlePromise;\n    if (users.length === 1) {\n        titlePromise = Str.get_string('sendbulkmessagesingle', 'core_message');\n    } else {\n        titlePromise = Str.get_string('sendbulkmessage', 'core_message', users.length);\n    }\n\n    return SaveCancelModal.create({\n        body: Templates.render('core_user/send_bulk_message', {}),\n        title: titlePromise,\n        buttons: {\n            save: titlePromise,\n        },\n        removeOnClose: true,\n        show: true,\n    })\n    .then(modal => {\n        modal.getRoot().on(ModalEvents.save, (e) => {\n            const text = modal.getRoot().find('form textarea').val();\n            if (text.trim() === '') {\n                modal.getRoot().find('[data-role=\"messagetextrequired\"]').removeAttr('hidden');\n                e.preventDefault();\n                return;\n            }\n\n            submitSendMessage(modal, users, text);\n        });\n\n        return modal;\n    });\n};\n\n/**\n * Send a message to these users.\n *\n * @param {Modal} modal\n * @param {Number[]} users\n * @param {String} text\n * @return {Promise}\n */\nconst submitSendMessage = (modal, users, text) => {\n    const messages = users.map(touserid => {\n        return {\n            touserid,\n            text,\n        };\n    });\n\n    return Repository.sendMessagesToUsers(messages)\n    .then(messageIds => {\n        if (messageIds.length == 1) {\n            return Str.get_string('sendbulkmessagesentsingle', 'core_message');\n        } else {\n            return Str.get_string('sendbulkmessagesent', 'core_message', messageIds.length);\n        }\n    })\n    .then(msg => notifyUser(msg))\n    .catch(Notification.exception);\n};\n"], "names": ["courseid", "users", "noteStateNames", "stateHelpIcon", "length", "Promise", "resolve", "states", "key", "push", "value", "label", "selected", "context", "stateNames", "innerHTML", "titlePromise", "Str", "get_string", "SaveCancelModal", "create", "body", "Templates", "render", "title", "buttons", "save", "removeOnClose", "show", "then", "modal", "getRoot", "on", "ModalEvents", "submitAddNote", "text", "find", "val", "publishstate", "notes", "map", "userid", "Repository", "createNotesForUsers", "noteIds", "msg", "catch", "Notification", "exception", "e", "trim", "removeAttr", "preventDefault", "submitSendMessage", "messages", "to<PERSON><PERSON>", "sendMessagesToUsers", "messageIds"], "mappings": ";;;;;;;2aAwC2B,CAACA,SAAUC,MAAOC,eAAgBC,qBACpDF,MAAMG,cAEAC,QAAQC,gBAGbC,OAAS,OACV,IAAIC,OAAON,sBACJM,SACC,QACDD,OAAOE,KAAK,CAACC,MAAO,WAAYC,MAAOT,eAAeM,iBAErD,SACDD,OAAOE,KAAK,CAACC,MAAO,SAAUC,MAAOT,eAAeM,KAAMI,SAAU,cAEnE,OACDL,OAAOE,KAAK,CAACC,MAAOF,IAAKG,MAAOT,eAAeM,aAKrDK,QAAU,CACZC,WAAYP,OACZJ,cAAeA,cAAcY,eAG7BC,aAAe,YAEfA,aADiB,IAAjBf,MAAMG,OACSa,IAAIC,WAAW,oBAAqB,cAEpCD,IAAIC,WAAW,cAAe,aAAcjB,MAAMG,QAG9De,2BAAgBC,OAAO,CAC1BC,KAAMC,mBAAUC,OAAO,0BAA2BV,SAClDW,MAAOR,aACPS,QAAS,CACLC,KAAMV,cAEVW,eAAe,EACfC,MAAM,IAETC,MAAKC,QACFA,MAAMC,UAAUC,GAAGC,sBAAYP,MAAM,IAAMQ,cAAclC,SAAUC,MAAO6B,SACnEA,gBAYTI,cAAgB,CAAClC,SAAUC,MAAO6B,eAC9BK,KAAOL,MAAMC,UAAUK,KAAK,iBAAiBC,MAC7CC,aAAeR,MAAMC,UAAUK,KAAK,eAAeC,MAEnDE,MAAQtC,MAAMuC,KAAIC,SACb,CACHA,OAAAA,OACAN,KAAAA,KACAnC,SAAAA,SACAsC,aAAAA,wBAIDI,WAAWC,oBAAoBJ,OACrCV,MAAKe,SACqB,IAAnBA,QAAQxC,OACDa,IAAIC,WAAW,wBAAyB,cAExCD,IAAIC,WAAW,kBAAmB,aAAc0B,QAAQxC,UAGtEyB,MAAKgB,MAAO,cAAWA,OACvBC,MAAMC,sBAAaC,qCASO/C,YACtBA,MAAMG,cAEAC,QAAQC,cAGfU,oBAEAA,aADiB,IAAjBf,MAAMG,OACSa,IAAIC,WAAW,wBAAyB,gBAExCD,IAAIC,WAAW,kBAAmB,eAAgBjB,MAAMG,QAGpEe,2BAAgBC,OAAO,CAC1BC,KAAMC,mBAAUC,OAAO,8BAA+B,IACtDC,MAAOR,aACPS,QAAS,CACLC,KAAMV,cAEVW,eAAe,EACfC,MAAM,IAETC,MAAKC,QACFA,MAAMC,UAAUC,GAAGC,sBAAYP,MAAOuB,UAC5Bd,KAAOL,MAAMC,UAAUK,KAAK,iBAAiBC,SAC/B,KAAhBF,KAAKe,cACLpB,MAAMC,UAAUK,KAAK,qCAAqCe,WAAW,eACrEF,EAAEG,iBAINC,kBAAkBvB,MAAO7B,MAAOkC,SAG7BL,gBAYTuB,kBAAoB,CAACvB,MAAO7B,MAAOkC,cAC/BmB,SAAWrD,MAAMuC,KAAIe,WAChB,CACHA,SAAAA,SACApB,KAAAA,gBAIDO,WAAWc,oBAAoBF,UACrCzB,MAAK4B,YACuB,GAArBA,WAAWrD,OACJa,IAAIC,WAAW,4BAA6B,gBAE5CD,IAAIC,WAAW,sBAAuB,eAAgBuC,WAAWrD,UAG/EyB,MAAKgB,MAAO,cAAWA,OACvBC,MAAMC,sBAAaC"}