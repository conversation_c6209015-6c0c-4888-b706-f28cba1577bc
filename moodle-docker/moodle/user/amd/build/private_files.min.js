define("core_user/private_files",["exports","core_form/dynamicform","core_form/modalform","core/str","core/toast"],(function(_exports,_dynamicform,_modalform,_str,_toast){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}
/**
   * Module to handle AJAX interactions with user private files
   *
   * @module     core_user/private_files
   * @copyright  2020 Marina Glancy
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.initModal=_exports.initDynamicForm=void 0,_dynamicform=_interopRequireDefault(_dynamicform),_modalform=_interopRequireDefault(_modalform);_exports.initDynamicForm=(containerSelector,formClass)=>{const form=new _dynamicform.default(document.querySelector(containerSelector),formClass);form.addEventListener(form.events.FORM_SUBMITTED,(()=>{form.load(),(0,_str.getString)("changessaved").then(_toast.add).catch(null)})),form.addEventListener(form.events.CANCEL_BUTTON_PRESSED,(()=>window.location.reload()))};_exports.initModal=(elementSelector,formClass)=>{document.querySelector(elementSelector).addEventListener("click",(function(e){e.preventDefault();const form=new _modalform.default({formClass:formClass,args:{nosubmit:!0},modalConfig:{title:(0,_str.getString)("privatefilesmanage")},returnFocus:e.target});form.addEventListener(form.events.FORM_SUBMITTED,(()=>window.location.reload())),form.show()}))}}));

//# sourceMappingURL=private_files.min.js.map