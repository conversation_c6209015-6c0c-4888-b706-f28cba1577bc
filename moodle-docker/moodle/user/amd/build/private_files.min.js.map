{"version": 3, "file": "private_files.min.js", "sources": ["../src/private_files.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// <PERSON><PERSON><PERSON> is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle AJAX interactions with user private files\n *\n * @module     core_user/private_files\n * @copyright  2020 Marina Glancy\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\nimport DynamicForm from 'core_form/dynamicform';\nimport ModalForm from 'core_form/modalform';\nimport {getString} from 'core/str';\nimport {add as addToast} from 'core/toast';\n\n/**\n * Initialize private files form as AJAX form\n *\n * @param {String} containerSelector\n * @param {String} formClass\n */\nexport const initDynamicForm = (containerSelector, formClass) => {\n    const form = new DynamicForm(document.querySelector(containerSelector), formClass);\n\n    // When form is saved, refresh it to remove validation errors, if any:\n    form.addEventListener(form.events.FORM_SUBMITTED, () => {\n        form.load();\n        getString('changessaved')\n        .then(addToast)\n        .catch(null);\n    });\n\n    // Reload the page on cancel.\n    form.addEventListener(form.events.CANCEL_BUTTON_PRESSED, () => window.location.reload());\n};\n\n/**\n * Initialize private files form as Modal form\n *\n * @param {String} elementSelector\n * @param {String} formClass\n */\nexport const initModal = (elementSelector, formClass) => {\n    document.querySelector(elementSelector).addEventListener('click', function(e) {\n        e.preventDefault();\n        const form = new ModalForm({\n            formClass,\n            args: {nosubmit: true},\n            modalConfig: {title: getString('privatefilesmanage')},\n            returnFocus: e.target,\n        });\n        form.addEventListener(form.events.FORM_SUBMITTED, () => window.location.reload());\n        form.show();\n    });\n};\n"], "names": ["containerSelector", "formClass", "form", "DynamicForm", "document", "querySelector", "addEventListener", "events", "FORM_SUBMITTED", "load", "then", "addToast", "catch", "CANCEL_BUTTON_PRESSED", "window", "location", "reload", "elementSelector", "e", "preventDefault", "ModalForm", "args", "nosubmit", "modalConfig", "title", "returnFocus", "target", "show"], "mappings": ";;;;;;;yOAiC+B,CAACA,kBAAmBC,mBACzCC,KAAO,IAAIC,qBAAYC,SAASC,cAAcL,mBAAoBC,WAGxEC,KAAKI,iBAAiBJ,KAAKK,OAAOC,gBAAgB,KAC9CN,KAAKO,0BACK,gBACTC,KAAKC,YACLC,MAAM,SAIXV,KAAKI,iBAAiBJ,KAAKK,OAAOM,uBAAuB,IAAMC,OAAOC,SAASC,+BAS1D,CAACC,gBAAiBhB,aACvCG,SAASC,cAAcY,iBAAiBX,iBAAiB,SAAS,SAASY,GACvEA,EAAEC,uBACIjB,KAAO,IAAIkB,mBAAU,CACvBnB,UAAAA,UACAoB,KAAM,CAACC,UAAU,GACjBC,YAAa,CAACC,OAAO,kBAAU,uBAC/BC,YAAaP,EAAEQ,SAEnBxB,KAAKI,iBAAiBJ,KAAKK,OAAOC,gBAAgB,IAAMM,OAAOC,SAASC,WACxEd,KAAKyB"}