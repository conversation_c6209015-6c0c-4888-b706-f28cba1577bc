{"version": 3, "file": "repository.min.js", "sources": ["../src/repository.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * Module to handle AJAX interactions.\n *\n * @module     core_user/repository\n * @copyright  2020 Andrew <PERSON> <<EMAIL>>\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport Config from 'core/config';\nimport {call as fetchMany} from 'core/ajax';\nimport Fetch from 'core/fetch';\n\nconst checkUserId = (userid) => {\n    if (Number(userid) === 0) {\n        return;\n    }\n    if (Number(userid) === Config.userId) {\n        return;\n    }\n    throw new Error(\n        `Invalid user ID: ${userid}. It is only possible to manage preferences for the current user.`,\n    );\n};\n\n/**\n * Turn the response object into a Proxy object that will log a warning if the saved property is accessed.\n *\n * @param {Object} response\n * @param {Object} preferences The preferences that might be in the response\n * @return {Promise<Proxy>}\n */\nconst addLegacySavedProperty = (response, preferences) => {\n    const debugLogger = {\n        get(target, prop, receiver) {\n            if (prop === 'then') {\n                // To proxy a Promise we have to return null when the then key is requested.\n                return null;\n            }\n            if (prop === 'saved') {\n                window.console.warn(\n                    'The saved property is deprecated. Please use the response object directly.',\n                );\n\n                return preferences\n                    .filter((preference) => target.hasOwnProperty(preference.name))\n                    .map((preference) => ({\n                        name: preference.name,\n                        userid: Config.userid,\n                    }));\n            }\n            return Reflect.get(target, prop, receiver);\n        },\n    };\n\n    return Promise.resolve(new Proxy(response, debugLogger));\n};\n\n/**\n * Get single user preference\n *\n * @param {String} name Name of the preference\n * @param {Number} userid User ID (defaults to current user)\n * @return {Promise}\n */\nexport const getUserPreference = (name, userid = 0) => getUserPreferences(name, userid)\n    .then((response) => response[name]);\n\n/**\n * Get multiple user preferences\n *\n * @param {String|null} name Name of the preference (omit if you want to retrieve all)\n * @param {Number} userid User ID (defaults to current user)\n * @return {Promise<object<string, string>>}\n */\nexport const getUserPreferences = (name = null, userid = 0) => {\n    checkUserId(userid);\n    const endpoint = ['current', 'preferences'];\n\n    if (name) {\n        endpoint.push(name);\n    }\n\n    return Fetch.performGet('core_user', endpoint.join('/')).then((response) => response.json());\n};\n\n/**\n * Set single user preference\n *\n * @param {String} name Name of the preference\n * @param {String|null} value Value of the preference (omit if you want to remove the current value)\n * @param {Number} userid User ID (defaults to current user)\n * @return {Promise}\n */\nexport const setUserPreference = (name, value = null, userid = 0) => {\n    checkUserId(userid);\n    return Fetch.performPost(\n        'core_user',\n        `current/preferences/${name}`,\n        {\n            body: {value},\n        },\n    )\n    // Return the result of the fetch call, and also add in the legacy saved property.\n    .then((response) => response.json())\n    .then((response) => addLegacySavedProperty(response, [{name}]));\n};\n\n/**\n * Set multiple user preferences\n *\n * @param {Object[]} preferences Array of preferences containing name/value/userid attributes\n * @return {Promise}\n */\nexport const setUserPreferences = (preferences) => {\n    preferences.forEach((preference) => checkUserId(preference.userid));\n    return Fetch.performPost(\n        'core_user',\n        'current/preferences',\n        {\n            body: {\n                preferences: Object.fromEntries (preferences.map((preference) => ([preference.name, preference.value]))),\n            },\n        },\n    )\n    // Return the result of the fetch call, and also add in the legacy saved property.\n    .then((response) => response.json())\n    .then((response) => addLegacySavedProperty(response, preferences));\n};\n\n/**\n * Unenrol the user with the specified user enrolmentid ID.\n *\n * @param {Number} userEnrolmentId\n * @return {Promise}\n */\nexport const unenrolUser = userEnrolmentId => {\n    return fetchMany([{\n        methodname: 'core_enrol_unenrol_user_enrolment',\n        args: {\n            ueid: userEnrolmentId,\n        },\n    }])[0];\n};\n\n/**\n * Submit the user enrolment form with the specified form data.\n *\n * @param {String} formdata\n * @return {Promise}\n */\nexport const submitUserEnrolmentForm = formdata => {\n    return fetchMany([{\n        methodname: 'core_enrol_submit_user_enrolment_form',\n        args: {\n            formdata,\n        },\n    }])[0];\n};\n\nexport const createNotesForUsers = notes => {\n    return fetchMany([{\n        methodname: 'core_notes_create_notes',\n        args: {\n            notes\n        }\n    }])[0];\n};\n\nexport const sendMessagesToUsers = messages => {\n    return fetchMany([{\n        methodname: 'core_message_send_instant_messages',\n        args: {messages}\n    }])[0];\n};\n"], "names": ["checkUserId", "userid", "Number", "Config", "userId", "Error", "addLegacySavedProperty", "response", "preferences", "debugLogger", "get", "target", "prop", "receiver", "window", "console", "warn", "filter", "preference", "hasOwnProperty", "name", "map", "Reflect", "Promise", "resolve", "Proxy", "getUserPreferences", "then", "endpoint", "push", "<PERSON>tch", "performGet", "join", "json", "value", "performPost", "body", "for<PERSON>ach", "Object", "fromEntries", "userEnrolmentId", "methodname", "args", "<PERSON><PERSON>d", "formdata", "notes", "messages"], "mappings": ";;;;;;;sXA2BMA,YAAeC,YACM,IAAnBC,OAAOD,SAGPC,OAAOD,UAAYE,gBAAOC,aAGxB,IAAIC,iCACcJ,8EAWtBK,uBAAyB,CAACC,SAAUC,qBAChCC,YAAc,CAChBC,IAAG,CAACC,OAAQC,KAAMC,WACD,SAATD,KAEO,KAEE,UAATA,MACAE,OAAOC,QAAQC,KACX,8EAGGR,YACFS,QAAQC,YAAeP,OAAOQ,eAAeD,WAAWE,QACxDC,KAAKH,cACFE,KAAMF,WAAWE,KACjBnB,OAAQE,gBAAOF,YAGpBqB,QAAQZ,IAAIC,OAAQC,KAAMC,kBAIlCU,QAAQC,QAAQ,IAAIC,MAAMlB,SAAUE,0CAUd,SAACW,UAAMnB,8DAAS,SAAMyB,mBAAmBN,KAAMnB,QAC3E0B,MAAMpB,UAAaA,SAASa,eASpBM,mBAAqB,eAACN,4DAAO,KAAMnB,8DAAS,EACrDD,YAAYC,cACN2B,SAAW,CAAC,UAAW,sBAEzBR,MACAQ,SAASC,KAAKT,MAGXU,eAAMC,WAAW,YAAaH,SAASI,KAAK,MAAML,MAAMpB,UAAaA,SAAS0B,oFAWxD,SAACb,UAAMc,6DAAQ,KAAMjC,8DAAS,SAC3DD,YAAYC,QACL6B,eAAMK,YACT,0CACuBf,MACvB,CACIgB,KAAM,CAACF,MAAAA,SAIdP,MAAMpB,UAAaA,SAAS0B,SAC5BN,MAAMpB,UAAaD,uBAAuBC,SAAU,CAAC,CAACa,KAAAA,uCASxBZ,cAC/BA,YAAY6B,SAASnB,YAAelB,YAAYkB,WAAWjB,UACpD6B,eAAMK,YACT,YACA,sBACA,CACIC,KAAM,CACF5B,YAAa8B,OAAOC,YAAa/B,YAAYa,KAAKH,YAAgB,CAACA,WAAWE,KAAMF,WAAWgB,aAK1GP,MAAMpB,UAAaA,SAAS0B,SAC5BN,MAAMpB,UAAaD,uBAAuBC,SAAUC,qCAS9BgC,kBAChB,cAAU,CAAC,CACdC,WAAY,oCACZC,KAAM,CACFC,KAAMH,oBAEV,oCAS+BI,WAC5B,cAAU,CAAC,CACdH,WAAY,wCACZC,KAAM,CACFE,SAAAA,aAEJ,gCAG2BC,QACxB,cAAU,CAAC,CACdJ,WAAY,0BACZC,KAAM,CACFG,MAAAA,UAEJ,gCAG2BC,WACxB,cAAU,CAAC,CACdL,WAAY,qCACZC,KAAM,CAACI,SAAAA,aACP"}