define("core_user/status_field",["exports","core_table/dynamic","./repository","core/str","core_table/local/dynamic/selectors","core/fragment","core/modal_events","core/notification","core/templates","core/toast","core/modal_save_cancel","core/modal_cancel"],(function(_exports,DynamicTable,Repository,Str,_selectors,_fragment,_modal_events,_notification,_templates,_toast,_modal_save_cancel,_modal_cancel){function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj}}function _getRequireWildcardCache(nodeInterop){if("function"!=typeof WeakMap)return null;var cacheBabelInterop=new WeakMap,cacheNodeInterop=new WeakMap;return(_getRequireWildcardCache=function(nodeInterop){return nodeInterop?cacheNodeInterop:cacheBabelInterop})(nodeInterop)}function _interopRequireWildcard(obj,nodeInterop){if(!nodeInterop&&obj&&obj.__esModule)return obj;if(null===obj||"object"!=typeof obj&&"function"!=typeof obj)return{default:obj};var cache=_getRequireWildcardCache(nodeInterop);if(cache&&cache.has(obj))return cache.get(obj);var newObj={},hasPropertyDescriptor=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var key in obj)if("default"!==key&&Object.prototype.hasOwnProperty.call(obj,key)){var desc=hasPropertyDescriptor?Object.getOwnPropertyDescriptor(obj,key):null;desc&&(desc.get||desc.set)?Object.defineProperty(newObj,key,desc):newObj[key]=obj[key]}return newObj.default=obj,cache&&cache.set(obj,newObj),newObj}
/**
   * AMD module for the user enrolment status field in the course participants page.
   *
   * @module     core_user/status_field
   * @copyright  2017 Jun Pataleta
   * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
   */Object.defineProperty(_exports,"__esModule",{value:!0}),_exports.init=void 0,DynamicTable=_interopRequireWildcard(DynamicTable),Repository=_interopRequireWildcard(Repository),Str=_interopRequireWildcard(Str),_selectors=_interopRequireDefault(_selectors),_fragment=_interopRequireDefault(_fragment),_modal_events=_interopRequireDefault(_modal_events),_notification=_interopRequireDefault(_notification),_templates=_interopRequireDefault(_templates),_modal_save_cancel=_interopRequireDefault(_modal_save_cancel),_modal_cancel=_interopRequireDefault(_modal_cancel);const Selectors_editEnrolment='[data-action="editenrolment"]',Selectors_showDetails='[data-action="showdetails"]',Selectors_unenrol='[data-action="unenrol"]',Selectors_statusElement="[data-status]",getDynamicTableFromLink=link=>link.closest(_selectors.default.main.region),getStatusContainer=link=>link.closest(Selectors_statusElement),getUserEnrolmentIdFromLink=link=>link.getAttribute("rel"),showEditDialogue=(link,getBody)=>{const container=getStatusContainer(link),userEnrolmentId=getUserEnrolmentIdFromLink(link);_modal_save_cancel.default.create({large:!0,title:Str.get_string("edituserenrolment","enrol",container.dataset.fullname),body:getBody(userEnrolmentId)}).then((modal=>(modal.getRoot().on(_modal_events.default.save,(e=>{e.preventDefault(),submitEditFormAjax(link,getBody,modal,userEnrolmentId,container.dataset)})),modal.getRoot().on(_modal_events.default.hidden,(()=>{modal.destroy()})),modal.show(),modal))).catch(_notification.default.exception)},showUnenrolConfirmation=link=>{const container=getStatusContainer(link),userEnrolmentId=getUserEnrolmentIdFromLink(link);_modal_save_cancel.default.create().then((modal=>{modal.getRoot().on(_modal_events.default.save,(e=>{e.preventDefault(),submitUnenrolFormAjax(link,modal,{ueid:userEnrolmentId},container.dataset)})),modal.getRoot().on(_modal_events.default.hidden,(()=>{modal.destroy()})),modal.show();const stringData=[{key:"unenrol",component:"enrol"},{key:"unenrolconfirm",component:"enrol",param:{user:container.dataset.fullname,course:container.dataset.coursename,enrolinstancename:container.dataset.enrolinstancename}}];return Promise.all([Str.get_strings(stringData),modal])})).then((_ref=>{let[strings,modal]=_ref;return modal.setTitle(strings[0]),modal.setSaveButtonText(strings[0]),modal.setBody(strings[1]),modal})).catch(_notification.default.exception)},showStatusDetails=link=>{const container=getStatusContainer(link),context={editenrollink:"",statusclass:container.querySelector("span.badge").getAttribute("class"),...container.dataset},editEnrolLink=container.querySelector(Selectors_editEnrolment);editEnrolLink&&(context.editenrollink=editEnrolLink.outerHTML),_modal_cancel.default.create({large:!0,title:Str.get_string("enroldetails","enrol"),body:_templates.default.render("core_user/status_details",context)}).then((modal=>(editEnrolLink&&modal.getRoot().on("click",Selectors_editEnrolment,(e=>{e.preventDefault(),modal.hide(),editEnrolLink.click()})),modal.show(),modal.getRoot().on(_modal_events.default.hidden,(()=>modal.destroy())),modal))).catch(_notification.default.exception)},submitEditFormAjax=(clickedLink,getBody,modal,userEnrolmentId,userData)=>{const form=modal.getRoot().find("form");Repository.submitUserEnrolmentForm(form.serialize()).then((data=>{if(!data.result)throw data.result;return modal.hide(),modal.destroy(),data})).then((()=>(DynamicTable.refreshTableContent(getDynamicTableFromLink(clickedLink)).catch(_notification.default.exception),Str.get_string("enrolmentupdatedforuser","core_enrol",userData)))).then((notificationString=>{(0,_toast.add)(notificationString)})).catch((()=>(modal.setBody(getBody(userEnrolmentId,JSON.stringify(form.serialize()))),modal)))},submitUnenrolFormAjax=(clickedLink,modal,args,userData)=>{Repository.unenrolUser(args.ueid).then((data=>data.result?(modal.hide(),modal.destroy(),data):(_notification.default.alert(data.errors[0].key,data.errors[0].message),data))).then((()=>(DynamicTable.refreshTableContent(getDynamicTableFromLink(clickedLink)).catch(_notification.default.exception),Str.get_string("unenrolleduser","core_enrol",userData)))).then((notificationString=>{(0,_toast.add)(notificationString)})).catch(_notification.default.exception)},getBody=function(contextId,ueid){let formdata=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return _fragment.default.loadFragment("enrol","user_enrolment_form",contextId,{ueid:ueid,formdata:formdata})};_exports.init=_ref2=>{let{contextid:contextid,uniqueid:uniqueid}=_ref2;((contextId,uniqueId)=>{const getBodyFunction=(userEnrolmentId,formData)=>getBody(contextId,userEnrolmentId,formData);document.addEventListener("click",(e=>{if(!e.target.closest(_selectors.default.main.fromRegionId(uniqueId)))return;const editLink=e.target.closest(Selectors_editEnrolment);editLink&&(e.preventDefault(),showEditDialogue(editLink,getBodyFunction));const unenrolLink=e.target.closest(Selectors_unenrol);unenrolLink&&(e.preventDefault(),showUnenrolConfirmation(unenrolLink));const showDetailsLink=e.target.closest(Selectors_showDetails);showDetailsLink&&(e.preventDefault(),showStatusDetails(showDetailsLink))}))})(contextid,uniqueid)}}));

//# sourceMappingURL=status_field.min.js.map