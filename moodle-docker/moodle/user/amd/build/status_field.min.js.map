{"version": 3, "file": "status_field.min.js", "sources": ["../src/status_field.js"], "sourcesContent": ["// This file is part of Moodle - http://moodle.org/\n//\n// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify\n// it under the terms of the GNU General Public License as published by\n// the Free Software Foundation, either version 3 of the License, or\n// (at your option) any later version.\n//\n// Moodle is distributed in the hope that it will be useful,\n// but WITHOUT ANY WARRANTY; without even the implied warranty of\n// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the\n// GNU General Public License for more details.\n//\n// You should have received a copy of the GNU General Public License\n// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.\n\n/**\n * AMD module for the user enrolment status field in the course participants page.\n *\n * @module     core_user/status_field\n * @copyright  2017 Jun Pataleta\n * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later\n */\n\nimport * as DynamicTable from 'core_table/dynamic';\nimport * as Repository from './repository';\nimport * as Str from 'core/str';\nimport DynamicTableSelectors from 'core_table/local/dynamic/selectors';\nimport Fragment from 'core/fragment';\nimport ModalEvents from 'core/modal_events';\nimport Notification from 'core/notification';\nimport Templates from 'core/templates';\nimport {add as notifyUser} from 'core/toast';\nimport SaveCancelModal from 'core/modal_save_cancel';\nimport CancelModal from 'core/modal_cancel';\n\nconst Selectors = {\n    editEnrolment: '[data-action=\"editenrolment\"]',\n    showDetails: '[data-action=\"showdetails\"]',\n    unenrol: '[data-action=\"unenrol\"]',\n    statusElement: '[data-status]',\n};\n\n/**\n * Get the dynamic table from the specified link.\n *\n * @param {HTMLElement} link\n * @returns {HTMLElement}\n */\nconst getDynamicTableFromLink = link => link.closest(DynamicTableSelectors.main.region);\n\n/**\n * Get the status container from the specified link.\n *\n * @param {HTMLElement} link\n * @returns {HTMLElement}\n */\nconst getStatusContainer = link => link.closest(Selectors.statusElement);\n\n/**\n * Get user enrolment id from the specified link\n *\n * @param {HTMLElement} link\n * @returns {Number}\n */\nconst getUserEnrolmentIdFromLink = link => link.getAttribute('rel');\n\n/**\n * Register all event listeners for the status fields.\n *\n * @param {Number} contextId\n * @param {Number} uniqueId\n */\nconst registerEventListeners = (contextId, uniqueId) => {\n    const getBodyFunction = (userEnrolmentId, formData) => getBody(contextId, userEnrolmentId, formData);\n\n    document.addEventListener('click', e => {\n        const tableRoot = e.target.closest(DynamicTableSelectors.main.fromRegionId(uniqueId));\n        if (!tableRoot) {\n            return;\n        }\n\n        const editLink = e.target.closest(Selectors.editEnrolment);\n        if (editLink) {\n            e.preventDefault();\n\n            showEditDialogue(editLink, getBodyFunction);\n        }\n\n        const unenrolLink = e.target.closest(Selectors.unenrol);\n        if (unenrolLink) {\n            e.preventDefault();\n\n            showUnenrolConfirmation(unenrolLink);\n        }\n\n        const showDetailsLink = e.target.closest(Selectors.showDetails);\n        if (showDetailsLink) {\n            e.preventDefault();\n\n            showStatusDetails(showDetailsLink);\n        }\n    });\n};\n\n/**\n * Show the edit dialogue.\n *\n * @param {HTMLElement} link\n * @param {Function} getBody Function to get the body for the specified user enrolment\n */\nconst showEditDialogue = (link, getBody) => {\n    const container = getStatusContainer(link);\n    const userEnrolmentId = getUserEnrolmentIdFromLink(link);\n\n    SaveCancelModal.create({\n        large: true,\n        title: Str.get_string('edituserenrolment', 'enrol', container.dataset.fullname),\n        body: getBody(userEnrolmentId)\n    })\n    .then(modal => {\n        // Handle save event.\n        modal.getRoot().on(ModalEvents.save, e => {\n            // Don't close the modal yet.\n            e.preventDefault();\n\n            // Submit form data.\n            submitEditFormAjax(link, getBody, modal, userEnrolmentId, container.dataset);\n        });\n\n        // Handle hidden event.\n        modal.getRoot().on(ModalEvents.hidden, () => {\n            // Destroy when hidden.\n            modal.destroy();\n        });\n\n        // Show the modal.\n        modal.show();\n\n        return modal;\n    })\n    .catch(Notification.exception);\n};\n\n/**\n * Show and handle the unenrolment confirmation dialogue.\n *\n * @param {HTMLElement} link\n */\nconst showUnenrolConfirmation = link => {\n    const container = getStatusContainer(link);\n    const userEnrolmentId = getUserEnrolmentIdFromLink(link);\n\n    SaveCancelModal.create()\n    .then(modal => {\n        // Handle confirm event.\n        modal.getRoot().on(ModalEvents.save, e => {\n            // Don't close the modal yet.\n            e.preventDefault();\n\n            // Submit data.\n            submitUnenrolFormAjax(\n                link,\n                modal,\n                {\n                    ueid: userEnrolmentId,\n                },\n                container.dataset\n            );\n        });\n\n        // Handle hidden event.\n        modal.getRoot().on(ModalEvents.hidden, () => {\n            // Destroy when hidden.\n            modal.destroy();\n        });\n\n        // Display the delete confirmation modal.\n        modal.show();\n\n        const stringData = [\n            {\n                key: 'unenrol',\n                component: 'enrol',\n            },\n            {\n                key: 'unenrolconfirm',\n                component: 'enrol',\n                param: {\n                    user: container.dataset.fullname,\n                    course: container.dataset.coursename,\n                    enrolinstancename: container.dataset.enrolinstancename,\n                }\n            }\n        ];\n\n        return Promise.all([Str.get_strings(stringData), modal]);\n    })\n    .then(([strings, modal]) => {\n        modal.setTitle(strings[0]);\n        modal.setSaveButtonText(strings[0]);\n        modal.setBody(strings[1]);\n\n        return modal;\n    })\n    .catch(Notification.exception);\n};\n\n/**\n * Show the user details dialogue.\n *\n * @param {HTMLElement} link\n */\nconst showStatusDetails = link => {\n    const container = getStatusContainer(link);\n\n    const context = {\n        editenrollink: '',\n        statusclass: container.querySelector('span.badge').getAttribute('class'),\n        ...container.dataset,\n    };\n\n    // Find the edit enrolment link.\n    const editEnrolLink = container.querySelector(Selectors.editEnrolment);\n    if (editEnrolLink) {\n        // If there's an edit enrolment link for this user, clone it into the context for the modal.\n        context.editenrollink = editEnrolLink.outerHTML;\n    }\n\n    CancelModal.create({\n        large: true,\n        title: Str.get_string('enroldetails', 'enrol'),\n        body: Templates.render('core_user/status_details', context),\n    })\n    .then(modal => {\n        if (editEnrolLink) {\n            modal.getRoot().on('click', Selectors.editEnrolment, e => {\n                e.preventDefault();\n                modal.hide();\n\n                // Trigger click event for the edit enrolment link to show the edit enrolment modal.\n                editEnrolLink.click();\n            });\n        }\n\n        modal.show();\n\n        // Handle hidden event.\n        modal.getRoot().on(ModalEvents.hidden, () => modal.destroy());\n\n        return modal;\n    })\n    .catch(Notification.exception);\n};\n\n/**\n * Submit the edit dialogue.\n *\n * @param {HTMLElement} clickedLink\n * @param {Function} getBody\n * @param {Object} modal\n * @param {Number} userEnrolmentId\n * @param {Object} userData\n */\nconst submitEditFormAjax = (clickedLink, getBody, modal, userEnrolmentId, userData) => {\n    const form = modal.getRoot().find('form');\n\n    Repository.submitUserEnrolmentForm(form.serialize())\n    .then(data => {\n        if (!data.result) {\n            throw data.result;\n        }\n\n        // Dismiss the modal.\n        modal.hide();\n        modal.destroy();\n\n        return data;\n    })\n    .then(() => {\n        DynamicTable.refreshTableContent(getDynamicTableFromLink(clickedLink))\n        .catch(Notification.exception);\n\n        return Str.get_string('enrolmentupdatedforuser', 'core_enrol', userData);\n    })\n    .then(notificationString => {\n        notifyUser(notificationString);\n\n        return;\n    })\n    .catch(() => {\n        modal.setBody(getBody(userEnrolmentId, JSON.stringify(form.serialize())));\n\n        return modal;\n    });\n};\n\n/**\n * Submit the unenrolment form.\n *\n * @param {HTMLElement} clickedLink\n * @param {Object} modal\n * @param {Object} args\n * @param {Object} userData\n */\nconst submitUnenrolFormAjax = (clickedLink, modal, args, userData) => {\n    Repository.unenrolUser(args.ueid)\n    .then(data => {\n        if (!data.result) {\n            // Display an alert containing the error message\n            Notification.alert(data.errors[0].key, data.errors[0].message);\n\n            return data;\n        }\n\n        // Dismiss the modal.\n        modal.hide();\n        modal.destroy();\n\n        return data;\n    })\n    .then(() => {\n        DynamicTable.refreshTableContent(getDynamicTableFromLink(clickedLink))\n        .catch(Notification.exception);\n\n        return Str.get_string('unenrolleduser', 'core_enrol', userData);\n    })\n    .then(notificationString => {\n        notifyUser(notificationString);\n\n        return;\n    })\n    .catch(Notification.exception);\n};\n\n/**\n * Get the body fragment.\n *\n * @param {Number} contextId\n * @param {Number} ueid The user enrolment id\n * @param {Object} formdata\n * @returns {Promise}\n */\nconst getBody = (contextId, ueid, formdata = null) => Fragment.loadFragment(\n    'enrol',\n    'user_enrolment_form',\n    contextId,\n    {\n        ueid,\n        formdata,\n    }\n);\n\n/**\n * Initialise the statu field handler.\n *\n * @param {object} param\n * @param {Number} param.contextid\n * @param {Number} param.uniqueid\n */\nexport const init = ({contextid, uniqueid}) => {\n    registerEventListeners(contextid, uniqueid);\n};\n"], "names": ["Selectors", "getDynamicTableFromLink", "link", "closest", "DynamicTableSelectors", "main", "region", "getStatusContainer", "getUserEnrolmentIdFromLink", "getAttribute", "showEditDialogue", "getBody", "container", "userEnrolmentId", "create", "large", "title", "Str", "get_string", "dataset", "fullname", "body", "then", "modal", "getRoot", "on", "ModalEvents", "save", "e", "preventDefault", "submitEditFormAjax", "hidden", "destroy", "show", "catch", "Notification", "exception", "showUnenrolConfirmation", "submitUnenrolFormAjax", "<PERSON><PERSON>d", "stringData", "key", "component", "param", "user", "course", "coursename", "enrolinstancename", "Promise", "all", "get_strings", "_ref", "strings", "setTitle", "setSaveButtonText", "setBody", "showStatusDetails", "context", "editenrollink", "statusclass", "querySelector", "editEnrolLink", "outerHTML", "Templates", "render", "hide", "click", "clickedLink", "userData", "form", "find", "Repository", "submitUserEnrolmentForm", "serialize", "data", "result", "DynamicTable", "refreshTableContent", "notificationString", "JSON", "stringify", "args", "unenrolUser", "alert", "errors", "message", "contextId", "formdata", "Fragment", "loadFragment", "_ref2", "contextid", "uniqueid", "uniqueId", "getBodyFunction", "formData", "document", "addEventListener", "target", "fromRegionId", "editLink", "unenrolLink", "showDetailsLink", "registerEventListeners"], "mappings": ";;;;;;;6jBAmCMA,wBACa,gCADbA,sBAEW,8BAFXA,kBAGO,0BAHPA,wBAIa,gBASbC,wBAA0BC,MAAQA,KAAKC,QAAQC,mBAAsBC,KAAKC,QAQ1EC,mBAAqBL,MAAQA,KAAKC,QAAQH,yBAQ1CQ,2BAA6BN,MAAQA,KAAKO,aAAa,OA8CvDC,iBAAmB,CAACR,KAAMS,iBACtBC,UAAYL,mBAAmBL,MAC/BW,gBAAkBL,2BAA2BN,iCAEnCY,OAAO,CACnBC,OAAO,EACPC,MAAOC,IAAIC,WAAW,oBAAqB,QAASN,UAAUO,QAAQC,UACtEC,KAAMV,QAAQE,mBAEjBS,MAAKC,QAEFA,MAAMC,UAAUC,GAAGC,sBAAYC,MAAMC,IAEjCA,EAAEC,iBAGFC,mBAAmB5B,KAAMS,QAASY,MAAOV,gBAAiBD,UAAUO,YAIxEI,MAAMC,UAAUC,GAAGC,sBAAYK,QAAQ,KAEnCR,MAAMS,aAIVT,MAAMU,OAECV,SAEVW,MAAMC,sBAAaC,YAQlBC,wBAA0BnC,aACtBU,UAAYL,mBAAmBL,MAC/BW,gBAAkBL,2BAA2BN,iCAEnCY,SACfQ,MAAKC,QAEFA,MAAMC,UAAUC,GAAGC,sBAAYC,MAAMC,IAEjCA,EAAEC,iBAGFS,sBACIpC,KACAqB,MACA,CACIgB,KAAM1B,iBAEVD,UAAUO,YAKlBI,MAAMC,UAAUC,GAAGC,sBAAYK,QAAQ,KAEnCR,MAAMS,aAIVT,MAAMU,aAEAO,WAAa,CACf,CACIC,IAAK,UACLC,UAAW,SAEf,CACID,IAAK,iBACLC,UAAW,QACXC,MAAO,CACHC,KAAMhC,UAAUO,QAAQC,SACxByB,OAAQjC,UAAUO,QAAQ2B,WAC1BC,kBAAmBnC,UAAUO,QAAQ4B,4BAK1CC,QAAQC,IAAI,CAAChC,IAAIiC,YAAYV,YAAajB,WAEpDD,MAAK6B,WAAEC,QAAS7B,mBACbA,MAAM8B,SAASD,QAAQ,IACvB7B,MAAM+B,kBAAkBF,QAAQ,IAChC7B,MAAMgC,QAAQH,QAAQ,IAEf7B,SAEVW,MAAMC,sBAAaC,YAQlBoB,kBAAoBtD,aAChBU,UAAYL,mBAAmBL,MAE/BuD,QAAU,CACZC,cAAe,GACfC,YAAa/C,UAAUgD,cAAc,cAAcnD,aAAa,YAC7DG,UAAUO,SAIX0C,cAAgBjD,UAAUgD,cAAc5D,yBAC1C6D,gBAEAJ,QAAQC,cAAgBG,cAAcC,iCAG9BhD,OAAO,CACfC,OAAO,EACPC,MAAOC,IAAIC,WAAW,eAAgB,SACtCG,KAAM0C,mBAAUC,OAAO,2BAA4BP,WAEtDnC,MAAKC,QACEsC,eACAtC,MAAMC,UAAUC,GAAG,QAASzB,yBAAyB4B,IACjDA,EAAEC,iBACFN,MAAM0C,OAGNJ,cAAcK,WAItB3C,MAAMU,OAGNV,MAAMC,UAAUC,GAAGC,sBAAYK,QAAQ,IAAMR,MAAMS,YAE5CT,SAEVW,MAAMC,sBAAaC,YAYlBN,mBAAqB,CAACqC,YAAaxD,QAASY,MAAOV,gBAAiBuD,kBAChEC,KAAO9C,MAAMC,UAAU8C,KAAK,QAElCC,WAAWC,wBAAwBH,KAAKI,aACvCnD,MAAKoD,WACGA,KAAKC,aACAD,KAAKC,cAIfpD,MAAM0C,OACN1C,MAAMS,UAEC0C,QAEVpD,MAAK,KACFsD,aAAaC,oBAAoB5E,wBAAwBkE,cACxDjC,MAAMC,sBAAaC,WAEbnB,IAAIC,WAAW,0BAA2B,aAAckD,aAElE9C,MAAKwD,oCACSA,uBAId5C,OAAM,KACHX,MAAMgC,QAAQ5C,QAAQE,gBAAiBkE,KAAKC,UAAUX,KAAKI,eAEpDlD,UAYTe,sBAAwB,CAAC6B,YAAa5C,MAAO0D,KAAMb,YACrDG,WAAWW,YAAYD,KAAK1C,MAC3BjB,MAAKoD,MACGA,KAAKC,QAQVpD,MAAM0C,OACN1C,MAAMS,UAEC0C,6BATUS,MAAMT,KAAKU,OAAO,GAAG3C,IAAKiC,KAAKU,OAAO,GAAGC,SAE/CX,QASdpD,MAAK,KACFsD,aAAaC,oBAAoB5E,wBAAwBkE,cACxDjC,MAAMC,sBAAaC,WAEbnB,IAAIC,WAAW,iBAAkB,aAAckD,aAEzD9C,MAAKwD,oCACSA,uBAId5C,MAAMC,sBAAaC,YAWlBzB,QAAU,SAAC2E,UAAW/C,UAAMgD,gEAAW,YAASC,kBAASC,aAC3D,QACA,sBACAH,UACA,CACI/C,KAAAA,KACAgD,SAAAA,0BAWYG,YAACC,UAACA,UAADC,SAAYA,gBA/RF,EAACN,UAAWO,kBACjCC,gBAAkB,CAACjF,gBAAiBkF,WAAapF,QAAQ2E,UAAWzE,gBAAiBkF,UAE3FC,SAASC,iBAAiB,SAASrE,QACbA,EAAEsE,OAAO/F,QAAQC,mBAAsBC,KAAK8F,aAAaN,wBAKrEO,SAAWxE,EAAEsE,OAAO/F,QAAQH,yBAC9BoG,WACAxE,EAAEC,iBAEFnB,iBAAiB0F,SAAUN,wBAGzBO,YAAczE,EAAEsE,OAAO/F,QAAQH,mBACjCqG,cACAzE,EAAEC,iBAEFQ,wBAAwBgE,oBAGtBC,gBAAkB1E,EAAEsE,OAAO/F,QAAQH,uBACrCsG,kBACA1E,EAAEC,iBAEF2B,kBAAkB8C,sBAqQ1BC,CAAuBZ,UAAWC"}