<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Mood<PERSON> is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace core_user\hook;

use core\hook\stoppable_trait;

/**
 * Allow plugins to callback as soon possible after user has completed login.
 *
 * @package    core_user
 * @copyright  2024 Juan <PERSON>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
#[\core\attribute\label('Allow plugins to callback as soon possible after user has completed login.')]
#[\core\attribute\tags('user', 'login')]
class after_login_completed implements
    \Psr\EventDispatcher\StoppableEventInterface
{
    use stoppable_trait;
}
