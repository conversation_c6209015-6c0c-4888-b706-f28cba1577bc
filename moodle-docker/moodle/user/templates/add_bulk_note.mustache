{{!
    This file is part of Moodle - http://moodle.org/

    Moodle is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_user/add_bulk_note

    Template for the add bulk note modal.

    Context variables required for this template:
    * stateNames array - List of value / label pairs of valid publish states for notes.
    * stateHelpIcon string - Rendered help icon for the publish state.

    Example context (json):
    {
        "stateNames": [ { "value": 0, "label": "State 1"}, { "value": 1, "label": "State 2"} ],
        "stateHelpIcon": "(help me)"
    }
}}
<form>
<p>
<label for="bulk-state" class="me-2">
{{#str}}publishstate, core_notes{{/str}}
</label>
<select name="state" id="bulk-state" class="form-select">
{{#stateNames}}
    <option value="{{value}}" {{#selected}}selected{{/selected}}>{{label}}</option>
{{/stateNames}}
</select>
{{{stateHelpIcon}}}
</p>
<p>
<label for="bulk-note">
<span class="visually-hidden">{{#str}}note, core_notes{{/str}}</span>
</label>
<textarea id="bulk-note" rows="3" data-max-rows="10" data-auto-rows="true" cols="30" class="form-control"></textarea>
</p>
</form>
{{#js}}
require(['core/auto_rows'], function(AutoRows) {
    AutoRows.init(document.getElementById('bulk-note'));
});
{{/js}}
