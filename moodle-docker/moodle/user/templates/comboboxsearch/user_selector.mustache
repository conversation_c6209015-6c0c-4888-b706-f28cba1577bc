{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_user/comboboxsearch/user_selector

    The user selector trigger element.

    Context variables required for this template:
    * name - The name of the input element representing the user search combobox.
    * value - The value of the input element representing the user search combobox.
    * currentvalue - If the user has already searched, set the value to that.
    * courseid - The course ID.
    * group - The group ID.
    * resetlink - The link to reset the page.

    Example context (json):
    {
        "name": "input-1",
        "value": "0",
        "currentvalue": "bar",
        "courseid": 2,
        "group": 25,
        "resetlink": "grade/report/grader/index.php?id=2"
    }
}}
<span class="d-none" data-region="courseid" data-courseid="{{courseid}}"></span>
<span class="d-none" data-region="groupid" data-groupid="{{group}}"></span>
<span class="d-none" data-region="instance" data-instance="{{instance}}"></span>
<span class="d-none" data-region="currentvalue" data-currentvalue="{{currentvalue}}"></span>
{{< core/search_input_auto }}
    {{$label}}{{#str}}searchusers, core{{/str}}{{/label}}
    {{$placeholder}}{{#str}}searchusers, core{{/str}}{{/placeholder}}
    {{$value}}{{currentvalue}}{{/value}}
    {{$additionalattributes}}
        role="combobox"
        aria-expanded="false"
        aria-controls="user-{{instance}}-result-listbox"
        aria-autocomplete="list"
        aria-haspopup="listbox"
        data-input-element="user-input-{{uniqid}}-{{instance}}"
    {{/additionalattributes}}
{{/ core/search_input_auto }}
{{#currentvalue}}
    <a class="ms-2 btn btn-link border-0 align-self-center" href="{{resetlink}}" data-action="resetpage" role="link" aria-label="{{#str}}clearsearch, core{{/str}}">
        {{#str}}clear{{/str}}
    </a>
{{/currentvalue}}
<input type="hidden" name="{{name}}" value="{{value}}" id="user-input-{{uniqid}}-{{instance}}"/>
