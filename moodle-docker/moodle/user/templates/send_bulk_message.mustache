{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHA<PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_user/send_bulk_message

    Template for the send bulk message modal.

    Context variables required for this template:
      None

    Example context (json):
    {
    }
}}
<form>
<p>
<label for="bulk-message">
<span class="visually-hidden">{{#str}}message, core_message{{/str}}</span>
</label>
<textarea id="bulk-message" rows="3" data-max-rows="10" data-auto-rows="true" cols="30" class="form-control"></textarea>
</p>
<div class="text-danger" data-role="messagetextrequired" hidden>
    {{#str}} messagetextrequired, core_message {{/str}}
</div>
</form>
{{#js}}
require(['core/auto_rows'], function(AutoRows) {
    AutoRows.init(document.getElementById('bulk-message'));
});
{{/js}}
