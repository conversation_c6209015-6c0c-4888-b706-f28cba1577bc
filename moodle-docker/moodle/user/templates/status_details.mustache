{{!
    This file is part of Moodle - http://moodle.org/

    Mo<PERSON>le is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_user/status_details

    Template for the user enrolment status details.

    Context variables required for this template:
    * fullname string - The user's full name.
    * coursename string - The course name.
    * enrolinstancename string - The enrolment instance name.
    * status string - The enrolment status.
    * statusclass string - The CSS class for the enrolment status.
    * timestart string - Optional. The enrolment time start.
    * timeend string - Optional. The enrolment time end.
    * timeenrolled string - Optional. The time when the user was enrolled.

    Example context (json):
    {
        "fullname": "Student John",
        "coursename": "TC 1",
        "enrolinstancename": "Manual enrolment",
        "status": "Active",
        "statusclass": "badge bg-success text-white",
        "timestart": "1 January 2017",
        "timeend": "31 January 2018",
        "timeenrolled": "31 December 2016"
    }
}}
<table class="table user-enrol-details">
    <tr>
        <th>
            {{#str}}fullname{{/str}}
        </th>
        <td class="user-fullname">
            {{fullname}}
        </td>
    </tr>
    <tr>
        <th>
            {{#str}}course{{/str}}
        </th>
        <td class="user-course">
            {{coursename}}
        </td>
    </tr>
    <tr>
        <th>
            {{#str}}enrolmentmethod, enrol{{/str}}
        </th>
        <td class="user-enrol-instance">
            {{enrolinstancename}} {{{editenrollink}}}
        </td>
    </tr>
    <tr>
        <th>
            {{#str}}participationstatus, enrol{{/str}}
        </th>
        <td class="user-enrol-status">
            <span class="{{statusclass}}">
                {{status}}
            </span>
        </td>
    </tr>
    {{#timestart}}
    <tr>
        <th>
            {{#str}}enroltimestart, enrol{{/str}}
        </th>
        <td  class="user-enrol-timestart">
            {{timestart}}
        </td>
    </tr>
    {{/timestart}}
    {{#timeend}}
    <tr>
        <th>
            {{#str}}enroltimeend, enrol{{/str}}
        </th>
        <td class="user-enrol-timeend">
            {{timeend}}
        </td>
    </tr>
    {{/timeend}}
    {{#timeenrolled}}
    <tr>
        <th>
            {{#str}}enroltimecreated, enrol{{/str}}
        </th>
        <td class="user-enrol-timeenrolled">
            {{timeenrolled}}
        </td>
    </tr>
    {{/timeenrolled}}
</table>
