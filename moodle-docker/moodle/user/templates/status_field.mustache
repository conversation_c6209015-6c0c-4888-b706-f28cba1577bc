{{!
    This file is part of Moodle - http://moodle.org/

    <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    Moodle is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with Moodle.  If not, see <http://www.gnu.org/licenses/>.
}}
{{!
    @template core_user/status_field

    Template for the enrolment status field.

    Context variables required for this template:
    * fullname string - The user's full name.
    * coursename string - The course name.
    * enrolinstancename string - The enrolment instance name.
    * status string - The enrolment status.
    * active boolean - Flag to indicate whether the user has an active enrolment.
    * suspended boolean - Flag to indicate whether the user is suspended.
    * notcurrent boolean - Flag to indicate whether the user's enrolment is active, but not current.
    * timestart string - Optional. The enrolment time start.
    * timeend string - Optional. The enrolment time end.
    * timeenrolled string - Optional. The time when the user was enrolled.
    * enrolactions array - Optional. Array of enrol actions consisting of:
      * url string - The URL of the enrol action link.
      * attributes array - The array of attributes for the enrol action link.
      * icon string - The HTML output for the enrol action icon.

    Example context (json):
    {
        "fullname": "Student John",
        "coursename": "TC 1",
        "enrolinstancename": "Manual enrolment",
        "status": "Active",
        "active": true,
        "timestart": "1 January 2017",
        "timeend": "31 January 2018",
        "timeenrolled": "31 December 2016",
        "enrolactions": [
            {
                "url": "#",
                "attributes": [
                    {
                        "name": "class",
                        "value": "action-class"
                    }
                ],
                "icon": "<i class=\"icon fa fa-cog fa-fw\" aria-hidden=\"true\" title=\"Edit enrolment\" aria-label=\"\"></i>"
            }
        ]
    }
}}
<div data-fullname="{{fullname}}" data-coursename="{{coursename}}" data-enrolinstancename="{{enrolinstancename}}"
     data-status="{{status}}" data-timestart="{{timestart}}" data-timeend="{{timeend}}" data-timeenrolled="{{timeenrolled}}">
    <span class="badge {{#active}}bg-success text-white{{/active}}{{#suspended}}bg-warning text-dark{{/suspended}}{{#notcurrent}}bg-secondary text-dark{{/notcurrent}}">{{status}}</span>
    <a data-action="showdetails" href="#" role="button" tabindex="0">{{!
        }}{{#pix}}docs, core, {{enrolinstancename}}{{/pix}}{{!
    }}</a>
    {{#enrolactions}}
    <a href="{{url}}" role="button" {{#attributes}}{{name}}="{{value}}" {{/attributes}}>{{!
        }}{{{icon}}}{{!
    }}</a>
    {{/enrolactions}}
</div>
