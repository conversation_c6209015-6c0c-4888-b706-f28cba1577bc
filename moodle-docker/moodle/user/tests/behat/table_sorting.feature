@core @core_user
Feature: Tables can be sorted by additional names
  In order to sort fields by additional names
  As a user
  I need to browse to a page with users in a table.

  Background:
    Given the following "users" exist:
    | username | firstname | lastname | middlename | alternatename | email | idnumber |
    | student1 | <PERSON> | <PERSON> | Faith | Anne | <EMAIL> | s1 |
    | student2 | <PERSON> | <PERSON> | <PERSON> | Gman | <EMAIL> | s2 |
    | student3 | <PERSON> | <PERSON> | Peter | Mr T | <EMAIL> | s3 |
    And I log in as "admin"
    And I navigate to "Users > Permissions > User policies" in site administration
    And the following config values are set as admin:
    | fullnamedisplay | firstname middlename lastname |
    | alternativefullnameformat | firstname middlename alternatename lastname |

  @javascript
  Scenario: All user names are show and sortable in the administration user list.
    Given I navigate to "Users > Accounts > Browse list of users" in site administration
    Then the following should exist in the "reportbuilder-table" table:
    | First name | Email address |
    | Admin User | <EMAIL> |
    | <PERSON> | <EMAIL> |
    | <PERSON> | <EMAIL> |
    | <PERSON> T <PERSON> | <EMAIL> |
    And "<PERSON>" "table_row" should appear before "<PERSON>" "table_row"
    And "<PERSON>" "table_row" should appear before "<PERSON> <PERSON> Mr T Sutcliff" "table_row"
    And I follow "Middle name"
    And "<PERSON> <PERSON>man <PERSON>" "table_row" should appear before "<PERSON> <PERSON> <PERSON> <PERSON>" "table_row"
    And "<PERSON> <PERSON> <PERSON> <PERSON>" "table_row" should appear before "<PERSON> <PERSON> Mr T Sutcliff" "table_row"
    And I follow "Middle name"
    And "<PERSON>man <PERSON>" "table_row" should appear after "<PERSON> <PERSON> <PERSON> <PERSON>" "table_row"
    And "<PERSON> <PERSON> <PERSON> <PERSON>" "table_row" should appear after "<PERSON> <PERSON> Mr T Sutcliff" "table_row"
    And I follow "Alternate name"
    And "Annie Faith Anne Edison" "table_row" should appear after "George David Gman Bradley" "table_row"
    And "George David Gman Bradley" "table_row" should appear after "Travis Peter Mr T Sutcliff" "table_row"
    And I follow "Alternate name"
    And "Annie Faith Anne Edison" "table_row" should appear before "George David Gman Bradley" "table_row"
    And "George David Gman Bradley" "table_row" should appear before "Travis Peter Mr T Sutcliff" "table_row"
    And I follow "Last name"
    And "George David Gman Bradley" "table_row" should appear before "Annie Faith Anne Edison" "table_row"
    And "Annie Faith Anne Edison" "table_row" should appear before "Travis Peter Mr T Sutcliff" "table_row"
    And I follow "Last name"
    And "George David Gman Bradley" "table_row" should appear after "Annie Faith Anne Edison" "table_row"
    And "Annie Faith Anne Edison" "table_row" should appear after "Travis Peter Mr T Sutcliff" "table_row"
