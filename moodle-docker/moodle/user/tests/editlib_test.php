<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

namespace core_user;

defined('MOODLE_INTERNAL') || die();

global $CFG;
require_once($CFG->dirroot.'/user/editlib.php');

/**
 * Unit tests for user editlib api.
 *
 * @package    core_user
 * @category   test
 * @copyright  2013 Adrian Greeve <<EMAIL>>
 * @license    http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */
final class editlib_test extends \advanced_testcase {

    /**
     * Test that the required fields are returned in the correct order.
     */
    function test_useredit_get_required_name_fields(): void {
        global $CFG;
        // Back up config settings for restore later.
        $originalcfg = new \stdClass();
        $originalcfg->fullnamedisplay = $CFG->fullnamedisplay;

        $CFG->fullnamedisplay = 'language';
        $expectedresult = array(5 => 'firstname', 21 => 'lastname');
        $this->assertEquals(useredit_get_required_name_fields(), $expectedresult);
        $CFG->fullnamedisplay = 'firstname';
        $expectedresult = array(5 => 'firstname', 21 => 'lastname');
        $this->assertEquals(useredit_get_required_name_fields(), $expectedresult);
        $CFG->fullnamedisplay = 'lastname firstname';
        $expectedresult = array('lastname', 9 => 'firstname');
        $this->assertEquals(useredit_get_required_name_fields(), $expectedresult);
        $CFG->fullnamedisplay = 'firstnamephonetic lastnamephonetic';
        $expectedresult = array(5 => 'firstname', 21 => 'lastname');
        $this->assertEquals(useredit_get_required_name_fields(), $expectedresult);

        // Tidy up after we finish testing.
        $CFG->fullnamedisplay = $originalcfg->fullnamedisplay;
    }

    /**
     * Test that the enabled fields are returned in the correct order.
     */
    function test_useredit_get_enabled_name_fields(): void {
        global $CFG;
        // Back up config settings for restore later.
        $originalcfg = new \stdClass();
        $originalcfg->fullnamedisplay = $CFG->fullnamedisplay;

        $CFG->fullnamedisplay = 'language';
        $expectedresult = array();
        $this->assertEquals(useredit_get_enabled_name_fields(), $expectedresult);
        $CFG->fullnamedisplay = 'firstname lastname firstnamephonetic';
        $expectedresult = array(19 => 'firstnamephonetic');
        $this->assertEquals(useredit_get_enabled_name_fields(), $expectedresult);
        $CFG->fullnamedisplay = 'firstnamephonetic, lastname lastnamephonetic (alternatename)';
        $expectedresult = array('firstnamephonetic', 28 => 'lastnamephonetic', 46 => 'alternatename');
        $this->assertEquals(useredit_get_enabled_name_fields(), $expectedresult);
        $CFG->fullnamedisplay = 'firstnamephonetic lastnamephonetic alternatename middlename';
        $expectedresult = array('firstnamephonetic', 18 => 'lastnamephonetic', 35 => 'alternatename', 49 => 'middlename');
        $this->assertEquals(useredit_get_enabled_name_fields(), $expectedresult);

        // Tidy up after we finish testing.
        $CFG->fullnamedisplay = $originalcfg->fullnamedisplay;
    }

    /**
     * Test that the disabled fields are returned.
     */
    function test_useredit_get_disabled_name_fields(): void {
        global $CFG;
        // Back up config settings for restore later.
        $originalcfg = new \stdClass();
        $originalcfg->fullnamedisplay = $CFG->fullnamedisplay;

        $CFG->fullnamedisplay = 'language';
        $expectedresult = array('firstnamephonetic' => 'firstnamephonetic', 'lastnamephonetic' => 'lastnamephonetic',
                'middlename' => 'middlename', 'alternatename' => 'alternatename');
        $this->assertEquals(useredit_get_disabled_name_fields(), $expectedresult);
        $CFG->fullnamedisplay = 'firstname lastname firstnamephonetic';
        $expectedresult = array('lastnamephonetic' => 'lastnamephonetic', 'middlename' => 'middlename', 'alternatename' => 'alternatename');
        $this->assertEquals(useredit_get_disabled_name_fields(), $expectedresult);
        $CFG->fullnamedisplay = 'firstnamephonetic, lastname lastnamephonetic (alternatename)';
        $expectedresult = array('middlename' => 'middlename');
        $this->assertEquals(useredit_get_disabled_name_fields(), $expectedresult);
        $CFG->fullnamedisplay = 'firstnamephonetic lastnamephonetic alternatename middlename';
        $expectedresult = array();
        $this->assertEquals(useredit_get_disabled_name_fields(), $expectedresult);

        // Tidy up after we finish testing.
        $CFG->fullnamedisplay = $originalcfg->fullnamedisplay;
    }

    /**
     * Provider for test_useredit_update_user_preference
     *
     * @return array
     */
    public static function useredit_update_user_preference_provider(): array {
        return [
            'Homepage fixed' => [HOMEPAGE_SITE, [], HOMEPAGE_MYCOURSES, null],
            'Simple user pref' => [HOMEPAGE_USER, [], HOMEPAGE_MYCOURSES, (string)HOMEPAGE_MYCOURSES],
            'Extra option' => [HOMEPAGE_USER, ['/helloworld'], '/helloworld', '/helloworld'],
            'Invalid option' => [HOMEPAGE_USER, [], '/helloworld', (string)HOMEPAGE_MY],
        ];
    }

    /**
     * Tests for function useredit_update_user_preference
     *
     * @dataProvider useredit_update_user_preference_provider
     * @covers ::useredit_update_user_preference
     * @param int $defaulthomepage to be set as the default homepage
     * @param array $extraoptions additional options for the pref value that are added in the hook callbacks
     * @param int|string $setting what we are trying to set as a user preference
     * @param string|null $expected what will be actually set in the user preference
     */
    public function test_useredit_update_user_preference(int $defaulthomepage, array $extraoptions,
            $setting, ?string $expected): void {
        global $DB, $CFG, $USER;
        $this->resetAfterTest();
        $user = $this->getDataGenerator()->create_user();
        $this->setUser($user);

        if ($extraoptions) {
            // Pretend we have hook callbacks adding extra allowed options.
            $testcallback = function(\core_user\hook\extend_default_homepage $hook) use ($extraoptions) {
                foreach ($extraoptions as $localurl) {
                    $hook->add_option(new \moodle_url($localurl), new \lang_string('yes'));
                }
            };
            $this->redirectHook(\core_user\hook\extend_default_homepage::class, $testcallback);
        }

        set_config('defaulthomepage', $defaulthomepage);
        useredit_update_user_preference([
            'id' => $user->id,
            'preference_user_home_page_preference' => $setting,
        ]);

        // Ensure user preference returns expected value.
        $preference = get_user_preferences('user_home_page_preference', null, $user);
        $this->assertSame($expected, $preference);
    }
}
