=== 4.5 Onwards ===

This file has been replaced by UPGRADING.md. See MDL-81125 for further information.

===
This files describes API changes for code that uses the user API.

=== 4.4 ===

* The `profile_field_base` class now contains a `show_field_content` method to determine whether the field and
  content should be shown to the user. Can be overridden in child classes as required

=== 4.3 ===

* Added new methods:
 - `core_user::get_profile_picture` for retrieving user picture.
 - `core_user::get_profile_url` for retrieving profile url.
 - `core_user::get_fullname` for retrieving user full name.
* The `core_user/repository` Javascript module now exports new methods for manipulating user preferences:
  - `[get|set]UserPreference`
  - `[get|set]UserPreferences`
* The following user preference helpers have been deprecated, please use the `core_user/repository` module instead:
  - `user_preference_allow_ajax_update`
  - `M.util.set_user_preference`
  - `lib/ajax/setuserpref.php`
* The external `core_user_set_user_preferences` method will now default the `userid` property of each preference to
  that of the current user, if omitted
* The following previously deprecated methods have been removed and can no longer be used:
  - `profile_display_fields`
  - `profile_edit_category`
  - `profile_edit_field`
  - `user_get_participants`
  - `user_get_participants_sql`
  - `user_get_total_participants`
* The users_search_sql function parameter $searchanywhere has been change to $searchtype for different type of search. $searchtype is a int parameter and has three constant value:
  USER_SEARCH_STARTS_WITH: 0, USER_SEARCH_CONTAINS: 1, USER_SEARCH_EXACT_MATCH: 2
  users_search_sql('User Test 2', '', false) =>  users_search_sql('Test Street', '', USER_SEARCH_CONTAINS)

=== 4.2 ===

* Added get_internalfield_list() and get_internalfields() in the user_field_mapping class.
  The get_internalfield_list() returns data in an array by grouping profile fields based on field categories,
  used for internal field name dropdown in the user field mapping of Oauth2 services
  The get_internalfields() converts the result from get_internalfield_list() into flat array,
  used to save/update the profile data when a user uses OAuth2 services.
* Added get_profile_field_names() and get_profile_field_list() in the profile_field_base class.
  The get_profile_field_names() returns the list of valid custom profile user fields.
  The get_profile_field_list() returns the profile fields
  in a format that can be used for choices in a group select menu.
* New method `core_user::is_current_user`, useful for components implementing permission callbacks for their preferences
* New `profile_get_user_field` method for returning profile field instance of given type
* The `profile_field_base::is_visible` method now accepts an optional `$context` argument
* External function core_user_external::add_user_private_files() now returns moodle_exception when the user quota is exceeded
* The `customfields` structure used for returning user profile fields information has been updated to:
   * Return in the field `value` the value of the custom field (as stored in the database)
   * Return in the field `displayvalue` the value of the custom field for display
   External systems displaying user information should always use `displayvalue`.
* New method `user_update_device_public_key()`` and accompanying external function core_user_external::update_device_public_key to
  update a user's app generated public key by device uuid and app id.

=== 4.1 ===

* Added a new method is_transform_supported() in the profile_field_base class.
  The purpose is to allow the field to be transformed during the export process.
  It has been implemented in the Date/Time data type (Applied in 4.1, 4.0.6).

* user_get_user_details_courses() now accepts an optional second parameter, an array of userfields that should be
  returned. The values passed into the $userfields parameter must all be included in the return from
  user_get_default_fields().
  It also allows you to reduce how much of a user record is required by the method. The minimum user record fields are:
    * id
    * deleted
    * all potential fullname fields

* Participant filter is moved to core as an API which can be used in different areas of core by implementing the API
  and filterable objects. As a part of making the API mature as a core one, these are the js files moved from core
  user to core library:
  * user/amd/src/local/participantsfilter/filter.js → lib/amd/src/datafilter/filtertype.js
  * user/amd/src/local/participantsfilter/filtertypes/country.js → lib/amd/src/datafilter/filtertypes/country.js
  * user/amd/src/local/participantsfilter/filtertypes/courseid.js → lib/amd/src/datafilter/filtertypes/courseid.js
  * user/amd/src/local/participantsfilter/filtertypes/keyword.js → lib/amd/src/datafilter/filtertypes/keyword.js
  * user/amd/src/local/participantsfilter/selectors.js → lib/amd/src/datafilter/selectors.js
  The following mustache have been moved from core user to core library:
  * user/templates/local/participantsfilter/filterrow.mustache → lib/templates/datafilter/filter_row.mustache
  * user/templates/local/participantsfilter/filtertype.mustache → lib/templates/datafilter/filter_type.mustache
  * user/templates/local/participantsfilter/filtertypes.mustache → lib/templates/datafilter/filter_types.mustache
  * user/templates/local/participantsfilter/autocomplete_layout.mustache → lib/templates/datafilter/autocomplete_layout.mustache
  * user/templates/local/participantsfilter/autocomplete_selection.mustache → lib/templates/datafilter/autocomplete_selection.mustache
  * user/templates/local/participantsfilter/autocomplete_selection_items.mustache → lib/templates/datafilter/autocomplete_selection_items.mustache
  Class participant_filter now extends core filter api in core user.

* The unified_filter function has been finally deprecated and cannot be used anymore
* The class \core_user\output\unified_filter has been finally deprecated and removed

=== 4.0 ===

* External function core_user_external::update_users() will now fail on a per user basis. Previously if one user
  update failed all users in the operation would fail.
* External function core_user_external::update_users() now returns an error code and message to why a user update
  action failed.
* New method `core_user\fields::get_sql_fullname` for retrieving user fullname format in SQL statement
* The `profile_get_custom_field_data_by_shortname` method now accepts an optional parameter to determine whether
  to use case-sensitive matching of the profile field shortname or not (default true)

=== 3.11 ===

* Added new core_user/form_user_selector JS module that can be used as the 'ajax' handler for the autocomplete form
  element implementing the user selector.
* Added new external function core_user_external::search_identity(). The main purpose of this external function is to
  provide data for asynchronous user selectors and similar widgets. It allows to search users matching the given query
  in their name or other available identity fields.

=== 3.9 ===

* The unified filter has been replaced by the participants filter. The following have therefore been deprecated:
  * Library functions:
    * user_get_participants_sql
    * user_get_total_participants
    * user_get_participants
  * Unified filter renderer (core_user_renderer::unified_filter)
  * Unified filter renderable (\core_user\output\unified_filter)
  * Unified filter JavaScript (core_user/unified_filter.js and core_user/unified_filter_datasource.js)
  * Unified filter template (unified_filter.mustache)

=== 3.6 ===

* The following functions have been finally deprecated and can not be used anymore:
  * useredit_update_picture()
* core_user_external::update_user_preferences() now allows to unset existing preferences values.
  If the preference value field is not set, the preference will be unset.
