=== 4.5 Onwards ===

This file has been replaced by UPGRADING.md. See MDL-81125 for further information.

===
This files describes API changes in /webservice/*
information provided here is intended especially for developers.

This information is intended for authors of webservices, not people writing webservice clients.

=== 4.2 ===

* External function core_webservice_external::get_site_info() does not throw exceptions for missing components anymore.

=== 4.1 ===

* The XMLRPC webservice (protocol) has been completely removed. It's now available in the plugins directory.

=== 4.0 ===

* User access related exceptions have been changed to use the moodle_exception class instead of the
  generic webservice_access_exception, the main reason for this change is to allow clients to
  implement some code logic against an access error.

=== 3.11 ===

* The method webservice::get_user_capabilities() is deprecated now without a replacement. It has been used
  internally only to populate the list of missing capabilities. That functionality has been improved so that
  it no longer needs this standalone method.

=== 3.10 ===

* The class externallib_advanced_testcase, used in unit tests, has a new function called "configure_filters" to easily configure filters for external functions testing.

=== 3.8 ===

* Ajax calls can now specify a cache key. This allows for better caching capabilities on servers. If a cache key
  is passed and the web service call does not require the user to be logged in we will attempt to use GET for the
  request. This allows for things like proxy caching on URLs. The cache key must be changed if we do not want to
  retrieve what has been cached and want to perform the request again.
* External function core_webservice_external::get_site_info() now returns the user private access key "userprivateaccesskey".
  This key could be used for fetching files via the tokenpluginfile.php script instead webservice/pluginfile.php to avoid
  multiple GET requests that include the WS token as a visible parameter.
* External function core_webservice_external::get_site_info() now returns a new field "userissiteadmin" indicating if
  the current user is a site administrator.

=== 3.7 ===

* External function core_webservice_external::get_site_info() now returns the current site theme (for the user).

=== 3.4 ===

* External function core_webservice_external::get_site_info() now returns the calendar type used in the site and
  by the user in the sitecalendartype and usercalendartype fields.
* Implementations of forms for test clients now must follow naming schema: WSFUNCTIONNAME_testclient_form
  The old naming schema WSFUNCTIONNAME_form caused conflicts with existing classes.
  New class webservice_test_client_base_form can be used as a base class for such forms.

=== 3.2 ===

* webservice->get_external_functions now returns the external function list ordered by name ASC.
* The filearea optional parameter has been removed from webservice/upload.php.
  Since Moodle 3.1 all the uploads go to the draft area.
* external_format_text() function: component, filearea and itemid are now optional parameters.
  In some contexts those parameteres are not necessary because is not required to do a file rewrite via
  file_rewrite_pluginfile_urls.
* External function get_site_info now returns the site course ID. This new field is marked as VALUE_OPTIONAL for backwards compatibility.
* A new field "privatetoken" has been added to the "external_tokens" table.
  This private token must be safely stored (or not stored at all) by the client because it will be used in places where a request
  must be double-checked.
  This token should not be passed via GET paramaters and it must be transmitted only via https.
  This token is generated only in login/token.php after the user credential has been confirmed. It can't be generated by admins.

=== 3.1 ===

* The xmlrpc backend has changed, Zend_XmlRpc has been dropped and there might be slight differences in
  responses. Fault strings that were generated by Zend_XmlRpc_XXX_Exception exceptions (i.e. 'Method
  "[methodname]" does not exist') are no longer used which may display a different error message depending
  on the string returned by the getMessage() method of the thrown exception.
* The xmlrpc server is no longer enabled when the Mobile service is activated.
* Support for the AMF protocol has been dropped completely.
* As Zend Framework has been removed, the webservice_zend_* classes have also been removed.
* Zend_SOAP has been dropped. The native PHP SoapClient and SoapServer classes are now being used instead. WSDL is now
  generated by the new class webservice_soap_wsdl. For fault strings, a different error message might be shown depending
  on the string returned by the getMessage() method of the thrown exception.
* With Zend_SOAP dropped, moodle_zend_soap_server is now also deprecated.
* As mentioned in the 2.9 notes, deprecated web service functions have now been removed.
* Since our new XML-RPC server implementation does not support introspection, it is critical that all clients send
  parameters in the correct order.
* File uploading to the user private file area via the webservice/upload.php script is not supported anymore.
  Only uploads to the draft area are allowed.

=== 3.0 ===

* WS protocols webservice/myprotocol:use capabilities were defined with a high riskbitmask value
  when the fact that a user has that capability does not imply any risk, but other capabilities
  that the user may have do. If your ws protocol does not imply and risk by itself, you can remove the
  riskbitmask from your $capabilities array in webservice/myprotocol/db/access.php
* New function for formatting external strings: external_format_strings, it should be used as a replacement of format_string in
  external functions.
  All the occurrences of format_strings have been replaced with this new function.

=== 2.9 ===

* The deprecated functions can not be added to services anymore and
  a debugging message for developers is triggered when viewing an existing
  services using them. It is recommended to replace calls to the deprecated
  functions for calls to the proposed replacements. If you are using a moodle
  mobile app fork, it is recommended to update your customisations on top of
  the latest moodle mobile app version.

  The web services functions that will be finally deprecated in the next
  moodle version are:
    - moodle_course_create_courses
    - moodle_course_get_courses
    - moodle_enrol_get_enrolled_users
    - moodle_enrol_get_users_courses
    - moodle_enrol_manual_enrol_users
    - moodle_file_get_files
    - moodle_file_upload
    - moodle_group_add_groupmembers
    - moodle_group_create_groups
    - moodle_group_delete_groupmembers
    - moodle_group_delete_groups
    - moodle_group_get_course_groups
    - moodle_group_get_groupmembers
    - moodle_group_get_groups
    - moodle_message_send_instantmessages
    - moodle_notes_create_notes
    - moodle_role_assign
    - moodle_role_unassign
    - moodle_user_create_users
    - moodle_user_delete_users
    - moodle_user_get_course_participants_by_id
    - moodle_user_get_users_by_courseid
    - moodle_user_get_users_by_id
    - moodle_user_update_users
    - core_grade_get_definitions
    - core_user_get_users_by_id
    - moodle_webservice_get_siteinfo

* External function core_webservice_external::get_site_info now returns additional optional fields:
   - advancedfeatures: Array listing Moodle advanced features and if enabled or not.
   - usercanmanageownfiles: Whether the my files option is disabled.
   - userquota: User storage quota.
   - usermaxuploadfilesize: Files upload size limit.


=== 2.7 ===

* All webservice server.php and simpleserver.php scripts must define('WS_SERVER', true)
  before including config.php file.


=== 2.6 ===

* webservice/upload.php
Accepts 2 new post parameters to allow uploading of files to a users draft area.
 - filearea should be either 'private' (default) or 'draft'
 - itemid unused if the filearea is 'private', for 'draft' it can be the id of a previously
   created draft area - or 0 which will generate a new draft area for the files.

